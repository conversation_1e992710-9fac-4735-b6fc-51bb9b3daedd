﻿@inherits InteractiveBaseComponent
@if (IsLoading)
{
    <MudProgressLinear Indeterminate="true" />
    @if (!IsLoading && _response is null || _response?.Items.Count == 0)
    {
        <MudSkeleton Width="60%"
                     Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
        <MudSkeleton Width="60%"
                     Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
        <MudSkeleton Width="60%"
                     Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
        <MudSkeleton Width="60%"
                     Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
        <MudSkeleton Width="60%"
                     Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
    }
}

@if (!IsLoading && _response is not null && _response.IsSuccess)
{
    <MudStack Spacing="16">
        @foreach (var item in _response.Items)
        {
            <MudStack @key="item.Id">
                <MudText Typo="Typo.subtitle1"
                         Color="Color.Primary"><b>@item.Name</b></MudText>
                <CameraPublicAccessComponent AccessId="item.Id" />
            </MudStack>
        }
        @if (_response.Items.Count == 0)
        {
            <MudText>Нет публичных ссылок</MudText>
        }
    </MudStack>
}