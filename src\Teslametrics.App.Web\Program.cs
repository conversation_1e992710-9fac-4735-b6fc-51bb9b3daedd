using FluentValidation;
using Microsoft.AspNetCore.Localization;
using Orleans.Configuration;
using Serilog;
using System.Net;
using Teslametrics.App.Web.Behaviors;
using Teslametrics.App.Web.Middleware;
using Teslametrics.App.Web.Services.Authentication;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.App.Web.Services.BlzEventSystem;
using Teslametrics.App.Web.Services.BrowserTime;
using Teslametrics.App.Web.Services.Cookies;
using Teslametrics.App.Web.Services.MudBlazor;
using Teslametrics.App.Web.Services.OpenTelemetry;
using Teslametrics.App.Web.Services.UserDevice;
using Teslametrics.App.Web.Services.UserSession;
using Teslametrics.Core.Domain;
using Teslametrics.Core.Services.DomainEventBus;
using Teslametrics.Core.Services.FileStorage;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Core.Services.TransactionManager;
using Teslametrics.Shared;

namespace Teslametrics.App.Web;

public class Program
{
    public static void Main(string[] args)
    {
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

        environment = ValidateEnvironment(environment);

        var config = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{environment}.json", optional: true, reloadOnChange: true)
            .Build();

        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(config)
            .CreateBootstrapLogger();

        AppDomain currentDomain = AppDomain.CurrentDomain;
        currentDomain.UnhandledException += new UnhandledExceptionEventHandler((object sender, UnhandledExceptionEventArgs args) =>
        {
            Log.Logger.Error((Exception)args.ExceptionObject, "Unhandled exception");
        });

        try
        {
            Log.Information($"Starting application in {environment} environment...");

            var builder = WebApplication.CreateBuilder(args);

            builder.Host.UseSerilog();

            builder.Services.AddLocalization();

            builder.Services.AddMediatR(cfg =>
            {
                cfg.RegisterServicesFromAssembly(typeof(Program).Assembly);
                cfg.AddOpenBehavior(typeof(ExceptionBehavior<,>));
                cfg.AddOpenBehavior(typeof(ResilenceBehavior<,>));
            });

            builder.Services.AddValidatorsFromAssembly(typeof(Program).Assembly);

            DomainModule.Install(builder.Services);

            PostgresDomainPersistenceModule.Install(builder.Services, builder.Configuration);

            AuthenticationModule.Install(builder.Services);

            AuthorizationModule.Install(builder.Services);

            OutboxModule.Install(builder.Services, true);

            DomainEventBusModule.Install(builder.Services);

            TransactionManagerModule.Install(builder.Services);

            SessionModule.Install(builder.Services);

            UserDeviceModule.Install(builder.Services);

            MinioFileStorageModule.Install(builder.Services, builder.Configuration);

            OpenTelemetryModule.Install(builder);

            CookieStorageAccessorModule.Install(builder.Services);

            MudBlazorModule.Install(builder.Services);

            BrowserTimeModule.Install(builder.Services);

            builder.UseOrleansClient(clientBuilder =>
            {
                if (environment == "Local")
                {
                    clientBuilder.UseLocalhostClustering();
                }
                else
                {
                    var hostname = builder.Configuration.GetValue("Orleans:HostName", "media-server");
                    var hostEntry = Dns.GetHostEntry(hostname);
                    var ip = hostEntry.AddressList[0];

                    var port = builder.Configuration.GetValue("Orleans:Port", 30000);
                    clientBuilder.UseStaticClustering(new IPEndPoint(ip, port));
                }

                clientBuilder.Configure<ClusterOptions>(options =>
                {
                    options.ClusterId = "MediaServerCluster";
                    options.ServiceId = "MediaServer";
                });

                clientBuilder.AddLiveStreams(StreamNames.CameraEventLiveStream);
                clientBuilder.AddLiveStreams(StreamNames.VideoLiveStream);
            });

            builder.Services.AddDatabaseDeveloperPageExceptionFilter();

            builder.Services.AddHttpContextAccessor();

            #region Blazor
            builder.Services.AddRazorComponents()
                .AddInteractiveServerComponents();

            builder.Services.AddScoped<IBlzEventSystem, DefaultBlzEventSystem>();

            builder.Services.AddBlazorDownloadFile();

            #endregion
            builder.Services.AddAntiforgery(options =>
            {
                options.Cookie.HttpOnly = true;
                options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
                options.SuppressXFrameOptionsHeader = false;
            });

            builder.Services.AddControllers();
            builder.Services.AddSignalR(o =>
            {
                o.MaximumReceiveMessageSize = 1024 * 1024 * 10; // 10 MB
                o.KeepAliveInterval = TimeSpan.FromSeconds(15); // ping the proxy
                o.ClientTimeoutInterval = TimeSpan.FromSeconds(60); // client waits this long for a ping
            })
            .AddMessagePackProtocol(options =>
            {
                // Настройка MessagePack для эффективной передачи бинарных данных
                options.SerializerOptions = MessagePack.MessagePackSerializerOptions.Standard;
            })
            .AddHubOptions<Hubs.VideoHub>(options =>
            {
                options.EnableDetailedErrors = environment == "Local"; // TODO: Удалить после тестов
            });

            var app = builder.Build();

            // Локализация
            string[] supportedCultures = ["ru-RU"];//, "en-US"
            var russianCulture = new System.Globalization.CultureInfo("ru-RU");

            // Принудительно устанавливаем русскую локаль как стандартную
            System.Globalization.CultureInfo.DefaultThreadCurrentCulture = russianCulture;
            System.Globalization.CultureInfo.DefaultThreadCurrentUICulture = russianCulture;

            var localizationOptions = new RequestLocalizationOptions()
                .SetDefaultCulture(supportedCultures[0])
                .AddSupportedCultures(supportedCultures)
                .AddSupportedUICultures(supportedCultures);
            //System.Globalization.CultureInfo.DefaultThreadCurrentCulture = new System.Globalization.CultureInfo(supportedCultures[0]);

            // Настраиваем провайдеры определения культуры, чтобы принудительно использовать русскую локаль
            localizationOptions.RequestCultureProviders.Clear();
            localizationOptions.RequestCultureProviders.Add(new CustomRequestCultureProvider(context => Task.FromResult<ProviderCultureResult?>(new("ru-RU"))));

            app.UseRequestLocalization(localizationOptions);

            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment())
            {
                app.UseMigrationsEndPoint();
            }
            else
            {
                app.UseExceptionHandler("/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            //app.UseHttpsRedirection();

            //TODO нужен ли этот заголовок для всего приложения или только для плеера?
            //Зачем нам разрешать встраивать всё приложение в iframe?
            app.Use(async (context, next) =>
            {
                context.Response.Headers["X-Frame-Options"] = "ALLOW-FROM *";
                context.Response.Headers["Content-Security-Policy"] = "frame-ancestors *";
                await next();
            });

            // 1. Отдача статических файлов: wwwroot, _content/ и RCL — встроено
            app.MapStaticAssets();

            // 2. Отдача статический файлов (.js) не ассоциированных ни с одним компонентом, лежащих в /components
            var jsOnly = new Microsoft.AspNetCore.StaticFiles.FileExtensionContentTypeProvider(mapping: new Dictionary<string, string>() { { ".js", "application/javascript" } });
            app.UseWhen(
                context => context.Request.Path.HasValue && context.Request.Path.StartsWithSegments("/components") && context.Request.Path.Value.EndsWith(".js"),
                branch =>
                {
                    branch.UseStaticFiles(new StaticFileOptions
                    {
                        FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(Path.Combine(app.Environment.ContentRootPath, "Components")),
                        RequestPath = "/components",
                        ContentTypeProvider = jsOnly,
                        ServeUnknownFileTypes = false
                    });
                });

            if (!builder.Environment.IsProduction() && !builder.Environment.IsDevelopment())
            {
                builder.WebHost.UseStaticWebAssets();
            }

            app.UseAntiforgery();
            app.UseMiddleware<CustomAuthenticationMiddleware>();
            app.UseAuthorization();

            #region Blazor
            app.MapRazorComponents<App>()
                .AddInteractiveServerRenderMode()
                .WithStaticAssets();
            #endregion
            app.UseStatusCodePagesWithRedirects("/Error");
            app.MapControllerRoute("default", "{controller=Home}/{action=Index}/{id?}");
            app.MapPrometheusScrapingEndpoint();

            // Регистрируем хаб для MSE плеера
            app.MapHub<Hubs.VideoHub>("/videoHub");

            UserDeviceModule.Use(app);
            MudBlazorModule.Use(app);

            app.Run();
        }
        catch (Exception ex) when (ex is not HostAbortedException && ex.Source != "Microsoft.EntityFrameworkCore.Design")
        {
            Log.Fatal(ex, "Host terminated unexpectedly!");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static string ValidateEnvironment(string? environment)
    {
        if (string.IsNullOrEmpty(environment))
        {
            throw new ArgumentOutOfRangeException(nameof(environment), "Environment variable ASPNETCORE_ENVIRONMENT is not set");
        }

        return environment switch
        {
            "Local" => environment,
            "Development" => environment,
            "Perftest" => environment,
            "Caviardev" => environment,
            "Stage" => environment,
            "Tesla" => environment,
            "Production" => environment,
            _ => throw new ArgumentOutOfRangeException(nameof(environment))
        };
    }
}