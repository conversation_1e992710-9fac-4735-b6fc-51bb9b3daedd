@inherits InteractiveBaseComponent

<MudText Typo="Typo.h2"
		 Class="mb-8">
	Требуется смена пароля
</MudText>

<MudStack Spacing="6">
	<MudForm Model="@PwdChangeModel"
			 Validation="_validator.ValidateValue"
			 @bind-IsValid="_isValid"
			 class="mud-width-full"
			 Spacing="6"
			 @ref="_formRef">
		<PasswordFieldComponent Value="@PwdChangeModel.NewPassword"
								ValueChanged="@OnPwdChange"
								For="() => PwdChangeModel.NewPassword"
								Immediate="true"
								HelperText="Введите новый пароль"
								Placeholder="*********"
								Required="true"
								Margin="Margin.Dense"
								ShrinkLabel="false"
								Variant="Variant.Outlined"
								@ref="_pwdFieldRef" />
		<PasswordFieldComponent Value="@PwdChangeModel.NewPasswordVerify"
								ValueChanged="@OnPwdConfirmChange"
								For="() => PwdChangeModel.NewPasswordVerify"
								Immediate="true"
								Margin="Margin.Dense"
								HelperText="Повторите новый пароль"
								Placeholder="*********"
								Required="true"
								Variant="Variant.Outlined"
								ShrinkLabel="false"
								@ref="_pwdConfirmFieldRef" />
	</MudForm>
	<MudButton OnClick="SubmitAsync"
			   Variant="Variant.Filled"
			   Color="Color.Primary"
			   Disabled="@(!_isValid)">Сменить пароль</MudButton>
</MudStack>