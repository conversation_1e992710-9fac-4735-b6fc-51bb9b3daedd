@using Teslametrics.App.Web.Components.Diagrams.Models
@inherits BaseNodeWidget<BasePolygonNodeModel>
@implements IDisposable
<svg class="@(isActive ? "base_polygon_node node overflow-visible active" : "base_polygon_node node overflow-visible")"
	 data-id="@Node.Id"
	 id="@Node.Id"
	 width="@($"{Math.Round(Node.Size?.Width ?? 0)}px")"
	 height="@($"{Math.Round(Node.Size?.Height ?? 0)}px")">
	<path d="@CreateRoundedPolygonPath(4)"
		  class="polygon" />

	@if (isActive)
	{
		@foreach (var vertex in Node.VertexPoints)
		{
			<VertexRenderer @key="vertex"
							Vertex="vertex"
							SelectedColor="var(--mud-palette-warning)"
							Color="var(--mud-palette-primary)" />
		}
	}
</svg>