using MudBlazor;
using MudBlazor.Utilities;
using MudBlazor.Services;
using MudBlazor.Extensions;
using MudBlazor.Extensions.Options;

namespace Teslametrics.App.Web.Services.MudBlazor;

public static class MudBlazorModule
{
    public static void Install(IServiceCollection services)
    {
        // services.AddMudServices(config =>
        // {
        //     config.SnackbarConfiguration.PreventDuplicates = true;
        //     config.SnackbarConfiguration.PositionClass = Defaults.Classes.Position.BottomCenter;
        //     config.SnackbarConfiguration.ShowCloseIcon = true;

        //     config.SnackbarConfiguration.SnackbarVariant = Variant.Outlined;
        //     config.SnackbarConfiguration.VisibleStateDuration = 4000;
        //     config.SnackbarConfiguration.HideTransitionDuration = 500;
        //     config.SnackbarConfiguration.ShowTransitionDuration = 500;
        // });

        MudTheme CustomTheme = new()
        {
            Typography = new()
            {
                Default = new DefaultTypography()
                {
                    FontFamily = ["Inter", "sans-serif"]
                },
                H1 = new H1Typography()
                {
                    FontFamily = ["Inter", "sans-serif"],
                    FontSize = "2.5rem",      // 40px
                    FontWeight = "400",
                    LineHeight = "1.1",
                },
                H2 = new H2Typography()
                {
                    FontFamily = ["Inter", "sans-serif"],
                    FontSize = "2.25rem",       // 36px
                    FontWeight = "400",
                    LineHeight = "1.1", // 1.3,
                },
                H3 = new H3Typography()
                {
                    FontFamily = ["Inter", "sans-serif"],
                    FontSize = "1.5rem",    // 24px
                    FontWeight = "500",
                    LineHeight = "1.2",
                },
                H4 = new H4Typography()
                {
                    FontFamily = ["Inter", "sans-serif"],
                    FontSize = "1.4375rem",   // 23px
                    FontWeight = "700",
                    LineHeight = "1.45",
                },
                H5 = new H5Typography()
                {
                    FontFamily = ["Inter", "sans-serif"],
                    FontSize = "1rem",        // 16px
                    FontWeight = "400",
                    LineHeight = "1.1",
                },
                H6 = new H6Typography()
                {
                    FontFamily = ["Inter", "sans-serif"],
                    FontSize = "0.875rem",    // 14px
                    FontWeight = "400",
                    LineHeight = "1.1",
                },
                Body1 = new Body1Typography()
                {
                    FontFamily = ["Inter", "sans-serif"],
                    FontSize = "0.75rem",     // 12px
                    FontWeight = "500",//400,
                    LineHeight = "1.1",
                },
                Body2 = new Body2Typography
                {
                    FontFamily = ["Inter", "sans-serif"],
                    FontSize = "0.625rem",    // 10px
                    FontWeight = "400", //350,
                    LineHeight = "1.1" //1.5,
                },
                Subtitle1 = new Subtitle1Typography
                {
                    FontFamily = ["Inter", "sans-serif"],
                    FontSize = "1rem",        // 16px
                    FontWeight = "500",
                    LineHeight = "1.1",
                },
                Subtitle2 = new Subtitle2Typography
                {
                    FontFamily = ["Inter", "sans-serif"],
                    FontSize = "0.875rem",    // 14px
                    LineHeight = "1.1",
                    FontWeight = "500",
                },
                Caption = new CaptionTypography()
                {
                    FontFamily = ["Inter", "sans-serif"],
                    FontWeight = "400",
                    FontSize = "0.75rem",     // 12px
                    LineHeight = "1.1",
                    LetterSpacing = "0"
                },
                // Input = new Input()
                // {
                //     FontFamily = ["Inter", "sans-serif"],
                //     FontWeight = "500",
                //     FontSize = "12px",
                //     LineHeight = 1.1,
                //     LetterSpacing = "0"
                // }
            },
            LayoutProperties = new LayoutProperties()
            {
                AppbarHeight = "55px"
            },
            PaletteLight = new PaletteLight()
            {
                Primary = new MudColor("#669198"),
                Secondary = new MudColor("#B7E8D9"),
                Background = new MudColor("#F0F2F8"),
                BackgroundGray = new MudColor("#F6F8FA"),
                Surface = new MudColor("#FFFFFF"),
                // DrawerBackground = new MudColor("#00363F"),
                Error = new MudColor("#FF1323"),
                TextDisabled = new MudColor("#B2C2D2"),
                Success = new MudColor("#00A367"),
            },
            PaletteDark = new PaletteDark()
            {
                Primary = new MudColor("#669198"),
                Secondary = new MudColor("#B7E8D9"),
                TableStriped = new MudColor("rgba(255,255,255,0.05)"),
                Background = new MudColor("#191A18"),
                DrawerBackground = new MudColor("#1F221F"),
                Surface = new MudColor("#1F221F"),
                Error = new MudColor("#FF1323"),
                Success = new MudColor("#00A367")
            }
        };

        services.AddSingleton(CustomTheme);

        //services.AddMudExtensions();
        services.AddMudServicesWithExtensions(
            (MudServicesConfiguration config) =>
        {
            config.SnackbarConfiguration.PreventDuplicates = true;
            config.SnackbarConfiguration.PositionClass = Defaults.Classes.Position.BottomCenter;
            config.SnackbarConfiguration.ShowCloseIcon = true;

            config.SnackbarConfiguration.SnackbarVariant = Variant.Outlined;
            config.SnackbarConfiguration.VisibleStateDuration = 4000;
            config.SnackbarConfiguration.HideTransitionDuration = 500;
            config.SnackbarConfiguration.ShowTransitionDuration = 500;
        },
        (MudExConfiguration exConfig) =>
            exConfig.WithDefaultDialogOptions(c =>
            {
                c.AnimationDuration = TimeSpan.FromMilliseconds(100);
                c.Animation = AnimationType.Perspective3d;
            })
        );
    }

    public static void Use(IApplicationBuilder app)
    {
        app.Use(MudExWebApp.MudExMiddleware);
    }
}
