using Microsoft.AspNetCore.Components.Server.Circuits;

namespace Teslametrics.App.Web.Services.UserDevice;

/// <summary>
/// CircuitHandler для автоматической инициализации информации об устройстве пользователя
/// при установке Blazor SignalR соединения
/// </summary>
public class UserDeviceCircuitHandler : CircuitHandler
{
    private readonly IUserDeviceService _userDeviceService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<UserDeviceCircuitHandler> _logger;

    public UserDeviceCircuitHandler(
        IUserDeviceService userDeviceService,
        IHttpContextAccessor httpContextAccessor,
        ILogger<UserDeviceCircuitHandler> logger)
    {
        _userDeviceService = userDeviceService;
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
    }

    public override Task OnCircuitOpenedAsync(Circuit circuit, CancellationToken cancellationToken)
    {
        try
        {
            // Получаем HttpContext при установке circuit и инициализируем сервис
            var httpContext = _httpContextAccessor.HttpContext;

            if (httpContext != null)
            {
                _userDeviceService.InitializeFromHttpContext(httpContext);
                _logger.LogDebug("Инициализирована информация об устройстве для circuit {CircuitId}", circuit.Id);
            }
            else
            {
                _logger.LogWarning("HttpContext не найден для circuit {CircuitId}", circuit.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Ошибка при инициализации информации об устройстве для circuit {CircuitId}", circuit.Id);
        }

        return base.OnCircuitOpenedAsync(circuit, cancellationToken);
    }
}
