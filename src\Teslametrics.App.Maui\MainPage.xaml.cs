﻿using Microsoft.Maui.Controls.PlatformConfiguration;
using System.Net.NetworkInformation;

namespace Teslametrics.App.Maui
{
    public partial class MainPage : ContentPage
    {
        private const string MainUrl = "http://188.244.39.50:1337";

        public MainPage()
        {
            InitializeComponent();

            // Configure WebView for mobile-style scrollbars
            ConfigureWebView();

            webView.Navigated += (_, e) =>
            {
                if (e.Result == WebNavigationResult.Failure)
                {
                    // No internet - show fallback screen
                    ShowFallbackContent("No internet connection detected");
                }
                else
                {
                    ShowWebView();
                }
            };

            // Check connection only at startup
            //CheckInitialConnection();
        }

        private void ConfigureWebView()
        {
#if ANDROID
            if (webView.Handler?.PlatformView is Android.Webkit.WebView aView)
            {
                // 1. Полоса внутри-overlay, а не в отдельном «желобе»
                aView.ScrollBarStyle = Android.Views.ScrollbarStyles.InsideOverlay;

                aView.VerticalScrollBarEnabled   = false;   // выключаем вертикальную
                aView.HorizontalScrollBarEnabled = false;   // и горизонтальную
                aView.ScrollbarFadingEnabled     = true;    // на всякий случай
            }
#elif IOS || MACCATALYST
            if (webView.Handler?.PlatformView is WebKit.WKWebView iView)
            {
                iView.ScrollView.ShowsVerticalScrollIndicator = false;
                iView.ScrollView.ShowsHorizontalScrollIndicator = false;
            }
#endif
            // Subscribe to NavigationCompleted to inject CSS for hiding scrollbars
            webView.Navigated += OnWebViewNavigated;

            // Also subscribe to Loaded event for immediate application
            webView.Loaded += OnWebViewLoaded;
        }

        // Т.к. html сам рисует полосы прокрутки вырезаем их
        private async void OnWebViewLoaded(object? sender, EventArgs e)
        {
            // Apply initial scrollbar hiding as soon as WebView is loaded
            await Task.Delay(100); // Small delay to ensure WebView is ready

            try
            {
                var initialScript = @"
                    (function() {
                        if (document.body) {
                            document.body.style.scrollbarWidth = 'none';
                            document.body.style.msOverflowStyle = 'none';
                        }
                        if (document.documentElement) {
                            document.documentElement.style.scrollbarWidth = 'none';
                            document.documentElement.style.msOverflowStyle = 'none';
                        }
                    })();
                ";

                await webView.EvaluateJavaScriptAsync(initialScript);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to apply initial scrollbar hiding: {ex.Message}");
            }
        }

        private async void OnWebViewNavigated(object? sender, WebNavigatedEventArgs e)
        {
            if (e.Result == WebNavigationResult.Success)
            {
                // Inject CSS to hide scrollbars and make them mobile-style
                var cssScript = @"
                    (function() {
                        // Remove any existing mobile scrollbar styles
                        var existingStyle = document.getElementById('mobile-scrollbar-style');
                        if (existingStyle) {
                            existingStyle.remove();
                        }

                        var style = document.createElement('style');
                        style.id = 'mobile-scrollbar-style';
                        style.type = 'text/css';
                        style.innerHTML = `
                            /* Hide scrollbars for WebKit browsers (Safari, Chrome, WebView) */
                            ::-webkit-scrollbar {
                                width: 0px !important;
                                height: 0px !important;
                                background: transparent !important;
                                display: none !important;
                            }

                            ::-webkit-scrollbar-track {
                                background: transparent !important;
                                display: none !important;
                            }

                            ::-webkit-scrollbar-thumb {
                                background: transparent !important;
                                display: none !important;
                            }

                            ::-webkit-scrollbar-corner {
                                background: transparent !important;
                                display: none !important;
                            }

                            /* Hide scrollbars for Firefox */
                            html {
                                scrollbar-width: none !important;
                                -ms-overflow-style: none !important;
                            }

                            /* Ensure smooth scrolling behavior */
                            html, body {
                                scroll-behavior: smooth !important;
                                -webkit-overflow-scrolling: touch !important;
                                overflow-x: hidden !important;
                            }

                            /* Hide scrollbars for all elements */
                            * {
                                scrollbar-width: none !important;
                                -ms-overflow-style: none !important;
                            }

                            *::-webkit-scrollbar {
                                width: 0px !important;
                                height: 0px !important;
                                background: transparent !important;
                                display: none !important;
                            }

                            /* Specific targeting for common scrollable containers */
                            div, section, article, main, aside, nav {
                                scrollbar-width: none !important;
                                -ms-overflow-style: none !important;
                            }

                            div::-webkit-scrollbar,
                            section::-webkit-scrollbar,
                            article::-webkit-scrollbar,
                            main::-webkit-scrollbar,
                            aside::-webkit-scrollbar,
                            nav::-webkit-scrollbar {
                                display: none !important;
                                width: 0px !important;
                                height: 0px !important;
                            }
                        `;
                        document.head.appendChild(style);

                        // Also apply styles directly to body for immediate effect
                        document.body.style.scrollbarWidth = 'none';
                        document.body.style.msOverflowStyle = 'none';
                        document.documentElement.style.scrollbarWidth = 'none';
                        document.documentElement.style.msOverflowStyle = 'none';
                    })();
                ";

                try
                {
                    await webView.EvaluateJavaScriptAsync(cssScript);
                }
                catch (Exception ex)
                {
                    // Log error but don't break functionality
                    System.Diagnostics.Debug.WriteLine($"Failed to inject scrollbar CSS: {ex.Message}");
                }
            }
        }

        private async void CheckInitialConnection()
        {
            if (await IsConnectedToInternet())
            {
                // Internet available - let WebView load normally
                ShowWebView();
            }
            else
            {
                // No internet - show fallback screen
                ShowFallbackContent("No internet connection detected");
            }
        }

        private static async Task<bool> IsConnectedToInternet()
        {
            try
            {
                // Try to ping Google DNS
                using var ping = new Ping();
                var reply = await ping.SendPingAsync("*******", 5000);
                return reply.Status == IPStatus.Success;
            }
            catch
            {
                return false;
            }
        }

        private void OnRetryClicked(object? sender, EventArgs e)
        {
            // Show loading state
            retryButton.IsVisible = false;
            loadingIndicator.IsVisible = true;
            statusLabel.Text = "Checking connection...";
            webView.Reload();

            // Check connection again
            // if (await IsConnectedToInternet())
            //{
            //    ShowWebView();
            //}
            //else
            //{
            //    // Still no connection
            //    retryButton.IsVisible = true;
            //    loadingIndicator.IsVisible = false;
            //    statusLabel.Text = "Still no internet connection. Tap retry to try again.";
            // }
        }

        private void ShowWebView()
        {
            webView.IsVisible = true;
            fallbackContent.IsVisible = false;
        }

        private void ShowFallbackContent(string message)
        {
            webView.IsVisible = false;
            fallbackContent.IsVisible = true;
            errorMessage.Text = $"Unable to connect to Teslametrics server. {message}. Please check your internet connection and try again.";
            retryButton.IsVisible = true;
            loadingIndicator.IsVisible = false;
            statusLabel.Text = "Tap retry to attempt reconnection";
        }
    }
}
