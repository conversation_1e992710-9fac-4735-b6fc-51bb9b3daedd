using System.ServiceModel;
using System.ServiceModel.Channels;
using System.Net;
using System.Xml;
using System.Reactive.Subjects;
using System.Reactive.Linq;
using Teslametrics.MediaServer.OnvifProxy;

namespace Teslametrics.MediaServer.Onvif;

/// <summary>
/// Wrapper class for working with ONVIF events
/// </summary>
public class OnvifEventClient : IDisposable
{
    private readonly string _deviceUrl;
    private readonly string _username;
    private readonly string _password;
    private readonly CustomBinding _binding;
    private EventPortTypeClient? _eventClient;
    private PullPointSubscriptionClient? _pullPointClient;
    private SubscriptionManagerClient? _subscriptionManagerClient;
    private string? _subscriptionUrl;
    private CancellationTokenSource? _pollingCts;
    private Task? _pollingTask;
    private bool _isPolling;

    // Subject for implementing IObservable
    private Subject<OnvifMotionEvent> _motionEventsSubject = new Subject<OnvifMotionEvent>();

    /// <summary>
    /// Observable for motion events
    /// </summary>
    public IObservable<OnvifMotionEvent> MotionEvents => _motionEventsSubject.AsObservable();

    /// <summary>
    /// Creates a new Subject and updates subscribers
    /// </summary>
    private void RecreateSubject()
    {
        // Create a new Subject to continue operation
        var newSubject = new Subject<OnvifMotionEvent>();

        // Atomically replace the old Subject with the new one
        Interlocked.Exchange(ref _motionEventsSubject, newSubject);
    }

    /// <summary>
    /// Constructor for the ONVIF event client
    /// </summary>
    /// <param name="host">Host or IP address of the device</param>
    /// <param name="port">Device port</param>
    /// <param name="username">Username for authentication</param>
    /// <param name="password">Password for authentication</param>
    /// <param name="useSsl">Use SSL (HTTPS)</param>
    public OnvifEventClient(string host, int port, string username, string password, bool useSsl = false)
    {
        string protocol = useSsl ? "https" : "http";
        _deviceUrl = $"{protocol}://{host}:{port}/onvif/events";
        _username = username;
        _password = password;

        // Create binding for SOAP 1.2
        _binding = new CustomBinding();
        var textBindingElement = new TextMessageEncodingBindingElement(MessageVersion.Soap12WSAddressing10, System.Text.Encoding.UTF8);
        var httpBindingElement = new HttpTransportBindingElement
        {
            AuthenticationScheme = AuthenticationSchemes.Digest,
            MaxReceivedMessageSize = 1000000
        };

        _binding.Elements.Add(textBindingElement);
        _binding.Elements.Add(httpBindingElement);

        // Initialize client
        InitializeEventClient();
    }

    /// <summary>
    /// Initialize event client
    /// </summary>
    private void InitializeEventClient()
    {
        var endpoint = new EndpointAddress(_deviceUrl);
        _eventClient = new EventPortTypeClient(_binding, endpoint);

        // Set credentials for authentication
        var factory = _eventClient.ChannelFactory as ChannelFactory;
        if (factory != null)
        {
            factory.Credentials.HttpDigest.ClientCredential = new NetworkCredential(_username, _password);
        }
    }

    /// <summary>
    /// Get ONVIF device capabilities
    /// </summary>
    /// <returns>Object with device capabilities</returns>
    public async Task<Capabilities> GetCapabilitiesAsync()
    {
        if (_eventClient == null)
        {
            throw new InvalidOperationException("Event client is not initialized");
        }
        return await _eventClient.GetServiceCapabilitiesAsync();
    }

    /// <summary>
    /// Create subscription for motion events
    /// </summary>
    /// <returns>Subscription URL</returns>
    public async Task<string> CreateSubscriptionAsync()
    {
        if (_eventClient == null)
        {
            throw new InvalidOperationException("Event client is not initialized");
        }

        var createPullPointSubscriptionRequest = new CreatePullPointSubscriptionRequest();

        // Create XML document for filter
        XmlDocument doc = new XmlDocument();

        // Create root element TopicExpression
        XmlElement topicExpressionElement = doc.CreateElement("wsnt", "TopicExpression", "http://docs.oasis-open.org/wsn/b-2");

        // Set attributes for TopicExpression
        topicExpressionElement.SetAttribute("Dialect", "http://www.onvif.org/ver10/tev/topicExpression/ConcreteSet");

        // Set value to filter only motion events
        // topicExpressionElement.InnerText = "tns1:VideoSource/MotionAlarm tns1:RuleEngine/CellMotionDetector/Motion";
        topicExpressionElement.InnerText = "tns1:VideoSource/MotionAlarm";

        // Create filter and add TopicExpression to it
        createPullPointSubscriptionRequest.Filter = new FilterType
        {
            Any = new XmlElement[] { topicExpressionElement }
        };

        var response = await _eventClient.CreatePullPointSubscriptionAsync(createPullPointSubscriptionRequest);

        // Get original subscription reference
        string subscriptionUrl = response.SubscriptionReference.Address.Value;

        // Fix subscription reference by replacing internal IP with external one
        Uri originalUri = new Uri(subscriptionUrl);
        string path = originalUri.PathAndQuery;

        // Extract host from original device URL
        Uri deviceUri = new Uri(_deviceUrl);
        string externalHost = $"{deviceUri.Host}:{deviceUri.Port}";

        // Create fixed reference
        _subscriptionUrl = $"http://{externalHost}{path}";

        // Initialize clients for working with subscription
        InitializePullPointClient();
        InitializeSubscriptionManagerClient();

        return _subscriptionUrl;
    }

    /// <summary>
    /// Initialize client for receiving events
    /// </summary>
    private void InitializePullPointClient()
    {
        if (_subscriptionUrl == null)
        {
            throw new InvalidOperationException("Subscription URL is not initialized");
        }

        _pullPointClient = new PullPointSubscriptionClient(_binding, new EndpointAddress(_subscriptionUrl));
        var pullPointFactory = _pullPointClient.ChannelFactory as ChannelFactory;
        if (pullPointFactory != null)
        {
            pullPointFactory.Credentials.HttpDigest.ClientCredential = new NetworkCredential(_username, _password);
        }
    }

    /// <summary>
    /// Initialize client for subscription management
    /// </summary>
    private void InitializeSubscriptionManagerClient()
    {
        if (_subscriptionUrl == null)
        {
            throw new InvalidOperationException("Subscription URL is not initialized");
        }

        _subscriptionManagerClient = new SubscriptionManagerClient(_binding, new EndpointAddress(_subscriptionUrl));
        var subscriptionManagerFactory = _subscriptionManagerClient.ChannelFactory as ChannelFactory;
        if (subscriptionManagerFactory != null)
        {
            subscriptionManagerFactory.Credentials.HttpDigest.ClientCredential = new NetworkCredential(_username, _password);
        }
    }

    /// <summary>
    /// Get messages from the event queue
    /// </summary>
    /// <param name="timeout">Timeout in seconds</param>
    /// <param name="messageLimit">Maximum number of messages</param>
    /// <returns>List of messages</returns>
    public async Task<NotificationMessageHolderType[]> PullMessagesAsync(int timeout = 5, int messageLimit = 10)
    {
        if (_pullPointClient == null)
        {
            throw new InvalidOperationException("PullPoint client is not initialized");
        }

        var pullMessagesRequest = new PullMessagesRequest
        {
            Timeout = $"PT{timeout}S", // Timeout format according to ISO 8601
            MessageLimit = messageLimit
        };

        var response = await _pullPointClient.PullMessagesAsync(pullMessagesRequest);
        return response.NotificationMessage;
    }

    /// <summary>
    /// Cancel subscription to events
    /// </summary>
    public async Task UnsubscribeAsync()
    {
        // First stop event polling if it's running
        await StopEventPollingAsync();

        if (_subscriptionManagerClient != null)
        {
            var unsubscribe = new Unsubscribe();
            await _subscriptionManagerClient.UnsubscribeAsync(unsubscribe);
        }
    }

    /// <summary>
    /// Start receiving events
    /// </summary>
    /// <param name="pollingInterval">Polling interval in milliseconds</param>
    /// <returns>Task representing the asynchronous operation</returns>
    public async Task StartEventPollingAsync(int pollingInterval = 1000)
    {
        if (_pullPointClient == null)
        {
            throw new InvalidOperationException("PullPoint client is not initialized");
        }

        _isPolling = true;
        _pollingCts = new CancellationTokenSource();

        // Start polling task in a separate thread
        _pollingTask = Task.Run(async () =>
        {
            try
            {
                while (!_pollingCts.Token.IsCancellationRequested)
                {
                    try
                    {
                        var messages = await PullMessagesAsync();
                        if (messages != null && messages.Length > 0)
                        {
                            // Raise event for each received message
                            foreach (var message in messages)
                            {
                                try
                                {
                                    // Get event time from device
                                    var deviceTime = DateTimeOffset.Now;
                                    var isMotion = false;

                                    if (message.Message != null)
                                    {
                                        // Get event time from device
                                        var utcTimeAttr = message.Message.GetAttribute("UtcTime", "");
                                        if (!string.IsNullOrEmpty(utcTimeAttr) && DateTimeOffset.TryParse(utcTimeAttr, out var parsedTime))
                                        {
                                            deviceTime = parsedTime;
                                        }

                                        // Determine event topic
                                        string? topic = null;
                                        if (message.Topic?.Any != null && message.Topic.Any.Length > 0)
                                        {
                                            topic = message.Topic.Any[0]?.InnerText;
                                        }

                                        // Extract motion state
                                        if (topic == "tns1:VideoSource/MotionAlarm")
                                        {
                                            // Find SimpleItem element with name State
                                            var dataNodes = message.Message.GetElementsByTagName("tt:Data");
                                            if (dataNodes.Count > 0 && dataNodes[0] is XmlElement dataElement)
                                            {
                                                var simpleItems = dataElement.GetElementsByTagName("tt:SimpleItem");
                                                foreach (var itemNode in simpleItems)
                                                {
                                                    if (itemNode is XmlElement item &&
                                                        item.GetAttribute("Name") == "State")
                                                    {
                                                        isMotion = item.GetAttribute("Value").ToLower() == "true";
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                        else if (topic == "tns1:RuleEngine/CellMotionDetector/Motion")
                                        {
                                            // Find SimpleItem element with name IsMotion
                                            var dataNodes = message.Message.GetElementsByTagName("tt:Data");
                                            if (dataNodes.Count > 0 && dataNodes[0] is XmlElement dataElement)
                                            {
                                                var simpleItems = dataElement.GetElementsByTagName("tt:SimpleItem");
                                                foreach (var itemNode in simpleItems)
                                                {
                                                    if (itemNode is XmlElement item &&
                                                        item.GetAttribute("Name") == "IsMotion")
                                                    {
                                                        isMotion = item.GetAttribute("Value").ToLower() == "true";
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    // Create event arguments object
                                    var motionEvent = new OnvifMotionEvent(deviceTime, isMotion);

                                    // Send event through Subject
                                    _motionEventsSubject.OnNext(motionEvent);
                                }
                                catch (Exception ex)
                                {
                                    // Send error through OnError
                                    _motionEventsSubject.OnError(ex);

                                    // Create new Subject to continue operation
                                    RecreateSubject();

                                    // Break current iteration
                                    break;
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Send error through OnError
                        _motionEventsSubject.OnError(ex);

                        // Create new Subject to continue operation
                        RecreateSubject();
                    }

                    // Wait specified time before next request
                    await Task.Delay(pollingInterval, _pollingCts.Token);
                }
            }
            catch (OperationCanceledException)
            {
                // Normal termination when task is cancelled
            }
            catch (Exception ex)
            {
                // Send error through OnError
                _motionEventsSubject.OnError(ex);

                // Create new Subject to continue operation
                RecreateSubject();
            }
            finally
            {
                _isPolling = false;
            }
        });

        await Task.CompletedTask;
    }

    /// <summary>
    /// Stop receiving events
    /// </summary>
    /// <returns>Task representing the asynchronous operation</returns>
    public async Task StopEventPollingAsync()
    {
        if (_pollingCts == null || _pollingTask == null || !_isPolling)
        {
            return;
        }

        try
        {
            // Cancel token and wait for task to complete
            _pollingCts.Cancel();
            await _pollingTask;
        }
        catch (Exception)
        {
            // Ignore errors during stopping
        }
        finally
        {
            _pollingCts.Dispose();
            _pollingCts = null;
            _pollingTask = null;
            _isPolling = false;
        }
    }

    /// <summary>
    /// Releases resources
    /// </summary>
    public void Dispose()
    {
        _pollingCts?.Cancel();
        _pollingCts?.Dispose();
        _pollingCts = null;
        _pollingTask = null;
        _isPolling = false;

        // Complete Subject
        _motionEventsSubject.OnCompleted();
        _motionEventsSubject.Dispose();
    }
}
