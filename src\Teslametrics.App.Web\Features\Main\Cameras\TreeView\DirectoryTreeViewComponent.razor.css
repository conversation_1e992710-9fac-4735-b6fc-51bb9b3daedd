﻿::deep .tree .dropable
{
	border: 3px dotted var(--mud-palette-success);
}

::deep .tree .no_dropable
{
	border: 3px dotted var(--mud-palette-error);
}

::deep.sidebar
{
	display: grid;
	grid-template-rows: 1fr auto auto auto ;
	overflow: auto;
	padding-bottom: 4px;
}

::deep .badge_position
{
	padding-bottom: 4px !important;
	bottom: -3px !important;
	top: auto !important;
}

::deep .mud-treeview-item-content {
    border-radius: 8px;
    margin-top: 4px;
    margin-bottom: 4px;
    border: 1px solid transparent;
}

::deep .mud-treeview-item-content:hover {
    border: 1px solid var(--color-stroke-blue);}

::deep .mud-treeview-item:is(.mud-treeview-item-selected) > .mud-treeview-item-content:not(:hover),
::deep .mud-treeview-item-content:is(.mud-treeview-item-selected):not(:hover) {
    background-color: var(--color-bg-3) !important;
}

::deep .chip:not(.active) {
    background-color: var(--color-bg-3) !important;
}
