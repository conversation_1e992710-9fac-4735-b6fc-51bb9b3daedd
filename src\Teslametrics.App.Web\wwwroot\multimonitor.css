﻿:root {
	--color-neutral-10: #17181A;
	--color-neutral-40: #5C6166;
	--color-neutral-70: #A1AAB2;
	--color-neutral-80: #B8C2CC;
	--color-neutral-90: #CFDAE5;

	--color-stroke-blue: #B2C2D2;
	--color-stroke-stroke:#E2E4E6;

	--color-primary-600: #4D7F87;
	--color-primary-700: #336D76;
	--color-primary-800: #1A5A65;
	--color-primary-900: #00363F;

	--color-secondary-900: #3C9177;
	--color-secondary-800: #93DDC6;

	--color-text-placeholder: #B2C2D2;
	--color-text-button-disabled: #B2C2D2;
	--color-text-input-tile: #5C6166;

	--color-system-err-bg: #FFF3F4;
	--color-bg-blue: #ECF4FC;
    --color-bg-3: #F0F2F8;
	--color-bg-2: #F6F8FA;
}


.bg_2 {
	background-color: var(--color-bg-2);
}

.bg_3,
.bg3 {
	background-color: var(--color-bg-3);
}

@media (min-width: 600px) {
	:root {
		--mud-appbar-height: 80px !important;
	}
}

@media (max-width: 600px) {
	:root {
		--mud-appbar-height: 64px !important;
	}
}

.color_neutral_40 {
	color: var(--color-neutral-40);
}

.color_neutral_70 {
	color: var(--color-neutral-70);
}

.color_neutral_90 {
	color: var(--color-neutral-90);
}

.bg_light_blue_surface {
	background-color: rgba(230, 233, 244, 1) !important;
}

.mud_theme_dark .bg_light_blue_surface {
	background-color: #27272f !important;
}

/* .mud-input-outlined .mud-input-control-input-container,
.mud-input-outlined-with-label .mud-input-control-input-container {
	height: 40px;
} */

.mud_theme_dark .bg_light_blue_surface {
	background-color: #27272f !important;
}

.mud_theme_light .mud-typography-subtitle2 {
	color: var(--color-neutral-40);
}

.br_8 {
	border-radius: 8px !important;
}

.br_12 {
	border-radius: 12px !important;
}

.br_16 {
	border-radius: 16px !important;
}

.br_20 {
    border-radius: 20px !important;
}

:root:has(* .mud_theme_dark) {
    --color-bg-3: #191919;
	--color-bg-2: rgb(49 49 49);
	--color-system-err-bg: rgb(240 0 0 / 10%);
	--color-system-success-bg: #273E35;

	--color-neutral-80: #848484;

	--color-stroke-stroke:#5f6973;
}