﻿@inherits InteractiveBaseComponent
<div class="d_contents">
    <MudStack Spacing="5"
              Row="true"
              Class="relative incidents">
        @if (IsLoading && _response is not null && _response.IsSuccess)
        {
            <MudProgressLinear Class="progress absolute" />
        }
        @if (IsLoading && (_response is null || !_response.IsSuccess))
        {
            <IncidentsCardSkeleton />
            <IncidentsCardSkeleton />
            <IncidentsCardSkeleton />
        }
        @if (_response is not null && _response.IsSuccess)
        {
            <MudPaper Elevation="0"
                      Outlined="false"
                      Class="mud-width-full pa-6 d-flex flex-column gap-3 br_16">
                <div class="d-flex flex-row gap-3 align-center">
                    <MudText Typo="Typo.subtitle1"
                             Class="card_title">Холодильники</MudText>
                    <div class="pa-2 bg_2 d-flex align-center br_8">@_response.FridgeIncidents!.Total</div>
                </div>
                <MudStack Spacing="3"
                          Row="true">
                    <MudText Typo="Typo.subtitle2"
                             Class="dotted primary pa-2">@(_response.FridgeIncidents!.Total - _response.FridgeIncidents.Incidents) в норме
                    </MudText>
                    <MudText Typo="Typo.subtitle2"
                             Class="dotted error pa-2">@_response.FridgeIncidents!.Incidents с происшествием</MudText>
                </MudStack>
            </MudPaper>
            <MudPaper Elevation="0"
                      Outlined="false"
                      Class="mud-width-full pa-6 d-flex flex-column gap-3 br_16">
                <div class="d-flex flex-row gap-3 align-center">
                    <MudText Typo="Typo.subtitle1"
                             Class="card_title">Происшествия</MudText>
                    <div class="pa-2 bg_2 d-flex align-center br_8">@_response.TotalIncidents!.Total</div>
                </div>
                <MudStack Spacing="3"
                          Row="true">
                    <MudText Typo="Typo.subtitle2"
                             Class="dotted primary pa-2">@(_response.TotalIncidents!.Total - _response.TotalIncidents!.Current) завершено</MudText>
                    <MudText Typo="Typo.subtitle2"
                             Class="dotted error pa-2">@_response.TotalIncidents!.Current активных</MudText>
                </MudStack>
            </MudPaper>
            <MudPaper Elevation="0"
                      Outlined="false"
                      Class="mud-width-full pa-6 d-flex flex-column gap-3 br_16">
                <div class="d-flex flex-row gap-3 align-center">
                    <MudText Typo="Typo.subtitle1"
                             Class="card_title">Камеры</MudText>
                    <div class="pa-2 bg_2 d-flex align-center br_8">@_response.CameraIncidents!.Total</div>
                </div>
                <MudStack Spacing="3"
                          Row="true">
                    <MudText Typo="Typo.subtitle2"
                             Class="dotted primary pa-2">@(_response.CameraIncidents!.Total - _response.CameraIncidents.Incidents) на связи
                    </MudText>
                    <MudText Typo="Typo.subtitle2"
                             Class="dotted error pa-2">@_response.CameraIncidents!.Incidents не на связи</MudText>
                </MudStack>
            </MudPaper>
        }
    </MudStack>
</div>