using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.CameraViews.Drawer.Create.GridItem.Preview;

public partial class Preview
{
    private GetCameraUseCase.Response? _response;

    [Parameter]
    public Guid? CameraId { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        await FetchAsync();
    }

    private async Task FetchAsync()
    {
        if (CameraId is null)
        {
            _response = null;
            return;
        }

        await SetLoadingAsync(true);

        try
        {
            _response = await ScopeFactory.MediatorSend(new GetCameraUseCase.Query(CameraId.Value));
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, exc.Message);
            Snackbar.Add("Не удалось получить камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
        }

        await SetLoadingAsync(false);

        switch (_response?.Result)
        {
            case GetCameraUseCase.Result.Success:
                break;
            case GetCameraUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации данных", MudBlazor.Severity.Error);
                break;
            case GetCameraUseCase.Result.CameraNotFound:
                Snackbar.Add("Камера не найдена", MudBlazor.Severity.Error);
                break;
            case GetCameraUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(Preview), nameof(GetCameraUseCase));
                Snackbar.Add($"Не удалось получить камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(Preview), nameof(GetCameraUseCase), _response?.Result);
                Snackbar.Add($"Не удалось получить камеру из-за ошибки: {_response?.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
}
