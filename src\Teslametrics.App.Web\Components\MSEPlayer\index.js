import { createPlayer } from "./Core/index.js";

/**
 * Хранилище активных плееров.
 *
 * Ключом служит `cameraId`, значением — объект ядра («core»), возвращаемый
 * `createPlayer`. Карта не экспортируется наружу и предназначена только для
 * внутреннего управления жизненным циклом плееров.
 *
 * @type {Map<string|number, import('@company/player-core').Core>}
 * @private
 */
const players = new Map();

/**
 * Инициализирует (или переинициализирует) видеоплеер для указанной камеры.
 *
 * > **Blazor:** метод вызывается из C#-кода как `initializePlayer(element, id, mode, startIso, endIso, plugins)`.
 *
 * Если для той же камеры уже существует активный плеер, он будет корректно
 * остановлен и удалён перед созданием нового экземпляра.
 *
 * @param {HTMLVideoElement}   videoRef  DOM-элемент `<video>` (или контейнер),
 *                                       где будет отрисовываться видео.
 * @param {string|number}      cameraId  Уникальный идентификатор камеры —
 *                                       используется как ключ в `players`.
 * @param {'live'|'vod'|'replay'} mode   Режим воспроизведения:
 *                                       * `live`   — прямая трансляция;
 *                                       * `vod`    — видео по запросу;
 *                                       * `replay` — воспроизведение
 *                                                   архива по времени.
 * @param {string}             start     Время начала в формате ISO 8601
 *                                       (UTC) — используется в режимах
 *                                       `vod` и `replay`.
 * @param {string}             end       Время окончания в формате ISO 8601
 *                                       (UTC). В режиме `live` можно
 *                                       передать `null`.
 * @param {Array<Function>}    [plugins=[]] Массив плагинов (middleware),
 *                                          совместимых с `createPlayer`,
 *                                          применяемых при создании плеера.
 *
 * @returns {void}
 *
 * @example
 * // Прямая трансляция:
 * initializePlayer(videoEl, 42, 'live');
 *
 * @example
 * // Запрос архива за указанный интервал:
 * initializePlayer(videoEl, 42, 'replay',
 *                  '2025-05-22T09:30:00Z',
 *                  '2025-05-22T10:00:00Z');
 */
export function initializePlayer(
  videoRef,
  cameraId,
  mode,
  start,
  end,
  plugins = []
) {
  if (players.has(cameraId)) stopPlayer(cameraId);

  const { core } = createPlayer(
    videoRef,
    { cameraId, mode, start, end },
    plugins
  );
  players.set(cameraId, core);
}

/* ------------------------------------------------------------------ */
/* Обёртки, вызываемые из C# через JS-Interop                         */
/* ------------------------------------------------------------------ */

/**
 * Приостанавливает воспроизведение потока.
 *
 * @param {string|number} id  Идентификатор камеры (`cameraId`).
 * @returns {void}
 */
export const pauseStream = (id) => players.get(id)?.pause();

/**
 * Возобновляет воспроизведение после паузы.
 *
 * @param {string|number} id  Идентификатор камеры.
 * @returns {void}
 */
export const resumeStream = (id) => players.get(id)?.resume();

/**
 * Перематывает воспроизведение к текущему «живому» моменту.
 *
 * @param {string|number} id  Идентификатор камеры.
 * @returns {void}
 */
export const seekToLive = (id) => players.get(id)?.live();

/**
 * Полностью останавливает поток и удаляет плеер из карты `players`.
 *
 * > Важно: после вызова функции `stopStream` плеер считается уничтоженным.
 * > Для повторного воспроизведения необходимо заново вызвать
 * > `initializePlayer`.
 *
 * @param {string|number} id  Идентификатор камеры.
 * @returns {void}
 */
export const stopStream = (id) => {
  players.get(id)?.dispose();
  players.delete(id);
};

/**
 * Возвращает абсолютное (серверное) время кадра, который
 * в данный момент отображается плеером.
 *
 * @param {string|number} id  Идентификатор камеры.
 * @returns {string|undefined} Абсолютное время в формате ISO 8601 UTC
 *                             или `undefined`, если плеер не найден.
 */
export const getAbsTime = (id) => players.get(id)?.pipe?.currentAbsTime;
