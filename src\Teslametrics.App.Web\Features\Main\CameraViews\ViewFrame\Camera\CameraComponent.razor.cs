
using System.Reactive;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;
using Teslametrics.MediaServer.Orleans.Camera;

namespace Teslametrics.App.Web.Features.Main.CameraViews.ViewFrame.Camera;

public partial class CameraComponent
{
	private CameraStatus _cameraStatus;

	[Parameter]
	[EditorRequired]
	public Guid? CameraId { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }

	private bool _subscribing;
	private DateTime _lastRefreshTime;

	private SubscribeCameraUseCase.Response? _subscriptionResult;
	private GetCameraUseCase.Response? _viewResponse;
	private List<GetViewUseCase.Response.Cell> _cells = new List<GetViewUseCase.Response.Cell>();

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();

		await FetchAsync();
		await SubscribeAsync();
	}

	private async Task FetchAsync()
	{
		if (CameraId is null || CameraId == Guid.Empty) return;
		await SetLoadingAsync(true);
		try
		{
			_viewResponse = await ScopeFactory.MediatorSend(new GetCameraUseCase.Query(CameraId.Value));
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, exc.Message);
			_viewResponse = null;
			Snackbar.Add($"Не удалось получить камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
		}

		await SetLoadingAsync(false);
		if (_viewResponse is null) return;
		switch (_viewResponse.Result)
		{
			case GetCameraUseCase.Result.Success:
				_lastRefreshTime = DateTime.Now;
				_cameraStatus = _viewResponse.CameraStatus;
				break;
			case GetCameraUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации данных", MudBlazor.Severity.Error);
				break;
			case GetCameraUseCase.Result.CameraNotFound:
				Snackbar.Add("Камера не найдена", MudBlazor.Severity.Error);
				break;
			case GetCameraUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraComponent), nameof(GetCameraUseCase));
				Snackbar.Add($"Не удалось получить камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraComponent), nameof(GetCameraUseCase), _viewResponse.Result);
				Snackbar.Add($"Не удалось получить камеру из-за ошибки: {_viewResponse.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private async Task SubscribeAsync()
	{
		Unsubscribe();

		if (_viewResponse is null || CameraId is null || CameraId == Guid.Empty) return;

		await SetSubscribingAsync(true);
		try
		{
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), CameraId.Value));
		}
		catch (Exception ex)
		{
			_subscriptionResult = null;
			Snackbar.Add($"Не удалось получить подписку на события камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetSubscribingAsync(false);
		if (_subscriptionResult is null) return;
		switch (_subscriptionResult.Result)
		{
			case SubscribeCameraUseCase.Result.Success:
				CompositeDisposable.Add(_subscriptionResult.Subscription!);
				break;
			case SubscribeCameraUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
				break;
			case SubscribeCameraUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraComponent), nameof(SubscribeCameraUseCase));
				Snackbar.Add($"Не удалось получить подписку на события камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraComponent), nameof(SubscribeCameraUseCase), _subscriptionResult.Result);
				Snackbar.Add($"Не удалось получить подписку на события камеры из-за ошибки: {_subscriptionResult.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});
	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		await FetchAsync();
		await UpdateViewAsync();
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]
}
