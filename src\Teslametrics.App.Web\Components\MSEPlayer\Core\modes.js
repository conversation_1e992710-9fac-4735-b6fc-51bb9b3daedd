export const MODES = {
  live: () => ({
    wsPath: "/videoHub",
    mime: "video/mp2t;codecs=avc1.640028",
    keep: 30,
    autoreconnect: true,
  }),
  point: ({ start }) => ({
    wsPath: "/videoHub?start=" + start.toISOString(),
    mime: "video/mp2t;codecs=avc1.640028",
    keep: 60,
    autoreconnect: true,
  }),
  range: ({ start, end }) => ({
    wsPath: `/videoHub?start=${start.toISOString()}&end=${end.toISOString()}`,
    mime: "video/mp4;codecs=avc1.640028",
    keep: 120,
    autoreconnect: true,
  }),
};
