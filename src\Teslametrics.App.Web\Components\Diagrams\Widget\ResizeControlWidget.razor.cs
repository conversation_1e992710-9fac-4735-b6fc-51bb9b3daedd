using Blazor.Diagrams.Core.Models.Base;
using Blazor.Diagrams.Extensions;
using Microsoft.AspNetCore.Components;
using System.Text;
using Teslametrics.App.Web.Components.Diagrams.Controls;
using Teslametrics.App.Web.Components.Diagrams.Models;

namespace Teslametrics.App.Web.Components.Diagrams.Widget;

public partial class ResizeControlWidget
{
    private string DefaultClass = "default_node_resizer";
    private StringBuilder _classesBuilder => new StringBuilder(DefaultClass)
        .AppendIf(" cursor-nw-resize", Control.Position == ResizerControlPosition.TopLeft || Control.Position == ResizerControlPosition.BottomRight)
        .AppendIf(" cursor-nesw-resize", Control.Position == ResizerControlPosition.TopRight || Control.Position == ResizerControlPosition.BottomLeft);

    [Parameter]
    public ResizeControl Control { get; set; } = null!;

    [Parameter]
    public Model Model { get; set; } = null!;
}

