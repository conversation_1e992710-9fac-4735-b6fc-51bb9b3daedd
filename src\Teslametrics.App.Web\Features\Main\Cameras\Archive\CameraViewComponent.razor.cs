using System.Reactive;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.JSInterop;
using MudBlazor;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Cameras.Archive;

public partial class CameraViewComponent : IAsyncDisposable
{
	private IJSObjectReference? _playerJsModule;

	private Player? _playerRef;
	private (DateTime Start, DateTime End)? _currentTimeLineRange;
	private bool _subscribing;

	private SubscribeFolderUseCase.Response? _subscriptionResult;
	private GetCameraUseCase.Response? _camera;
	private GetAvailableArchivesUseCase.Response? _archive;

	[Inject]
	public IJSRuntime Js { get; set; } = null!;

	[Inject]
	private NavigationManager NavigationManager { get; set; } = default!;

	[Parameter]
	public Guid CameraId { get; set; }

	[SupplyParameterFromQuery(Name = "StartFrom")]
	public DateTime? StartFrom { get; set; }

	public async ValueTask DisposeAsync()
	{
		try
		{
			if (_playerJsModule is not null)
			{
				await _playerJsModule.DisposeAsync();
			}
		}
		catch (JSDisconnectedException) // А всё. Соединения нету. https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
		{
		}
		catch (Exception ex)
		{
			Logger.LogError(ex, ex.Message);
		}

		GC.SuppressFinalize(this);
	}

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();

		await FetchAsync();
		await SubscribeAsync();
	}

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		await base.OnAfterRenderAsync(firstRender);
		if (firstRender)
		{
			try
			{
				_playerJsModule = await Js.InvokeAsync<IJSObjectReference>("import", "./Features/Main/Cameras/Archive/CameraViewComponent.razor.js");
			}
			catch (JSDisconnectedException) // А всё. Соединения нету. https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
			{
			}
			catch (Exception ex)
			{
				Snackbar.Add("Не удалось получить камеру.");
				Logger.LogError(ex, ex.Message);
			}
		}
	}

	private async Task FetchAsync()
	{
		if (_camera is null) await SetLoadingAsync(true);
		try
		{
			_camera = await ScopeFactory.MediatorSend(new GetCameraUseCase.Query(CameraId));
		}
		catch (Exception ex)
		{
			_camera = null;
			Logger.LogError(ex, ex.Message);
			Snackbar.Add("Не удалось получить архивируемую камеру.");
		}

		await SetLoadingAsync(false);

		if (_camera is null) return;

		switch (_camera.Result)
		{
			case GetCameraUseCase.Result.Success:
				await SubscribeAsync();
				await FetchMonthAvaliableDates(DateTime.UtcNow);
				if (_archive is not null && _archive.AvailableDates.Contains(DateTime.UtcNow))
				{
					StartFrom = DateTime.UtcNow;
					OnFilterParamsChanged();
				}
				break;
			case GetCameraUseCase.Result.CameraNotFound:
				Snackbar.Add("Не удалось получить архивируемую камеру. Возможно камера уже архивирована.", Severity.Warning);
				Cancel();
				break;
			case GetCameraUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось получить архивируемую камеру из-за ошибки валидации.", Severity.Error);
				Cancel();
				break;
			case GetCameraUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraViewComponent), nameof(GetCameraUseCase));
				Snackbar.Add("Не удалось получить архивируемую камеру из-за непредвиденной ошибки.", Severity.Error);
				Cancel();
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraViewComponent), nameof(GetCameraUseCase), _camera.Result);
				Snackbar.Add("Не удалось получить архивируемую камеру из-за непредвиденной ошибки.", Severity.Error);
				Cancel();
				break;
		}
	}

	private async Task FetchMonthAvaliableDates(DateTime? dateTime)
	{
		if (dateTime is null) return;
		try
		{
			_archive = await ScopeFactory.MediatorSend(new GetAvailableArchivesUseCase.Query(CameraId, dateTime.Value));
		}
		catch (Exception ex)
		{
			_archive = null;
			Logger.LogError(ex, ex.Message);
			Snackbar.Add("Не удалось получить архвив.");
		}

		await SetLoadingAsync(false);
		if (_archive is null) return;

		switch (_archive.Result)
		{
			case GetAvailableArchivesUseCase.Result.Success:
				break;
			case GetAvailableArchivesUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось получить список дат с архивами.", Severity.Error);
				break;
			case GetAvailableArchivesUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraViewComponent), nameof(GetAvailableArchivesUseCase));
				Snackbar.Add("Не удалось получить список дат с архивами из-за непредвиденной ошибки.", Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraViewComponent), nameof(GetAvailableArchivesUseCase), _archive.Result);
				Snackbar.Add("Не удалось получить список дат с архивами из-за непредвиденной ошибки.", Severity.Error);
				break;
		}
	}


	private async Task SubscribeAsync()
	{
		await SetSubscribingAsync(true);
		Unsubscribe();
		try
		{
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeFolderUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), CameraId));
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить подписку на события камеры из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		await SetSubscribingAsync(false);

		if (_subscriptionResult is null) return;
		switch (_subscriptionResult.Result)
		{
			case SubscribeFolderUseCase.Result.Success:
				CompositeDisposable.Add(_subscriptionResult.Subscription!);
				StateHasChanged();
				break;
			case SubscribeFolderUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
				break;
			case SubscribeFolderUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraViewComponent), nameof(SubscribeFolderUseCase));
				Snackbar.Add($"Не удалось получить подписку на события камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraViewComponent), nameof(SubscribeFolderUseCase), _subscriptionResult.Result);
				Snackbar.Add($"Не удалось получить подписку на события камеры из-за ошибки: {_subscriptionResult.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	private bool IsDateDisabledFunc(DateTime dt)
	{
		if (dt > DateTime.UtcNow) return true;

		if (_archive is null) return true;

		if (!_archive.AvailableDates.Contains(dt)) return true;

		return false;
	}

	#region [Actions]
	private void Cancel()
	{
		Unsubscribe();
		NavigationManager.NavigateTo("/cameras");
	}
	private Task RefreshAsync() => FetchAsync();
	#endregion [Actions]

	#region [Event Handlers]
	private void OnSelectedDateChanged(DateTime? date)
	{
		StartFrom = date;
		OnFilterParamsChanged();
	}

	private void OnFilterParamsChanged()
	{
		var uri = NavigationManager.Uri;
		var uriWithoutQuery = uri.Split('?')[0];
		var queryParameters = new Dictionary<string, string?>();

		if (StartFrom.HasValue)
			queryParameters.Add("StartFrom", StartFrom.Value.ToDateTimeOffsetString());

		var newUri = QueryHelpers.AddQueryString(uriWithoutQuery, queryParameters);
		NavigationManager.NavigateTo(newUri, replace: true);
	}

	private async Task OnEventSelected(DateTime startTime)
	{
		if (_playerRef is not null)
		{
			await _playerRef.SetTimeAsync(startTime);
		}
	}

	private async Task TimeLineChanged((DateTime start, DateTime end) timeLine)
	{
		_currentTimeLineRange = timeLine;
		try
		{
			if (_playerJsModule is not null)
			{
				await _playerJsModule.InvokeVoidAsync("SetVisibleRanges", _currentTimeLineRange.Value.Start, _currentTimeLineRange.Value.End);
			}
		}
		catch (JSDisconnectedException)
		{ }
		catch (Exception ex)
		{
			Logger.LogError(ex, ex.Message);
		}
	}

	private async void OnAppEventHandler(object appEvent)
	{
		switch (appEvent)
		{
			case SubscribeFolderUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeFolderUseCase.DeletedEvent deletedEto:
				Snackbar.Add("Камера была удалена", Severity.Warning);
				Cancel();
				break;

			default:
				Snackbar.Add("Было получено непредвиденное событие.", Severity.Warning);
				await FetchAsync();
				await UpdateViewAsync();
				Logger.LogWarning($"Unexpected event in {nameof(SubscribeFolderUseCase)}: {nameof(appEvent)}");
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]
}
