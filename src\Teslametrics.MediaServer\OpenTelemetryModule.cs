using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;

namespace Teslametrics.MediaServer;

public class OpenTelemetryModule
{
    public static void Install(WebApplicationBuilder builder)
    {
        // var tracingOtlpEndpoint = builder.Configuration["OTLP_ENDPOINT_URL"];
        var otel = builder.Services.AddOpenTelemetry();

        // Configure OpenTelemetry Resources with the application name
        otel.ConfigureResource(resource => resource
            .AddService(serviceName: builder.Environment.ApplicationName));

        // Add Metrics for ASP.NET Core and our custom metrics and export to Prometheus
        otel.WithMetrics(metrics => metrics
            // Metrics provider from OpenTelemetry
            .AddAspNetCoreInstrumentation()
            // Metrics provides by ASP.NET Core in .NET 8
            .AddMeter("Microsoft.AspNetCore.Hosting")
            .AddMeter("Microsoft.AspNetCore.Server.Kestrel")
            // Metrics provided by System.Net libraries
            .AddMeter("System.Net.Http")
            .AddMeter("System.Net.NameResolution")
            .AddMeter("Microsoft.Orleans")
            .AddPrometheusExporter());

        // Add Tracing for ASP.NET Core and our custom ActivitySource and export to Jaeger
        // otel.WithTracing(tracing =>
        // {
        //     tracing.AddAspNetCoreInstrumentation();
        //     tracing.AddHttpClientInstrumentation();
        //     tracing.AddSource("Microsoft.Orleans.Runtime");
        //     tracing.AddSource("Microsoft.Orleans.Application");
        //     if (tracingOtlpEndpoint != null)
        //     {
        //         tracing.AddOtlpExporter(otlpOptions =>
        //         {
        //             otlpOptions.Endpoint = new Uri(tracingOtlpEndpoint);
        //         });
        //     }
        //     else
        //     {
        //         tracing.AddConsoleExporter();
        //     }
        // });
    }
}