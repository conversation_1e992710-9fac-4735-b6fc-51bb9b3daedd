﻿@using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.FridgeNode.Sensors
@inherits BaseNodeComponent<FridgeElement>
<MudMenu AnchorOrigin="Origin.TopCenter"
         TransformOrigin="Origin.TopCenter"
         PositionAtCursor="true"
         PopoverClass="br_8 fridge_menu"
         Modal="false"
         ActivationEvent="@MouseEvent.MouseOver">

    <ActivatorContent>
        <div class="plan_fridge absolute"
             data-id="@Element.Id"
             style="@($"left: {ToCssValue(Element.X)}px; top: {ToCssValue(Element.Y)}px;")"
             @onclick="SelectAsync"
             @onmouseenter="OnMouseEnterAsync">
            <MudIcon Icon="@TeslaIcons.Devices.Fridge"
                     Class="@($"{(IsSelected ? "selected" : "")}")" />
        </div>
    </ActivatorContent>

    <ChildContent>
        <div class="fridge-content">
            @Element.Title
        </div>
        <div class="sensor-grid">
            @foreach (var sensor in _temperature ?? [])
            {
                <SensorDataComponent TopicName="@sensor.Name"
                                     ValueProcessor="TempValueProcessor"
                                     Error="@(sensor.Incident is not null)"
                                     Icon="@TeslaIcons.Sensors.Temperature" />
            }
            @foreach (var item in _humidity ?? [])
            {
                <SensorDataComponent TopicName="@item.Name"
                                     Error="@(item.Incident is not null)"
                                     ValueProcessor="HumidityValueProcessor"
                                     Icon="@TeslaIcons.Sensors.Humidity" />
            }
            @foreach (var item in _door ?? [])
            {
                <SensorDataComponent TopicName="@item.Name"
                                     Error="@(item.Incident is not null)"
                                     ValueProcessor="DoorValueProcessor"
                                     Icon="@TeslaIcons.Sensors.Door" />
            }
            @foreach (var item in _power ?? [])
            {
                <SensorDataComponent TopicName="@item.Name"
                                     Error="@(item.Incident is not null)"
                                     ValueProcessor="PowerValueProcessor"
                                     Icon="@TeslaIcons.Sensors.Power" />
            }
            @foreach (var item in _leak ?? [])
            {
                <SensorDataComponent TopicName="@item.Name"
                                     Error="@(item.Incident is not null)"
                                     ValueProcessor="LeakValueProcessor"
                                     Icon="@TeslaIcons.Sensors.Leak" />
            }
        </div>
    </ChildContent>
</MudMenu>

<style>
    .fridge_menu {
        border: 1px solid var(--color-stroke-stroke);
        box-shadow: rgba(10, 13, 18, 0.08) 0px 12px 16px -4px;
    }
</style>