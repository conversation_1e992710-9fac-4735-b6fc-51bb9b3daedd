using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.DoorOpenings;

public static class GetDoorOpeningUseCase
{
    public record Query(DateTimeOffset DateFrom, DateTimeOffset DateTo, Guid CityId, Guid BuildingId, Guid FloorId, Guid RoomId, Guid FridgeId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<DoorOpeningModel> DoorOpenings { get; init; }
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<DoorOpeningModel> doorOpenings)
        {
            DoorOpenings = doorOpenings;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            DoorOpenings = [];
            Result = result;
        }

        /// <summary>
        /// Представляет количество открытия двери за определенный день
        /// </summary>
        /// <param name="Date">Дата (без времени)</param>
        /// <param name="Count">Количество открытия двери за этот день</param>
        /// <param name="AverageTime">Среднее время открытия двери, мин за день</param>
        public record DoorOpeningModel(DateOnly Date, int Count, float AverageTime);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        PlanNotFound,
        DoorSensorNotFound,
        CityNotFound,
        BuildingNotFound,
        FloorNotFound,
        RoomNotFound,
        FridgeNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(x => x.DateTo)
                .GreaterThan(x => x.DateFrom)
                .WithMessage("DateTo must be greater than DateFrom");

            RuleFor(x => x.CityId)
                .NotEmpty()
                .WithMessage("CityId is required");

            RuleFor(x => x.BuildingId)
                .NotEmpty()
                .WithMessage("BuildingId is required");

            RuleFor(x => x.FloorId)
                .NotEmpty()
                .WithMessage("FloorId is required");

            RuleFor(x => x.RoomId)
                .NotEmpty()
                .WithMessage("RoomId is required");

            RuleFor(x => x.FridgeId)
                .NotEmpty()
                .WithMessage("FridgeId is required");
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            if (!await CheckTableExistsAsync())
            {
                return new Response(Result.PlanNotFound);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Plans.Props.Page)
                .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

            var pageJson = await _dbConnection.ExecuteScalarAsync<string?>(template.RawSql);

            if (string.IsNullOrEmpty(pageJson))
            {
                return new Response(Result.PlanNotFound);
            }

            var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

            var city = page.Cities.FirstOrDefault(c => c.Id == request.CityId);

            if (city is null)
            {
                return new Response(Result.CityNotFound);
            }

            var building = city.Buildings.FirstOrDefault(b => b.Id == request.BuildingId);

            if (building is null)
            {
                return new Response(Result.BuildingNotFound);
            }

            var floor = building.Floors.FirstOrDefault(f => f.Id == request.FloorId);

            if (floor is null)
            {
                return new Response(Result.FloorNotFound);
            }

            var room = floor.Rooms.FirstOrDefault(r => r.Id == request.RoomId);

            if (room is null)
            {
                return new Response(Result.RoomNotFound);
            }

            var fridge = room.Fridges.FirstOrDefault(f => f.Id == request.FridgeId);

            if (fridge is null)
            {
                return new Response(Result.FridgeNotFound);
            }

            var sensor = fridge.Sensors.FirstOrDefault(s => s is DoorModel);

            if (sensor is null)
            {
                return new Response(Result.DoorSensorNotFound);
            }

            var tableName = $"{Db.SensorHistory.Table}_{GuidUtility.Create(Guid.Parse("A4ED63A9-698B-4B11-A0A6-E13118E3C34D"), sensor.Name).ToString("N")}";

            // Получаем последнее состояние двери перед началом запрашиваемого периода
            var lastStateBeforePeriod = await GetLastDoorStateBeforePeriodAsync(tableName, request.DateFrom);

            template = SqlQueryBuilder.Create()
                .Select(Db.SensorHistory.Columns.Timestamp)
                .Select(Db.SensorHistory.Columns.Value)
                .Where(Db.SensorHistory.Columns.Timestamp, ":DateFrom", SqlOperator.GreaterThanOrEqual, new { DateFrom = request.DateFrom.UtcDateTime })
                .Where(Db.SensorHistory.Columns.Timestamp, ":DateTo", SqlOperator.LessThanOrEqual, new { DateTo = request.DateTo.UtcDateTime })
                .Build(QueryType.Standard, tableName, RowSelection.AllRows);

            var history = await _dbConnection.QueryAsync<History>(template.RawSql, template.Parameters);

            // Сортируем данные по времени
            var sortedHistory = history.OrderBy(h => h.TimeStamp).ToList();

            // Находим пары открытие-закрытие и создаем периоды открытия двери
            var doorOpenings = new List<DoorOpeningPeriod>();
            DateTimeOffset? openTime = null;

            // Проверяем, была ли дверь открыта на начало периода
            // Если последнее состояние перед периодом показывает, что дверь была открыта (Value = true),
            // то начинаем отсчет времени с начала запрашиваемого периода
            if (lastStateBeforePeriod?.Value == true)
            {
                openTime = request.DateFrom;
            }

            foreach (var record in sortedHistory)
            {
                if (record.Value && openTime == null)
                {
                    // Дверь открылась
                    openTime = record.TimeStamp;
                }
                else if (!record.Value && openTime != null)
                {
                    // Дверь закрылась - создаем периоды с разбивкой по дням
                    doorOpenings.AddRange(CreateDoorOpeningPeriods(openTime.Value, record.TimeStamp));
                    openTime = null;
                }
            }

            // Если дверь осталась открытой в конце периода, создаем периоды до конца
            if (openTime != null)
            {
                doorOpenings.AddRange(CreateDoorOpeningPeriods(openTime.Value, request.DateTo));
            }

            // Создаем полный диапазон дат для заполнения пропусков (используем offset пользователя)
            var dateRange = new List<DateOnly>();
            var userOffset = request.DateFrom.Offset;
            var currentDate = DateOnly.FromDateTime(request.DateFrom.ToOffset(userOffset).Date);
            var endDate = DateOnly.FromDateTime(request.DateTo.ToOffset(userOffset).Date);

            while (currentDate <= endDate)
            {
                dateRange.Add(currentDate);
                currentDate = currentDate.AddDays(1);
            }

            // Группируем периоды по дням и вычисляем статистику (используем offset пользователя)
            var doorOpeningsByDay = doorOpenings
                .GroupBy(period => period.GetDate(userOffset))
                .ToDictionary(g => g.Key, g => new {
                    Count = g.Count(),
                    AverageTime = g.Average(period => period.DurationInMinutes)
                });

            // Создаем результат для всех дней в диапазоне
            var result = dateRange.Select(date => new Response.DoorOpeningModel(
                date,
                doorOpeningsByDay.TryGetValue(date, out var dayData) ? dayData.Count : 0,
                doorOpeningsByDay.TryGetValue(date, out var dayData2) ? (float)dayData2.AverageTime : 0f
            )).ToList();

            return new Response(result);
        }

        /// <summary>
        /// Создает периоды открытия двери с разбивкой по дням
        /// </summary>
        private static List<DoorOpeningPeriod> CreateDoorOpeningPeriods(DateTimeOffset openTime, DateTimeOffset closeTime)
        {
            var periods = new List<DoorOpeningPeriod>();

            // Используем offset из времени закрытия для определения границ дней
            var userOffset = closeTime.Offset;
            var openDate = DateOnly.FromDateTime(openTime.ToOffset(userOffset).Date);
            var closeDate = DateOnly.FromDateTime(closeTime.ToOffset(userOffset).Date);

            // Если период в пределах одного дня, создаем один период
            if (openDate == closeDate)
            {
                periods.Add(new DoorOpeningPeriod(openTime, closeTime));
                return periods;
            }

            // Разбиваем период по дням
            var currentDate = openDate;
            var currentOpenTime = openTime;

            while (currentDate <= closeDate)
            {
                var isLastDay = currentDate == closeDate;

                var endOfDay = isLastDay
                    ? closeTime
                    : new DateTimeOffset(currentDate.ToDateTime(TimeOnly.MaxValue), userOffset);

                // Создаем период для текущего дня
                periods.Add(new DoorOpeningPeriod(currentOpenTime, endOfDay));

                // Переходим к следующему дню
                currentDate = currentDate.AddDays(1);
                currentOpenTime = new DateTimeOffset(currentDate.ToDateTime(TimeOnly.MinValue), userOffset);
            }

            return periods;
        }

        /// <summary>
        /// Получает последнее состояние двери перед началом запрашиваемого периода
        /// </summary>
        /// <param name="tableName">Имя таблицы с историей сенсора</param>
        /// <param name="periodStart">Начало запрашиваемого периода</param>
        /// <returns>Последнее состояние двери или null, если данных нет</returns>
        private async Task<History?> GetLastDoorStateBeforePeriodAsync(string tableName, DateTimeOffset periodStart)
        {
            try
            {
                // Используем прямой SQL запрос с LIMIT 1, так как SqlQueryBuilder не поддерживает SingleRow
                string sql = $@"
                    SELECT {Db.SensorHistory.Columns.Timestamp}, {Db.SensorHistory.Columns.Value}
                    FROM {tableName}
                    WHERE {Db.SensorHistory.Columns.Timestamp} < @PeriodStart
                    ORDER BY {Db.SensorHistory.Columns.Timestamp} DESC
                    LIMIT 1";

                var lastState = await _dbConnection.QueryFirstOrDefaultAsync<History?>(sql, new { PeriodStart = periodStart.UtcDateTime });
                return lastState;
            }
            catch (Exception)
            {
                // Если таблица не существует или произошла ошибка, возвращаем null
                return null;
            }
        }

        private async Task<bool> CheckTableExistsAsync()
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = Db.Plans.Table });

            return tableExists > 0;
        }
    }
}

public record History(DateTimeOffset TimeStamp, bool Value);

public class DoorOpeningPeriod
{
    public DateTimeOffset OpenTime { get; }
    public DateTimeOffset CloseTime { get; }
    public double DurationInMinutes => (CloseTime - OpenTime).TotalMinutes;

    /// <summary>
    /// Получает дату в указанном часовом поясе (offset)
    /// </summary>
    public DateOnly GetDate(TimeSpan userOffset) => DateOnly.FromDateTime(OpenTime.ToOffset(userOffset).Date);

    public DoorOpeningPeriod(DateTimeOffset openTime, DateTimeOffset closeTime)
    {
        if (closeTime < openTime)
            throw new ArgumentException("Close time cannot be earlier than open time");

        OpenTime = openTime;
        CloseTime = closeTime;
    }
}