<div class="d-flex flex-column gap-2 @(Disabled ? "disabled" : "")">
	<div class="d-flex gap-2 align-center">
		<MudIcon Icon="@Icon"
				 Size="Size.Small"
				 Color="@_titleColor"
				 Class="icon" />
		<MudText Class="card_title"
				 Color="@_titleColor">@Title</MudText>
	</div>
	<MudText Typo="Typo.body1"
			 Class="card_subtitle">@Subtitle</MudText>
</div>

@code {
	private Color _titleColor => Error ? Color.Error : Color.Default;

	[Parameter]
	public bool Error { get; set; } = false;

	[Parameter]
	public bool Disabled { get; set; } = false;

	[Parameter]
	public string Icon { get; set; } = string.Empty;

	[Parameter]
	[EditorRequired]
	public string Title { get; set; } = string.Empty;

	[Parameter]
	[EditorRequired]
	public string Subtitle { get; set; } = string.Empty;
}
