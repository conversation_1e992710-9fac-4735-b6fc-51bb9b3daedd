using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Shared;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.Core.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.CameraViews.Drawer.Create.GridItem.Preview;

public static class GetCameraUseCase
{
    public record Query(Guid Id) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Guid? CameraStreamId { get; init; }

        public CameraStatus CameraStatus { get; init; }

        public bool IsBlocked { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id, Guid? cameraStreamId, CameraStatus cameraStatus, bool isBlocked)
        {
            CameraStreamId = cameraStreamId;
            Id = id;
            CameraStatus = cameraStatus;
            IsBlocked = isBlocked;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Id = Guid.Empty;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        CameraNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.Id).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;
        private readonly IClusterClient _clusterClient;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection,
                       IClusterClient clusterClient)
        {
            _validator = validator;
            _dbConnection = dbConnection;
            _clusterClient = clusterClient;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken = default)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Cameras.Props.Id)
                .Select(Db.Cameras.Props.IsBlocked)
                .Where(Db.Cameras.Props.Id, ":Id", SqlOperator.Equals, new { request.Id })
                .Build(QueryType.Standard, Db.Cameras.Table, RowSelection.AllRows);

            var camera = await _dbConnection.QuerySingleOrDefaultAsync<CameraModel>(template.RawSql, template.Parameters);

            if (camera is null)
            {
                return new Response(Result.CameraNotFound);
            }

            var mediaServerGrain = _clusterClient.GetGrain<IMediaServerGrain>(Guid.Empty);
            var statusResponse = await mediaServerGrain.GetStatusesAsync(new IMediaServerGrain.GetCameraStatusesRequest([request.Id]));
            var status = statusResponse.Statuses[request.Id];

            Guid? cameraStreamId = null;

            if (status != CameraStatus.Stopped)
            {
                var cameraGrain = _clusterClient.GetGrain<ICameraGrain>(request.Id);
                var response = await cameraGrain.GetCameraStreamIdAsync(new ICameraGrain.GetCameraStreamIdRequest(StreamType.View));
                cameraStreamId = response.CameraStreamId;
            }

            return new Response(camera.Id,
                                cameraStreamId,
                                status,
                                camera.IsBlocked);
        }
    }

    public record CameraModel(Guid Id, bool IsBlocked);
}