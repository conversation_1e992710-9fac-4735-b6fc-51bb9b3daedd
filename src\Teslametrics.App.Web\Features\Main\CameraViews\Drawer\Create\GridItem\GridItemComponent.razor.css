.empty_item
{
	aspect-ratio: 16 / 9;
}

::deep .camera_card
{
    background: var(--color-bg-2);
    border-radius: 8px;
    display: grid;
    gap: 8px;
    grid-template-rows: auto 1fr;
}

.preview_container
{
	height: 100%;
    overflow: hidden;
    display: flex;
    align-content: center;
    align-items: center;
    border-radius: 4px;
    background: var(--mud-palette-surface);
}

::deep .mud-autocomplete .mud-input-control>.mud-input-control-input-container
{
    background: var(--mud-palette-surface);
    border-radius: 8px;
}