/* Стили для мобильных устройств */
::deep .bottom_navbar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--mud-palette-surface);
    border-top: 1px solid var(--mud-palette-divider);
    padding: 8px 16px;
    z-index: 1000;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    height: 56px;
}

.mud_theme_dark ::deep .bottom_navbar {
    background: var(--mud-palette-surface);
    border-top: 1px solid var(--mud-palette-divider);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.3);
}
