﻿@inherits InteractiveBaseComponent
<MudAutocomplete T="GetRoomListUseCase.Response.Item"
                 SearchFunc="@SearchAsync"
                 ToStringFunc="@(e => e == null ? null : e.Name)"
                 Value="@_selected"
                 ValueChanged="@ValueChanged"
                 Label="Помещение"
                 Clearable="true"
                 Margin="Margin.Dense"
                 ResetValueOnEmptyText="true"
                 Variant="Variant.Outlined"
                 Disabled="BuildingId is null || CityId is null || FloorId is null" />