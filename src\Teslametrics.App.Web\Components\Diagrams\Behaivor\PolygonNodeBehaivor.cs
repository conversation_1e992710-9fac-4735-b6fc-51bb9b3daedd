using Blazor.Diagrams.Core;
using Blazor.Diagrams.Core.Events;
using Blazor.Diagrams.Core.Geometry;
using Blazor.Diagrams.Core.Models.Base;
using Teslametrics.App.Web.Components.Diagrams.Models;

namespace Teslametrics.App.Web.Components.Diagrams.Behaivor;


public class PolygonNodeBehaivor(Diagram diagram, uint exclusionVertexRadius = 24) : Behavior(diagram)
{
	private uint _exclusionVertexRadius = exclusionVertexRadius;

	private Dictionary<string, bool> _nodesLockState = new();
	private bool _allowMultiSelection;

	protected record Edge(MovableVertexModel A, MovableVertexModel B);

	public BasePolygonNodeModel? ActiveNode { get; protected set; }
	public bool IsEditInProgress => ActiveNode is not null;

	public delegate void EditStartedHandler(BasePolygonNodeModel polygon);
	public event EditStartedHandler? EditStarted;

	public delegate void EditFinishedHandler(BasePolygonNodeModel polygon);
	public event EditFinishedHandler? EditFinished;

	public void StartZoneEdit(BasePolygonNodeModel model)
	{
		ActiveNode = model;

		Diagram.UnselectAll();
		Diagram.SelectModel(ActiveNode, false);

		foreach (var node in Diagram.Nodes)
		{
			_nodesLockState.TryAdd(node.Id, node.Locked);
			node.Locked = true;
		}

		// Чтобы не летала по всей карте
		ActiveNode.Locked = true;

		_allowMultiSelection = Diagram.Options.AllowMultiSelection;
		Diagram.Options.AllowMultiSelection = false;

		Diagram.SelectionChanged += OnSelectionChanged;
		Diagram.PointerClick += SetZonePoint;
		Diagram.Nodes.Removed += OnNodeRemoved;
		EditStarted?.Invoke(ActiveNode);
		Diagram.Refresh();
	}

	public void EndZoneEdit()
	{
		if (ActiveNode is null) return;
		foreach (var node in Diagram.Nodes)
		{
			_nodesLockState.TryGetValue(node.Id, out bool locked);
			node.Locked = locked;
		}

		_nodesLockState.Clear();

		var nodePointer = Diagram.Nodes.First(node => node.Id == ActiveNode.Id);
		Diagram.Nodes.Removed -= OnNodeRemoved;
		Diagram.SelectionChanged -= OnSelectionChanged;
		EditFinished?.Invoke((BasePolygonNodeModel)nodePointer);

		// if (ActiveNode.VertexPoints.Count < 3) // Мне не нужно удалять ноду
		// {
		//	ActiveNode.Refresh();
		// 	Diagram.Nodes.Remove(ActiveNode);
		// }


		Diagram.Options.AllowMultiSelection = _allowMultiSelection;
		Diagram.PointerClick -= SetZonePoint;
		ActiveNode.Refresh();
		ActiveNode = null;
		Diagram.Refresh();
	}

	public override void Dispose()
	{
		EndZoneEdit();
	}

	// Правим косяк, что нам могут перекрывать обзор другие ноды
	protected virtual void OnSelectionChanged(SelectableModel model)
	{
		if (model.Selected && !ActiveNode!.Equals(model)) Diagram.UnselectModel(model);

		//if (!model.Selected && ActiveNode!.Equals(model)) Diagram.SelectModel(model, true);
	}

	protected virtual void OnNodeRemoved(Model model)
	{
		if (ActiveNode is null) return;

		if (ActiveNode.VertexPoints.Count < 3)
		{
			Diagram.Nodes.Remove(ActiveNode);
		}

		foreach (var node in Diagram.Nodes)
		{
			_nodesLockState.TryGetValue(node.Id, out bool locked);
			node.Locked = locked;
		}
		_nodesLockState.Clear();

		Diagram.Options.AllowMultiSelection = _allowMultiSelection;
		Diagram.PointerClick -= SetZonePoint;
		Diagram.SelectionChanged -= OnSelectionChanged;
		ActiveNode = null;
		Diagram.Nodes.Removed -= OnNodeRemoved;

		Diagram.Refresh();
	}

	// Координаты Polygon ведут счёт от верхнего-левого угла. Положительные значения идут вниз/право. В то время, как отрицательные - вверх/лево, уходя за SVG и становясь OVERFLOW
	protected virtual void SetZonePoint(Model? model, PointerEventArgs e)
	{
		if (ActiveNode is null) return;

		if (e.Button != (int)MouseEventButton.Left) return;

		if (e.ClientX < Diagram.Container?.Left) return;

		if (e.ClientX > Diagram.Container?.Right) return;

		if (e.ClientY < Diagram.Container?.Top) return;

		if (e.ClientY > Diagram.Container?.Bottom) return;

		// Координаты курсора на диаграмме
		Point absoluteCoodinates = new(Math.Round((e.ClientX - Diagram.Pan.X - Diagram.Container?.Left ?? 0) / Diagram.Zoom), Math.Round((e.ClientY - Diagram.Pan.Y - Diagram.Container?.Top ?? 0) / Diagram.Zoom));

		// Новая точка зоны
		Point innerShapePoint;
		// рассчитываем координаты точки относительно верхнего-правого угла зоны
		if (ActiveNode.VertexPoints.Count > 0)
		{
			innerShapePoint = new Point(absoluteCoodinates.X - ActiveNode.Position.X, absoluteCoodinates.Y - ActiveNode.Position.Y);

			if (ActiveNode.VertexPoints.Any(point => point.Position.DistanceTo(innerShapePoint) <= _exclusionVertexRadius))
				return;
		}
		else
		{
			innerShapePoint = new Point(0, 0);
			ActiveNode.SetPosition(absoluteCoodinates.X, absoluteCoodinates.Y);
		}

		if (ActiveNode.VertexPoints.Count > 2)
		{
			var edge = FindClosestEdge(ActiveNode, innerShapePoint)!;

			var indexA = ActiveNode.VertexPoints.IndexOf(edge.A);
			var indexB = ActiveNode.VertexPoints.IndexOf(edge.B);
			var position = Math.Max(indexA, indexB);
			if (Math.Min(indexA, indexB) == 0 && position == ActiveNode.VertexPoints.Count - 1)
			{
				ActiveNode.AddShapePoint(innerShapePoint);
			}
			else
			{
				ActiveNode.AddShapePoint(innerShapePoint, position);
			}
		}
		else
		{
			ActiveNode.AddShapePoint(innerShapePoint);
		}

		ActiveNode.Refresh();
	}

	protected static Edge? FindClosestEdge(BasePolygonNodeModel zone, Point point)
	{
		var edges = new List<Edge>();
		for (int i = 0; i < zone.VertexPoints.Count; i++)
		{
			var current = zone.VertexPoints[i];
			var next = zone.VertexPoints[(i + 1) % zone.VertexPoints.Count]; // Замыкаем цикл
			edges.Add(new Edge(current, next));
		}

		Edge? closestEdge = null;
		double minDistance = double.MaxValue;

		foreach (var edge in edges)
		{
			double distance = DistanceToSegment(point, edge);
			if (distance < minDistance)
			{
				minDistance = distance;
				closestEdge = edge;
			}
		}

		return closestEdge;
	}

	// Расстояние от точки P до отрезка AB
	private static double DistanceToSegment(Point P, Edge edge)
	{
		double x1 = edge.A.Position.X, y1 = edge.A.Position.Y;
		double x2 = edge.B.Position.X, y2 = edge.B.Position.Y;
		double x0 = P.X, y0 = P.Y;

		double dx = x2 - x1;
		double dy = y2 - y1;

		if ((dx == 0) && (dy == 0))
			return Math.Sqrt(Math.Pow(x0 - x1, 2) + Math.Pow(y0 - y1, 2)); // A == B

		double t = ((x0 - x1) * dx + (y0 - y1) * dy) / (dx * dx + dy * dy);

		if (t < 0)
			return Math.Sqrt(Math.Pow(x0 - x1, 2) + Math.Pow(y0 - y1, 2)); // P находится ближе к A
		else if (t > 1)
			return Math.Sqrt(Math.Pow(x0 - x2, 2) + Math.Pow(y0 - y2, 2)); // P ближе к B
		else
		{
			double px = x1 + t * dx;
			double py = y1 + t * dy;
			return Math.Sqrt(Math.Pow(x0 - px, 2) + Math.Pow(y0 - py, 2)); // P проецируется на отрезок
		}
	}
}
