using Orleans.Runtime;

namespace Orleans.Providers
{
    /// <summary>
    /// Memory stream queue grain. This grain works as a storage queue of event data. Enqueue and Dequeue operations are supported.
    /// the max event count sets the max storage limit to the queue.
    /// </summary>
    public class LiveStreamQueueGrain : Grain, ILiveStreamQueueGrain, IGrainMigrationParticipant
    {
        private List<LiveMessageData> _eventQueue = new List<LiveMessageData>();
        private int _readIndex = 0;
        private long sequenceNumber = DateTime.UtcNow.Ticks;

        /// <summary>
        /// Enqueues an event data. If the current total count reaches the max limit. throws an exception.
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public Task Enqueue(LiveMessageData data, int maxQueueLength)
        {
            if (_eventQueue.Count >= maxQueueLength)
            {
                _eventQueue.RemoveAt(0);
                if (_readIndex > 0)
                {
                    _readIndex--;
                }
            }
            data.SequenceNumber = sequenceNumber++;
            _eventQueue.Add(data);
            return Task.CompletedTask;
        }

        /// <summary>
        /// Reads up to a max amount of maxCount event data from the queue without removing them.
        /// </summary>
        /// <param name="maxCount"></param>
        /// <returns></returns>
        public Task<List<LiveMessageData>> Dequeue(int maxCount)
        {
            var list = new List<LiveMessageData>();

            int availableCount = _eventQueue.Count - _readIndex;
            int itemsToRead = Math.Min(maxCount, availableCount);

            for (int i = 0; i < itemsToRead; ++i)
            {
                list.Add(_eventQueue[_readIndex + i]);
            }

            _readIndex += itemsToRead;

            return Task.FromResult(list);
        }

        void IGrainMigrationParticipant.OnDehydrate(IDehydrationContext dehydrationContext)
        {
            dehydrationContext.TryAddValue("queue", _eventQueue);
            dehydrationContext.TryAddValue("readIndex", _readIndex);
        }

        void IGrainMigrationParticipant.OnRehydrate(IRehydrationContext rehydrationContext)
        {
            if (rehydrationContext.TryGetValue("queue", out List<LiveMessageData>? queueValue))
            {
                _eventQueue = queueValue!;
            }

            if (rehydrationContext.TryGetValue("readIndex", out int readIndexValue))
            {
                _readIndex = readIndexValue;
            }
        }
    }
}
