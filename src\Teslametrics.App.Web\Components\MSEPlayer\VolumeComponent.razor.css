.volume_container {
    display: flex;
    position: relative;
    align-items: center;
}

.volume_button_container {
    position: relative;
    z-index: 2;
}

.volume_slider_container {
    position: absolute;
    flex-direction: column;
    justify-content: flex-end;
    z-index: 1;
    visibility: hidden;
    opacity: 0;
    transition: visibility 0s, opacity 0.2s ease;
    display: flex;
    width: 120px;
    height: auto;
    transform: rotate(270deg);
    bottom: 51px;
    left: -47px;
}

.volume_container:hover .volume_slider_container,
::deep.volume_slider_container:hover {
    visibility: visible;
    opacity: 1;
}

::deep.volume_container .volume_slider {
    height: auto;
    width: 120px;
    padding-left: 26px;
}