/**
 * Набор режимов видеопотока с соответствующими настройками WebSocket и MIME-типами.
 */
export const MODES = {
  /**
   * Режим живой трансляции.
   * @returns {{ wsPath: string, mime: string, keep: number }}
   * Объект конфигурации: путь WebSocket, MIME-тип и время хранения в секундах.
   */
  live: () => ({
    wsPath: "/videoHub",
    mime: "video/mp2t;codecs=avc1.640028",
    keep: 30,
  }),

  /**
   * Режим воспроизведения с определённой временной точки.
   * @param {Object} params
   * @param {Date} params.start - Временная метка начала воспроизведения.
   * @returns {{ wsPath: string, mime: string, keep: number }}
   * Объект конфигурации: путь WebSocket, MIME-тип и время хранения в секундах.
   */
  point: ({ start }) => ({
    wsPath: "/videoHub?start=" + start.toISOString(),
    mime: "video/mp2t;codecs=avc1.640028", // Уточните MIME-тип при необходимости
    keep: 60,
  }),

  /**
   * Режим воспроизведения диапазона архива.
   * @param {Object} params
   * @param {Date} params.start - Временная метка начала диапазона.
   * @param {Date} params.end - Временная метка конца диапазона.
   * @returns {{ wsPath: string, mime: string, keep: number }}
   * Объект конфигурации: путь WebSocket, MIME-тип и время хранения в секундах.
   */
  range: ({ start, end }) => ({
    wsPath: `/videoHub?start=${start.toISOString()}&end=${end.toISOString()}`,
    mime: "video/mp2t;codecs=avc1.640028", // Уточните MIME-тип при необходимости
    keep: 120,
  }),
};
