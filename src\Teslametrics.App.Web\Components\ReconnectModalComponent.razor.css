#components-reconnect-modal {
    display: none;
    transition: visibility 0s linear 1000ms;
    user-select: none;
}

#components-reconnect-modal.components-reconnect-show, 
#components-reconnect-modal.components-reconnect-failed, 
#components-reconnect-modal.components-reconnect-rejected {
    position: fixed;
    top: 0;
    height: 100vh;
    width: 100vw;
    background-color: var(--mud-palette-overlay-dark) !important;
    display: flex;
    align-items: center;
    justify-content: center;
}

#components-reconnect-modal .reconnecting,
#components-reconnect-modal .rejected {
    display: none;
}

#components-reconnect-modal.components-reconnect-show .reconnecting,
#components-reconnect-modal.components-reconnect-failed .rejected,
#components-reconnect-modal.components-reconnect-rejected .rejected {
    display: block;
}