using Teslametrics.Core.Abstractions;

namespace Teslametrics.Core.Domain.Cameras;

public interface ICameraRepository : IRepository<CameraAggregate>
{
    public Task<bool> HasCamerasInFolderAsync(Guid id, CancellationToken cancellationToken = default);

    public Task<int> GetCameraCountByQuotaAsync(Guid quotaId, CancellationToken cancellationToken = default);

    public Task<List<CameraAggregate>> GetAllByQuotaIdAsync(Guid quotaId, CancellationToken cancellationToken = default);

    public Task<bool> IsCameraExistsAsync(Guid cameraId, CancellationToken cancellationToken = default);
}