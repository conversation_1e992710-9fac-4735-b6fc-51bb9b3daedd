@using Teslametrics.App.Web.Features.Main.CameraViews.Drawer.Create.GridItem;
@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<DrawerHeader>
    <MudText Typo="Typo.subtitle1"
             Class="pl-4">Создание Вида</MudText>
    <MudSpacer />
    <MudIconButton OnClick="CancelAsync"
                   Icon="@Icons.Material.Outlined.Close" />
</DrawerHeader>

<MudForm Model="_model"
         Validation="_validator"
         OverrideFieldValidation="true"
         UserAttributes="@(new Dictionary<string, object>() {{"autocomplete", "off"}, {"aria-autocomplete", "none"}, {"role", "presentation"} })"
         Class="form px-4 py-4"
         Spacing="3">
    <MudText Typo="Typo.subtitle2">Описание вида</MudText>
    <MudText Typo="Typo.caption">Наименование</MudText>
    <MudTextField @bind-Value="_model.Name"
                  For="@(() => _model.Name)"
                  InputType="InputType.Text"
                  Immediate="true"
                  Placeholder="Наименование"
                  RequiredError="Данное поле обязательно"
                  Required="true"
                  Variant="Variant.Outlined"
                  Margin="Margin.Dense" />

    <MudRadioGroup T="bool"
                   Value="@(_model.Grid == GridType.GridCustom)"
                   ValueChanged="@(value => SetGridType(value ? GridType.GridCustom : GridType.Grid1Plus5))"
                   InputClass="d-flex flex-column flex-nowrap align-start">
        <MudRadio T="bool"
                  Value="false"
                  Color="Color.Secondary"
                  Size="Size.Medium"
                  Class="ml-n3">Шаблоны сетки</MudRadio>
        @if (_model.Grid != GridType.GridCustom)
        {
            <div class="d-flex flex-wrap mb-4 gap-2 button_grid">
                <MudIconButton Icon="@TeslaIcons.Grid.Grid1Plus5"
                               Size="Size.Medium"
                               Variant="Variant.Filled"
                               Color="@(_model.Grid == GridType.Grid1Plus5 ? Color.Primary : Color.Default)"
                               OnClick="() => SetGridType(GridType.Grid1Plus5)" />

                <MudIconButton Icon="@TeslaIcons.Grid.Grid1Plus7"
                               Size="Size.Medium"
                               Variant="Variant.Filled"
                               Color="@(_model.Grid == GridType.Grid1Plus7 ? Color.Primary : Color.Default)"
                               OnClick="() => SetGridType(GridType.Grid1Plus7)" />

                <MudIconButton Icon="@TeslaIcons.Grid.Grid1Plus12"
                               Size="Size.Medium"
                               Variant="Variant.Filled"
                               Color="@(_model.Grid == GridType.Grid1Plus12 ? Color.Primary : Color.Default)"
                               OnClick="() => SetGridType(GridType.Grid1Plus12)" />

                <MudIconButton Icon="@TeslaIcons.Grid.Grid2Plus8"
                               Size="Size.Medium"
                               Variant="Variant.Filled"
                               Color="@(_model.Grid == GridType.Grid2Plus8 ? Color.Primary : Color.Default)"
                               OnClick="() => SetGridType(GridType.Grid2Plus8)" />

                <MudIconButton Icon="@TeslaIcons.Grid.Grid3Plus4"
                               Size="Size.Medium"
                               Variant="Variant.Filled"
                               Color="@(_model.Grid == GridType.Grid3Plus4 ? Color.Primary : Color.Default)"
                               OnClick="() => SetGridType(GridType.Grid3Plus4)" />
            </div>
            <div class="d_contents">
                <Sortable Id="filtering"
                          Class="@($"grid {_model.Grid.ToString()}")"
                          Filter=".filtered"
                          Style="@($"--cols: {_model.Columns}; --rows: {_model.Rows};")"
                          Items="_model.Cells"
                          OnUpdate="@SortList"
                          Context="item">
                    <SortableItemTemplate>
                        <GridItemComponent OrganizationId="@OrganizationId"
                                           @bind-Camera="@item.Camera"
                                           @key="item.CellId" />
                    </SortableItemTemplate>
                </Sortable>
            </div>
        }

        <MudRadio T="bool"
                  Value="true"
                  Color="Color.Primary"
                  Size="Size.Small"
                  Class="ml-n3">Пользоватская сетка</MudRadio>
        @if (_model.Grid == GridType.GridCustom)
        {
            <MudGrid Class="mb-3"
                     Spacing="2">
                <MudItem xs="6">
                    <MudSelect T="short"
                               @bind-Value="_model.Rows"
                               For="@(() => _model.Rows)"
                               Required="true"
                               Margin="Margin.Dense"
                               Variant="Variant.Outlined">
                        <MudSelectItem T="short"
                                       Value="1"
                                       Disabled="@(_model.Columns == 1)">1</MudSelectItem>
                        <MudSelectItem T="short"
                                       Value="2">2</MudSelectItem>
                        <MudSelectItem T="short"
                                       Value="3">3</MudSelectItem>
                        <MudSelectItem T="short"
                                       Value="4">4</MudSelectItem>
                        <MudSelectItem T="short"
                                       Value="5">5</MudSelectItem>
                        <MudSelectItem T="short"
                                       Value="6">6</MudSelectItem>
                        <MudSelectItem T="short"
                                       Value="7">7</MudSelectItem>
                        <MudSelectItem T="short"
                                       Value="8">8</MudSelectItem>
                    </MudSelect>
                </MudItem>

                <MudItem xs="6">
                    <MudSelect T="short"
                               @bind-Value="_model.Columns"
                               For="@(() => _model.Columns)"
                               Required="true"
                               Margin="Margin.Dense"
                               Variant="Variant.Outlined">
                        <MudSelectItem T="short"
                                       Value="1"
                                       Disabled="@(_model.Rows == 1)">1</MudSelectItem>
                        <MudSelectItem T="short"
                                       Value="2">2</MudSelectItem>
                        <MudSelectItem T="short"
                                       Value="3">3</MudSelectItem>
                        <MudSelectItem T="short"
                                       Value="4">4</MudSelectItem>
                        <MudSelectItem T="short"
                                       Value="5">5</MudSelectItem>
                        <MudSelectItem T="short"
                                       Value="6">6</MudSelectItem>
                        <MudSelectItem T="short"
                                       Value="7">7</MudSelectItem>
                        <MudSelectItem T="short"
                                       Value="8">8</MudSelectItem>
                    </MudSelect>
                </MudItem>
            </MudGrid>
            <div class="d_contents">
                <Sortable Id="filtering"
                          Class="@($"grid {_model.Grid.ToString()}")"
                          Filter=".filtered"
                          Style="@($"--cols: {_model.Columns}; --rows: {_model.Rows};")"
                          Items="_model.Cells"
                          OnUpdate="@SortList"
                          Context="item">
                    <SortableItemTemplate>
                        <GridItemComponent OrganizationId="@OrganizationId"
                                           @bind-Camera="@item.Camera"
                                           @key="item.CellId" />
                    </SortableItemTemplate>
                </Sortable>
            </div>
        }
    </MudRadioGroup>
</MudForm>
<DrawerActions>
    <MudStack Row="true"
              Class="mud-width-full"
              Justify="Justify.Center">
        <MudButton OnClick="CancelAsync"
                   Variant="Variant.Outlined"
                   StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
        <MudButton OnClick="SubmitAsync"
                   Disabled="@(!_isValid)"
                   Color="Color.Secondary"
                   Variant="Variant.Outlined">Сохранить</MudButton>
    </MudStack>
</DrawerActions>