::deep.cams_list_component {
	display: grid;
	grid-template-rows: auto 1fr;
	overflow: hidden;
}

.cameras_grid {
	display: grid;
	/* grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); */
	align-items: start;
	overflow-x: hidden;
	gap: 16px;
	overflow-y: auto;
	height: -webkit-fill-available;
	padding-bottom: 16px;
	margin-bottom: auto;
	grid-auto-rows: min-content;
}

@media (min-width: 576px)  and (max-width: 767.98px)  {
	.cameras_grid {
		grid-template-columns:repeat(1, 1fr);
	}
}
@media (min-width: 768px)  and (max-width: 1199.98px) {
	.cameras_grid {
		grid-template-columns:repeat(2, 1fr);
	}
}
@media (min-width: 1200px) and (max-width: 1919.98px) {
	.cameras_grid {
		grid-template-columns:repeat(3, 1fr);
	}
}

@media (min-width: 1920px) {
	.cameras_grid {
		grid-template-columns: repeat(4, 1fr);
	}
}

::deep.cameras_grid>div {
	max-width: 600px;
}

::deep .badge_position {
	padding-bottom: 4px !important;
	bottom: -3px !important;
	top: auto !important;
}

@media (max-width: 767px) {
    ::deep .list_header {
        z-index: 1;
        padding: 0 8px;
        box-shadow: var(--mud-elevation-1);
    }
    ::deep .cameras_grid {
        padding: 12px 8px;
    }
}

::deep .button_group button {
	padding: 0 15px;
	height: 33px;
}