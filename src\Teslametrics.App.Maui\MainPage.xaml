﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:Teslametrics.App.Maui"
             x:Class="Teslametrics.App.Maui.MainPage">

    <Grid>
        <!-- Main WebView -->
        <WebView x:Name="webView"
                 Source="http://158.160.184.249"
                 IsVisible="True"/>

        <!-- Fallback Content -->
        <Grid x:Name="fallbackContent"
              IsVisible="False"
              BackgroundColor="#667eea"
              Padding="20">

            <Border BackgroundColor="Transparent"
                    Stroke="White"
                    StrokeThickness="2"
                    StrokeShape="RoundRectangle 20"
                    Padding="40,30"
                    VerticalOptions="Center"
                    HorizontalOptions="Center"
                    MaximumWidthRequest="400">

                <StackLayout Spacing="20">
                    <!-- Icon -->
                    <Label Text="📡"
                           FontSize="64"
                           HorizontalOptions="Center"
                           TextColor="White"/>

                    <!-- Title -->
                    <Label Text="Connection Error"
                           FontSize="24"
                           FontAttributes="Bold"
                           HorizontalOptions="Center"
                           TextColor="White"/>

                    <!-- Message -->
                    <Label x:Name="errorMessage"
                           Text="Unable to connect to Teslametrics server. Please check your internet connection and try again."
                           FontSize="16"
                           HorizontalOptions="Center"
                           HorizontalTextAlignment="Center"
                           TextColor="White"
                           Opacity="0.9"/>

                    <!-- Retry Button -->
                    <Button x:Name="retryButton"
                            Text="Retry Connection"
                            BackgroundColor="Transparent"
                            BorderColor="White"
                            BorderWidth="2"
                            TextColor="White"
                            CornerRadius="25"
                            Padding="20,12"
                            FontSize="16"
                            FontAttributes="Bold"
                            Clicked="OnRetryClicked"
                            HorizontalOptions="Center"/>

                    <!-- Loading Indicator -->
                    <StackLayout x:Name="loadingIndicator"
                                 IsVisible="False"
                                 Orientation="Horizontal"
                                 HorizontalOptions="Center"
                                 Spacing="10">
                        <ActivityIndicator IsRunning="True"
                                           Color="White"/>
                        <Label Text="Connecting..."
                               TextColor="White"
                               VerticalOptions="Center"/>
                    </StackLayout>

                    <!-- Status -->
                    <Label x:Name="statusLabel"
                           Text="Tap retry to attempt reconnection"
                           FontSize="14"
                           HorizontalOptions="Center"
                           TextColor="White"
                           Opacity="0.7"/>
                </StackLayout>
            </Border>
        </Grid>
    </Grid>

</ContentPage>
