using MQTTnet;
using System.Reactive.Subjects;
using System.Reactive.Linq;
using Microsoft.Extensions.Logging;
using CommunityToolkit.HighPerformance.Helpers;

namespace Teslametrics.MediaServer.Mqtt;

public class MqttClientService : IDisposable
{
    private IMqttClient? _mqttClient;
    private MqttClientOptions? _options;
    private bool _intentionalDisconnect = false;
    private readonly ILogger<MqttClientService> _logger;

    // Список активных подписок для надежной подписки
    private readonly HashSet<string> _activeSubscriptions = new HashSet<string>();
    private readonly SemaphoreSlim _subscriptionLock = new SemaphoreSlim(1, 1);

    // Subject для публикации сообщений
    private readonly Subject<MqttMessage> _messageSubject = new Subject<MqttMessage>();

    private bool _disposed = false;

    public IMqttClient MqttClient => _mqttClient ?? throw new InvalidOperationException("MQTT client is not initialized");

    /// <summary>
    /// Observable для подписки на сообщения MQTT
    /// </summary>
    public IObservable<MqttMessage> Messages => _messageSubject.AsObservable();

    /// <summary>
    /// Состояние подключения к MQTT брокеру
    /// </summary>
    public bool IsConnected => _mqttClient?.IsConnected ?? false;

    public Action? OnConnected;

    public Action? OnDisconnected;

    public MqttClientService(ILogger<MqttClientService> logger)
    {
        _logger = logger;
    }

    public async Task ConnectAsync(string brokerAddress, int port, string clientId)
    {
        _intentionalDisconnect = false;

        // Создание фабрики клиента
        var factory = new MqttClientFactory();
        _mqttClient = factory.CreateMqttClient();

        // Настройка параметров подключения
        _options = new MqttClientOptionsBuilder()
            .WithTcpServer(brokerAddress, port)
            .WithClientId(clientId)
            .WithCleanSession()
            .Build();

        // Обработчики событий
        _mqttClient.ApplicationMessageReceivedAsync += e =>
        {
            var timestamp = DateTimeOffset.UtcNow;

            // Получаем сообщение и публикуем его в Subject
            var payload = e.ApplicationMessage.ConvertPayloadToString();
            var topic = e.ApplicationMessage.Topic;

            // Создаем объект сообщения и публикуем его
            var message = new MqttMessage(topic, payload, timestamp);
            _messageSubject.OnNext(message);

            return Task.CompletedTask;
        };

        _mqttClient.DisconnectedAsync += async e =>
        {
            if (!_intentionalDisconnect)
            {
                _logger.LogWarning("Connection lost. Attempting to reconnect...");
                OnDisconnected?.Invoke();
                await Task.Delay(TimeSpan.FromSeconds(5));
                await ConnectAsync();

                // Восстанавливаем все подписки после переподключения
                if (_mqttClient.IsConnected && _activeSubscriptions.Count > 0)
                {
                    await RestoreSubscriptionsAsync();
                }
            }
        };

        // Подключение к брокеру
        await ConnectAsync();
    }

    private async Task ConnectAsync()
    {
        try
        {
            await _mqttClient!.ConnectAsync(_options, CancellationToken.None);
            _logger.LogInformation("Connected to MQTT broker.");
            OnConnected?.Invoke();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Connection error: {Message}", ex.Message);
        }
    }

    /// <summary>
    /// Восстанавливает все активные подписки после переподключения
    /// </summary>
    private async Task RestoreSubscriptionsAsync()
    {
        await _subscriptionLock.WaitAsync();
        string[] topicsToRestore;
        try
        {
            // Копируем список активных подписок, чтобы не блокировать его надолго
            topicsToRestore = _activeSubscriptions.ToArray();
        }
        finally
        {
            _subscriptionLock.Release();
        }

        if (topicsToRestore.Length > 0)
        {
            _logger.LogInformation("Restoring {Count} subscriptions after reconnection", topicsToRestore.Length);
            await PerformSubscribeAsync(topicsToRestore);
        }
    }

    /// <summary>
    /// Подписывается на указанные топики с механизмом надежной подписки
    /// </summary>
    public async Task SubscribeAsync(params string[] topics)
    {
        if (topics.Length == 0)
        {
            return;
        }

        // Добавляем топики в список активных подписок
        await _subscriptionLock.WaitAsync();
        try
        {
            foreach (var topic in topics)
            {
                _activeSubscriptions.Add(topic);
            }
        }
        finally
        {
            _subscriptionLock.Release();
        }

        // Если клиент не подключен, подписки будут восстановлены при подключении
        if (_mqttClient?.IsConnected != true)
        {
            _logger.LogInformation("Client not connected. Topics added to active subscriptions and will be subscribed when connected.");
            return;
        }

        // Выполняем фактическую подписку
        await PerformSubscribeAsync(topics);
    }

    /// <summary>
    /// Выполняет фактическую подписку на топики с повторными попытками
    /// </summary>
    private async Task PerformSubscribeAsync(string[] topics, int retryCount = 3)
    {
        if (_mqttClient?.IsConnected != true || topics.Length == 0)
        {
            return;
        }

        try
        {
            // Создаем опции подписки
            var subscribeOptionsBuilder = new MqttClientSubscribeOptionsBuilder();

            // Добавляем все топики в один запрос подписки
            foreach (var topic in topics)
            {
                subscribeOptionsBuilder.WithTopicFilter(new MqttTopicFilterBuilder()
                    .WithTopic(topic)
                    .WithQualityOfServiceLevel(MQTTnet.Protocol.MqttQualityOfServiceLevel.AtLeastOnce)
                    .Build());

                _logger.LogDebug("Added topic for subscription: {Topic}", topic);
            }

            // Выполняем подписку
            var result = await _mqttClient.SubscribeAsync(subscribeOptionsBuilder.Build());

            _logger.LogInformation("Subscription completed successfully");
            foreach (var item in result.Items)
            {
                _logger.LogDebug("Topic: {Topic}, Result: {ResultCode}", item.TopicFilter.Topic, item.ResultCode);
            }
        }
        catch (Exception ex) when (retryCount > 0)
        {
            _logger.LogWarning(ex, "Subscription failed. Retrying... Attempts left: {RetryCount}", retryCount - 1);
            await Task.Delay(1000); // Небольшая задержка перед повторной попыткой
            await PerformSubscribeAsync(topics, retryCount - 1);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to subscribe to topics after multiple attempts");
            throw;
        }
    }

    /// <summary>
    /// Отписывается от указанных топиков
    /// </summary>
    public async Task UnsubscribeAsync(params string[] topics)
    {
        if (topics.Length == 0)
        {
            return;
        }

        // Удаляем топики из списка активных подписок
        await _subscriptionLock.WaitAsync();
        try
        {
            foreach (var topic in topics)
            {
                _activeSubscriptions.Remove(topic);
            }
        }
        finally
        {
            _subscriptionLock.Release();
        }

        // Если клиент не подключен, не нужно выполнять фактическую отписку
        if (_mqttClient?.IsConnected != true)
        {
            _logger.LogInformation("Client not connected. Topics removed from active subscriptions.");
            return;
        }

        // Выполняем фактическую отписку
        await PerformUnsubscribeAsync(topics);
    }

    /// <summary>
    /// Выполняет фактическую отписку от топиков с повторными попытками
    /// </summary>
    private async Task PerformUnsubscribeAsync(string[] topics, int retryCount = 3)
    {
        if (_mqttClient?.IsConnected != true || topics.Length == 0)
        {
            return;
        }

        try
        {
            // Создаем опции отписки
            var unsubscribeOptionsBuilder = new MqttClientUnsubscribeOptionsBuilder();

            // Добавляем все топики в один запрос отписки
            foreach (var topic in topics)
            {
                unsubscribeOptionsBuilder.WithTopicFilter(topic);
                _logger.LogDebug("Added topic for unsubscription: {Topic}", topic);
            }

            // Выполняем отписку
            var result = await _mqttClient.UnsubscribeAsync(unsubscribeOptionsBuilder.Build());

            _logger.LogInformation("Unsubscription completed successfully");
            foreach (var item in result.Items)
            {
                _logger.LogDebug("Unsubscribed from topic: {Topic}", item.TopicFilter);
            }
        }
        catch (Exception ex) when (retryCount > 0)
        {
            _logger.LogWarning(ex, "Unsubscription failed. Retrying... Attempts left: {RetryCount}", retryCount - 1);
            await Task.Delay(1000);
            await PerformUnsubscribeAsync(topics, retryCount - 1);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unsubscribe from topics after multiple attempts");
            throw;
        }
    }

    public async Task DisconnectAsync()
    {
        if (_mqttClient is not null && _mqttClient.IsConnected)
        {
            // Устанавливаем флаг намеренного отключения перед вызовом Disconnect
            _intentionalDisconnect = true;

            await _mqttClient.DisconnectAsync();
            _logger.LogInformation("Disconnected from MQTT broker.");
        }
    }

    public void Dispose()
    {
        if (_disposed) return;

        _disposed = true;

        try
        {
            // Отключаемся от MQTT брокера
            if (_mqttClient?.IsConnected == true)
            {
                _intentionalDisconnect = true;
                _mqttClient.DisconnectAsync().Wait(TimeSpan.FromSeconds(5));
            }

            // Освобождаем ресурсы
            _mqttClient?.Dispose();
            _messageSubject?.OnCompleted();
            _messageSubject?.Dispose();
            _subscriptionLock?.Dispose();
            _activeSubscriptions?.Clear();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing MqttClientService");
        }

        GC.SuppressFinalize(this);
    }
}