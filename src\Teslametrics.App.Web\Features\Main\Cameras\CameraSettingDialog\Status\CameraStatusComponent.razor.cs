using System.Reactive;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraSettingDialog.Status;

public partial class CameraStatusComponent : IDisposable
{
    private bool _isAccessGranted;

    [Parameter]
    public Guid CameraId { get; set; }

    [Parameter]
    public Guid OrganizationId { get; set; }

    [Parameter]
    public bool IsBlocked { get; set; }

    [Parameter]
    public CameraStatus CameraStatus { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        if (!IsBlocked)
            _isAccessGranted = await CheckIfPermissionGranted();

        await base.OnParametersSetAsync();
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            UnsubscribeFromAuthStateChanged();
        }

        base.Dispose(disposing);
    }

    #region [Actions]
    private async Task OnSwitchChanged(bool value)
    {
        if (value)
        {
            await ConnectAsync();
        }
        else
        {
            await DisconnectAsync();
        }
    }

    private async Task DisconnectAsync()
    {
        if (!_isAccessGranted || IsLoading) return;

        await SetLoadingAsync(true);
        DisconnectCameraUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new DisconnectCameraUseCase.Command(CameraId));
        }
        catch (Exception ex)
        {
            response = null;
            Snackbar.Add($"Не удалось отключить камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetLoadingAsync(false);

        if (response is null) return;
        switch (response.Result)
        {
            case DisconnectCameraUseCase.Result.Success:
                Snackbar.Add("Отправлен запрос на отключение камеры", MudBlazor.Severity.Success);
                break;
            case DisconnectCameraUseCase.Result.CameraNotFound:
                Snackbar.Add("Не удалось отключить камеру так как она не числится в системе", MudBlazor.Severity.Error);
                break;
            case DisconnectCameraUseCase.Result.ValidationError:
                Snackbar.Add("Не удалось отключить камеру из-за ошибки валидации", MudBlazor.Severity.Error);
                break;
            case DisconnectCameraUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraStatusComponent), nameof(DisconnectCameraUseCase));
                Snackbar.Add($"Не удалось отключить камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraStatusComponent), nameof(DisconnectCameraUseCase), response.Result);
                Snackbar.Add($"Не удалось отключить камеру из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task ConnectAsync()
    {
        ConnectCameraUseCase.Response? response;
        await SetLoadingAsync(true);
        try
        {
            response = await ScopeFactory.MediatorSend(new ConnectCameraUseCase.Command(CameraId));
        }
        catch (Exception ex)
        {
            response = null;
            Snackbar.Add($"Не удалось подключить камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
        await SetLoadingAsync(false);

        if (response is null) return;

        switch (response.Result)
        {
            case ConnectCameraUseCase.Result.Success:
                Snackbar.Add("Отправлен запрос на подключение камеры", MudBlazor.Severity.Success);
                break;
            case ConnectCameraUseCase.Result.CameraNotFound:
                Snackbar.Add("Не удалось подключить камеру так как она не числится в системе", MudBlazor.Severity.Error);
                break;
            case ConnectCameraUseCase.Result.ValidationError:
                Snackbar.Add("Не удалось подключить камеру из-за ошибки валидации", MudBlazor.Severity.Error);
                break;
            case ConnectCameraUseCase.Result.CameraIsBlocked:
                Snackbar.Add("Камера заблокирована", MudBlazor.Severity.Error);
                break;
            case ConnectCameraUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraStatusComponent), nameof(ConnectCameraUseCase));
                Snackbar.Add($"Не удалось подключить камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraStatusComponent), nameof(ConnectCameraUseCase), response.Result);
                Snackbar.Add($"Не удалось подключить камеру из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
    #endregion [Actions]

    #region [Event Handlers]
    private async void OnAuthenticationStateChanged(Task<AuthenticationState> authState)
    {
        _isAccessGranted = await CheckIfPermissionGranted(authState);
    }
    #endregion [Event Handlers]

    #region [Helpers]
    private Task<bool> CheckIfPermissionGranted()
    {
        var userAuthState = AuthenticationStateProvider.GetAuthenticationStateAsync();

        return CheckIfPermissionGranted(userAuthState);
    }

    private async Task<bool> CheckIfPermissionGranted(Task<AuthenticationState> authState)
    {
        var userAuthState = await authState;
        var policyRequirementResource = new PolicyRequirementResource(OrganizationId, CameraId);
        var appPermission = CameraStatus == CameraStatus.Stopped ? AppPermissions.Main.Cameras.Connect : AppPermissions.Main.Cameras.Disconnect;

        var authorizationResult = await AuthorizationService.AuthorizeAsync(
            userAuthState.User,  // Current user from AuthenticationStateProvider
            policyRequirementResource, // The resource being authorized
            appPermission.GetEnumPermissionString() // The policy name
        );

        return authorizationResult.Succeeded;
    }

    private void SubscribeToAuthStateChanged()
    {
        AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
    }

    private void UnsubscribeFromAuthStateChanged()
    {
        AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
    }
    #endregion
}
