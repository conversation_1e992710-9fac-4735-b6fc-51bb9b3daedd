﻿@inherits InteractiveBaseComponent
<div class="pa-4 mud-height-full">
    <MudText Typo="Typo.subtitle1"
             Class="mb-4">Происшествия</MudText>
    <div id="incidents-chart"
         style="height: 300px;"></div>
</div>

<style>
    .incidents-custom-tooltip {
        position: absolute;
        background: var(--mud-palette-surface);
        border-radius: 5px;
        font-family: Inter, sans-serif;
        font-size: 14px;
        box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
        pointer-events: none;
        z-index: 1000;
        transition: opacity 0.2s ease-in-out;
        white-space: nowrap;
    }

    .tooltip-date {
        font-size: 12px;
        margin-bottom: 4px;
        font-weight: 400;
        background: var(--color-neutral-70);
        padding: 6px 16px;
        border-radius: 5px 5px 0 0;
        color: var(--mud-palette-primary-text);
    }

    .tooltip-value {
        font-size: 14px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 16px;
    }
</style>