using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Devices.RoomContent;

public static class GetFridgeUseCase
{
    public record Query(Guid FridgeId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public FridgeModel Fridge { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(FridgeModel fridge)
        {
            Fridge = fridge;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;
            Fridge = new();
        }

        public record FridgeModel
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public List<ISensorModel> Sensors { get; set; } = [];
            public bool HasIncidents => Sensors.Any(s => s.Incident is not null);
        }

        public interface ISensorModel
        {
            public Guid Id { get; set; }
            public string Name { get; set; } // TODO: Название топика
            public string? DisplayName { get; set; }
            public Incident? Incident { get; set; }
        }

        public class TemperatureModel : ISensorModel
        {
            public Guid Id { get; set; }
            public string Name { get; set; } // TODO: Название топика
            public string? DisplayName { get; set; }
            public float MinTemp { get; set; }
            public float MaxTemp { get; set; }
            public Incident? Incident { get; set; }

            public TemperatureModel(Guid id, string name, string? displayName, float minTemp, float maxTemp, Incident? incident = null)
            {
                Id = id;
                Name = name;
                DisplayName = displayName;
                MinTemp = minTemp;
                MaxTemp = maxTemp;
                Incident = incident;
            }
        }

        public class DoorModel : ISensorModel
        {
            public Guid Id { get; set; }
            public string Name { get; set; } // TODO: Название топика
            public string? DisplayName { get; set; }
            public int AvailableOpeningTime { get; set; }
            public Incident? Incident { get; set; }

            public DoorModel(Guid id, string name, string? displayName, int availableOpeningTime, Incident? incident = null)
            {
                Id = id;
                Name = name;
                DisplayName = displayName;
                AvailableOpeningTime = availableOpeningTime;
                Incident = incident;
            }
        }

        public class HumidityModel : ISensorModel
        {
            public Guid Id { get; set; }
            public string Name { get; set; } // TODO: Название топика
            public string? DisplayName { get; set; }
            public float MinHumidity { get; set; }
            public float MaxHumidity { get; set; }
            public Incident? Incident { get; set; }

            public HumidityModel(Guid id, string name, string? displayName, float minHumidity, float maxHumidity, Incident? incident = null)
            {
                Id = id;
                Name = name;
                DisplayName = displayName;
                MinHumidity = minHumidity;
                MaxHumidity = maxHumidity;
                Incident = incident;
            }
        }

        public class LeakModel : ISensorModel
        {
            public Guid Id { get; set; }
            public string Name { get; set; } // TODO: Название топика
            public string? DisplayName { get; set; }
            public Incident? Incident { get; set; }
            public LeakModel(Guid id, string name, string? displayName, Incident? incident = null)
            {
                Id = id;
                Name = name;
                DisplayName = displayName;
                Incident = incident;
            }
        }

        public class PowerModel : ISensorModel
        {
            public Guid Id { get; set; }
            public string Name { get; set; } // TODO: Название топика
            public string? DisplayName { get; set; }
            public Incident? Incident { get; set; }
            public PowerModel(Guid id, string name, string? displayName, Incident? incident = null)
            {
                Id = id;
                Name = name;
                DisplayName = displayName;
                Incident = incident;
            }
        }

        public record Incident // Инцидент с датчиком
        {
            public Guid Id { get; set; }
            public IncidentType IncidentType { get; set; }
            public DateTimeOffset TriggeredAt { get; set; } // Когда он произошел
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        PlanNotFound,
        FridgeNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.FridgeId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator, IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            if (!await CheckTableExistsAsync())
            {
                return new Response(Result.PlanNotFound);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Plans.Props.Page)
                .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

            var pageJson = await _dbConnection.ExecuteScalarAsync<string?>(template.RawSql);

            if (string.IsNullOrEmpty(pageJson))
            {
                return new Response(Result.PlanNotFound);
            }

            var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

            var fridge = page.GetFridge(request.FridgeId);

            if (fridge is null)
            {
                return new Response(Result.FridgeNotFound);
            }

            template = SqlQueryBuilder.Create()
                .Select(Db.Incidents.Props.Id)
                .Select(Db.Incidents.Props.SensorId)
                .Select(Db.Incidents.Props.IncidentType)
                .Select(Db.Incidents.Props.CreatedAt)
                .Where($"({Db.Incidents.Props.DeviceId} = :DeviceId OR {Db.Incidents.Columns.IncidentType} = :IncidentType)", new { DeviceId = request.FridgeId, IncidentType = IncidentType.WirenboardDisconnected.ToString() })
                .Where(Db.Incidents.Props.ResolvedAt, SqlOperator.IsNull)
                .Build(QueryType.Standard, Db.Incidents.Table, RowSelection.AllRows);

            var incidents = await _dbConnection.QueryAsync<IncidentModel>(template.RawSql, template.Parameters);

            var fridgeModel = new Response.FridgeModel { Id = fridge.Id, Name = fridge.Name, Sensors = fridge.Sensors.Select(s => GetSensorModel(s, incidents.Where(i => i.SensorId == s.Id || i.IncidentType == IncidentType.WirenboardDisconnected))).ToList() };

            return new Response(fridgeModel);
        }

        private Response.ISensorModel GetSensorModel(ISensorModel sensorModel, IEnumerable<IncidentModel> incidents)
        {
            var incident = incidents.OrderByDescending(i => i.CreatedAt).FirstOrDefault();

            var model = sensorModel switch
            {
                TemperatureModel sensor => (Response.ISensorModel)new Response.TemperatureModel(sensor.Id, sensor.Name, sensor.DisplayName, sensor.MinTemp, sensor.MaxTemp),
                DoorModel sensor => new Response.DoorModel(sensor.Id, sensor.Name, sensor.DisplayName, sensor.AvailableOpeningTime),
                HumidityModel sensor => new Response.HumidityModel(sensor.Id, sensor.Name, sensor.DisplayName, sensor.MinHumidity, sensor.MaxHumidity),
                LeakModel sensor => new Response.LeakModel(sensor.Id, sensor.Name, sensor.DisplayName),
                PowerModel sensor => new Response.PowerModel(sensor.Id, sensor.Name, sensor.DisplayName),
                _ => throw new ArgumentException($"Unknown sensor type: {sensorModel.GetType()}")
            };

            model.Incident = incident is null ? null : new Response.Incident { Id = incident.Id, IncidentType = incident.IncidentType, TriggeredAt = incident.CreatedAt };

            return model;
        }

        private async Task<bool> CheckTableExistsAsync()
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = Db.Plans.Table });

            return tableExists > 0;
        }
    }

    public record IncidentModel(Guid Id, Guid SensorId, IncidentType IncidentType, DateTimeOffset CreatedAt);
}