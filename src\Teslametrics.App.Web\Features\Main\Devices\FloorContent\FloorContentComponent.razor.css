﻿::deep .list {
	overflow: auto;
}

::deep .list .list_item {
	border-radius: 8px;
	border: none;
}

.mud_theme_light div ::deep .list .list_item:hover {
    background: var(--color-bg-2);
}

::deep .list .list_item:not(:first-child) {
	margin-top: 8px;
}

::deep .list .list_item .content {
	display: flex;
	flex-direction: row;
	align-content: center;
	align-items: center;
	gap: 10px;
	overflow: hidden;
	white-space: nowrap;
	color: rgba(184, 194, 204, 1) !important;
}

::deep .list .list_item .content .room_name {
    color: var(--color-text-input-tile);
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.data_item {
    display: flex;
    padding: 4px 8px;
    align-items: center;
    align-content: center;
    gap: 4px;
    background: var(--mud-palette-surface);
    border-radius: 8px;
    color: var(--color-text-placeholder);
}

.to_room {
    display: flex;
    align-items: center;
    align-content: center;
    min-height: 32px;
	min-width: 32px;
	border-radius: 50%;
    background: var(--mud-palette-surface);
    justify-content: center;
}
::deep .mud-list-item-icon {
    min-width: 32px;
}