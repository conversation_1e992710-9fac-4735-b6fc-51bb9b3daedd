using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using MudBlazor;
using Teslametrics.MediaServer.Orleans.Camera;

namespace Teslametrics.App.Web.Components.MSEPlayer;

public partial class MsePlayer : IMsePlayer, IAsyncDisposable
{
    // Reference to the video element
    private ElementReference? _videoElement;

    // Текущая громкость (0.0 - 1.0)
    private float _volume = 1.0f;
    public float Volume => _volume;

    private IJSObjectReference? _playerJsModule;
    private DotNetObjectReference<MsePlayer>? _objRef;
    //private bool _isStreamActive = false;

    #region [Injectables]
    [Inject]
    private ISnackbar Snackbar { get; set; } = null!;

    [Inject]
    private IJSRuntime Js { get; set; } = null!;
    #endregion

    #region [Parameters]

    [Parameter]
    public bool Muted { get; set; } = true;

    [Parameter]
    public bool Autoplay { get; set; } = true;

    [Parameter]
    public RenderFragment<IMsePlayer>? ToolbarContent { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid CameraId { get; set; }

    [Parameter]
    [EditorRequired]
    public StreamType Type { get; set; }
    #endregion

    public IMsePlayer.StreamStatus Status { get; protected set; }
    public event EventHandler<IMsePlayer.StreamStatus>? StreamStatusChanged;

    // Состояние полноэкранного режима
    private bool _isFullscreen = false;
    public bool IsFullscreen => _isFullscreen;
    public event EventHandler<bool>? FullscreenChanged;

    #region [JSInvokables]
    [JSInvokable]
    public void OnCameraStatusChanged(string status)
    {
        Status = status == "playing" ? IMsePlayer.StreamStatus.Playing :
            status == "paused" ? IMsePlayer.StreamStatus.Paused :
            IMsePlayer.StreamStatus.Stopped;

        StreamStatusChanged?.Invoke(this, Status);
    }

    [JSInvokable]
    public void OnFullscreenChanged(bool isFullscreen)
    {
        // Обновляем состояние полноэкранного режима
        _isFullscreen = isFullscreen;

        // Вызываем событие изменения состояния полноэкранного режима
        FullscreenChanged?.Invoke(this, isFullscreen);
    }

    [JSInvokable]
    public void OnCameraError(string errorMessage)
    {
        Logger.LogError("Ошибка в видеоплеере камеры {CameraId}: {ErrorMessage}", CameraId, errorMessage);
        Snackbar.Add($"Ошибка видеоплеера: {errorMessage}", Severity.Error);
    }

    [JSInvokable]
    public void OnTimeUpdate(string? timeString)
    {
        // Этот метод может быть использован для обновления timeline или других компонентов
        // В будущем здесь можно добавить событие TimeUpdated если потребуется
        // Пока просто логируем для отладки (можно убрать в продакшене)
        if (!string.IsNullOrEmpty(timeString))
        {
            // Logger.LogDebug("Time update for camera {CameraId}: {Time}", CameraId, timeString);
        }
    }
    #endregion

    #region [Public API]
    public async Task ResumeStream()
    {
        if (_playerJsModule is null) throw new ArgumentNullException(nameof(_playerJsModule)); // Стоит ли иметь другой тип исключения?

        await _playerJsModule.InvokeVoidAsync("resumeStream", CameraId);
    }

    public async Task PauseStream()
    {
        if (_playerJsModule is null) throw new ArgumentNullException(nameof(_playerJsModule)); // Стоит ли иметь другой тип исключения?

        await _playerJsModule.InvokeVoidAsync("pauseStream", CameraId);
    }

    /// <summary>
    /// Полностью останавливает поток и закрывает соединение с сервером
    /// </summary>
    /// <throws>JSDisconnectedException</throws>
    /// <throws>Exception</throws>
    public async Task StopStream()
    {
        if (_playerJsModule is null) return; // Если модуль не инициализирован, ничего делать не нужно

        try
        {
            await _playerJsModule.InvokeVoidAsync("stopStream", CameraId);
        }
        catch (JSDisconnectedException)
        {
            // Если соединение с JS уже разорвано, ничего делать не нужно
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при остановке потока: {Message}", ex.Message);
            // Не показываем Snackbar, так как это может происходить при закрытии страницы
        }
    }

    /// <summary>
    /// Устанавливает громкость видео (0.0 - 1.0)
    /// </summary>
    /// <param name="volume">Значение громкости от 0.0 (без звука) до 1.0 (максимальная громкость)</param>
    /// <throws>JSDisconnectedException</throws>
    /// <throws>Exception</throws>
    public async Task SetVolumeAsync(float volume)
    {
        if (_playerJsModule is null) throw new ArgumentNullException(nameof(_playerJsModule));

        // Ограничиваем значение громкости в диапазоне от 0.0 до 1.0
        volume = Math.Clamp(volume, 0.0f, 1.0f);

        // Обновляем значения полей
        _volume = volume;

        // Вызываем JavaScript-функцию для установки громкости
        await _playerJsModule.InvokeVoidAsync("setVolume", CameraId, volume);
        Muted = false;
    }

    /// <summary>
    /// Переключает полноэкранный режим для видеоэлемента
    /// </summary>
    /// <throws>JSDisconnectedException</throws>
    /// <throws>Exception</throws>
    public async Task ToggleFullscreenAsync()
    {
        if (_playerJsModule is null) throw new ArgumentNullException(nameof(_playerJsModule));

        try
        {
            await _playerJsModule.InvokeVoidAsync("toggleFullscreen", CameraId);
        }
        catch (JSDisconnectedException)
        {
            // Если соединение с JS уже разорвано, ничего делать не нужно
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при переключении полноэкранного режима: {Message}", ex.Message);
            Snackbar.Add("Ошибка при переключении полноэкранного режима!", Severity.Error);
        }
    }

    /// <summary>
    /// Переходит к просмотру прямого эфира (live edge)
    /// </summary>
    /// <throws>JSDisconnectedException</throws>
    /// <throws>Exception</throws>
    public async Task SeekToLiveAsync()
    {
        if (_playerJsModule is null) throw new ArgumentNullException(nameof(_playerJsModule));

        try
        {
            await _playerJsModule.InvokeVoidAsync("seekToLive", CameraId);

            // Если видео было на паузе, возобновляем воспроизведение
            if (Status == IMsePlayer.StreamStatus.Paused)
            {
                await ResumeStream();
            }
        }
        catch (JSDisconnectedException)
        {
            // Если соединение с JS уже разорвано, ничего делать не нужно
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при переходе к прямому эфиру: {Message}", ex.Message);
            Snackbar.Add("Ошибка при переходе к прямому эфиру!", Severity.Error);
        }
    }

    /// <summary>
    /// Возвращает время записи текущего сегмента видео
    /// </summary>
    /// <returns>Строка с временем в формате ISO 8601 или null, если время не определено</returns>
    /// <throws>JSDisconnectedException</throws>
    /// <throws>Exception</throws>
    public async Task<string?> GetCurrentSegmentTimestampAsync()
    {
        if (_playerJsModule is null) throw new ArgumentNullException(nameof(_playerJsModule));

        try
        {
            return await _playerJsModule.InvokeAsync<string?>("getCurrentSegmentTimestamp", CameraId);
        }
        catch (JSDisconnectedException)
        {
            // Если соединение с JS уже разорвано, возвращаем null
            return null;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при получении времени сегмента: {Message}", ex.Message);
            return null;
        }
    }

    /// <summary>
    /// Возвращает текущее абсолютное время воспроизведения видео
    /// </summary>
    /// <returns>Строка с временем в формате ISO 8601 или null, если время не определено</returns>
    /// <throws>JSDisconnectedException</throws>
    /// <throws>Exception</throws>
    public async Task<string?> GetCurrentAbsoluteTimeAsync()
    {
        if (_playerJsModule is null) throw new ArgumentNullException(nameof(_playerJsModule));

        try
        {
            return await _playerJsModule.InvokeAsync<string?>("getCurrentAbsoluteTime", CameraId);
        }
        catch (JSDisconnectedException)
        {
            // Если соединение с JS уже разорвано, возвращаем null
            return null;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при получении абсолютного времени: {Message}", ex.Message);
            return null;
        }
    }

    #endregion

    public async ValueTask DisposeAsync()
    {
        try
        {
            // Сначала отменяем регистрацию callback'а
            if (_playerJsModule is not null)
            {
                try
                {
                    await _playerJsModule.InvokeVoidAsync("unregisterStatusCallback", CameraId);
                }
                catch (JSDisconnectedException)
                {
                    // Если соединение с JS уже разорвано, ничего делать не нужно
                }
            }

            // Затем останавливаем поток и закрываем SignalR-соединение
            await StopStream();

            // Затем освобождаем ресурсы JS-модуля
            if (_playerJsModule is not null)
            {
                await _playerJsModule.DisposeAsync();
            }

            // Освобождаем ссылку на .NET-объект
            _objRef?.Dispose();
        }
        catch (JSDisconnectedException)
        {
            // Если соединение с JS уже разорвано, возвращаем null
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при освобождении ресурсов: {Message}", ex.Message);
        }

        GC.SuppressFinalize(this);
    }

    protected override void OnInitialized()
    {
        if (!Muted && Autoplay) throw new ArgumentException("Невозможно создать плеер с autoplay и с muted = false");

        _objRef = DotNetObjectReference.Create(this);
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            try
            {
                // Загружаем основной модуль плеера
                _playerJsModule = await Js.InvokeAsync<IJSObjectReference>("import", "./Components/MSEPlayer/MsePlayer.razor.js");

                if (_playerJsModule is not null)
                {
                    // Сначала регистрируем callback для получения уведомлений о изменении состояния плеера
                    await _playerJsModule.InvokeVoidAsync("registerStatusCallback", CameraId, _objRef);

                    // Затем инициализируем плеер (это запустит подписку на события)
                    await _playerJsModule.InvokeVoidAsync("initializePlayer",
                            _videoElement,
                            CameraId,
                            "live",            // "live" | "point" | "range"
                            null,
                            null,
                            null
                    );

                    // Синхронизируем начальное состояние плеера
                    await SynchronizeInitialStateAsync();

                    // Автоматически запускаем поток при инициализации
                    //await StartStream();
                }
            }
            catch (JSDisconnectedException)
            {
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Ошибка при загрузке скриптов: {Message}", ex.Message);
                Snackbar.Add("Не удалось загрузить необходимые файлы скриптов, плеер будет недоступен!", Severity.Error);
            }
        }
    }

    /// <summary>
    /// Синхронизирует начальное состояние плеера с UI компонентами
    /// </summary>
    private async Task SynchronizeInitialStateAsync()
    {
        if (_playerJsModule is null) return;

        try
        {
            // Небольшая задержка, чтобы дать плееру время полностью инициализироваться
            await Task.Delay(100);

            // Получаем текущее состояние плеера из JavaScript
            var currentState = await _playerJsModule.InvokeAsync<string?>("getPlayerState", CameraId);

            if (!string.IsNullOrEmpty(currentState))
            {
                // Обновляем состояние через тот же метод, что и события
                OnCameraStatusChanged(currentState);
                Logger.LogDebug("Синхронизировано начальное состояние плеера {CameraId}: {State}", CameraId, currentState);
            }
        }
        catch (JSDisconnectedException)
        {
            // Если соединение с JS уже разорвано, ничего делать не нужно
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Не удалось синхронизировать начальное состояние плеера {CameraId}: {Message}", CameraId, ex.Message);
        }
    }

    private async Task StartStream()
    {
        if (_playerJsModule is null)
        {
            Snackbar.Add("Плеер не инициализирован!", Severity.Warning);
            return;
        }

        try
        {
            await _playerJsModule.InvokeVoidAsync("resumeStream", CameraId);
            //_isStreamActive = true;
            StateHasChanged();
        }
        catch (JSDisconnectedException)
        {
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при запуске потока: {Message}", ex.Message);
            Snackbar.Add("Ошибка при запуске потока!", Severity.Error);
        }
    }
}
