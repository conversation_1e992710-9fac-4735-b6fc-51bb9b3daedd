
::deep .legend {
	display: flex;
	align-items: center;
	gap: 8px;
}

::deep .legend::before {
	content: " ";
	display: block;
	width: 10px;
	height: 10px;
	border-radius: 5px;
}

::deep .legend.door_opened::before {
	background: var(--color-primary-800);
}

::deep .legend.average_time::before {
	background: #33C7F8;
}

.door-custom-tooltip {
	position: absolute;
	background: var(--mud-palette-surface);
	border-radius: 5px;
	font-family: Inter, sans-serif;
	font-size: 14px;
	box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
	pointer-events: none;
	z-index: 1000;
	transition: opacity 0.2s ease-in-out;
	white-space: nowrap;
}

.tooltip-value {
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 16px; 
}

.tooltip-indicator {
    width: 10px;
    height: 10px;
    display: block;
    border-radius: 5px;
}

.door-opened .tooltip-indicator {
	background: var(--color-primary-800);
}

.average-time .tooltip-indicator {
	background: #33C7F8;
}

.tooltip-date {
	font-size: 12px;
	margin-bottom: 4px;
	font-weight: 400;
	background: var(--color-neutral-70);
	padding: 6px 16px;
	border-radius: 5px 5px 0 0;
	color: var(--mud-palette-primary-text);
}

.tooltip-text {
    font-weight: 500;
    font-size: 14px;
}

.tooltip-value-text {
    font-size: 14px;
}