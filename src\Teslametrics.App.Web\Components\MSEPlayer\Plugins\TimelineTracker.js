import { MediaPipelinePlugin } from "./MediaPipelinePlugin.js";

/**
 * Плагин для высокочастотного отслеживания времени воспроизведения
 * Обеспечивает непрерывное отслеживание video.currentTime и абсолютного времени записи
 */
export class TimelineTracker extends MediaPipelinePlugin {
  constructor(pipeline, options = {}) {
    super(pipeline, options);

    // Настройки отслеживания времени
    this.updateInterval = options.updateInterval || 100; // мс (10fps по умолчанию)
    this.highFrequencyMode = options.highFrequency || false;
    this.adaptiveMode = options.adaptive || true;

    // Состояние отслеживания
    this.lastEmittedTime = null;
    this.lastEmittedMediaTime = null;
    this.animationId = null;
    this.intervalId = null;
    this.frameCount = 0;
    this.lastFpsCheck = performance.now();

    // Адаптивная частота обновлений
    this.targetFps = 10; // По умолчанию 10fps
    this.maxFps = 60;
    this.minFps = 1;

    // Метрики производительности
    this.metrics = {
      updatesPerSecond: 0,
      averageLatency: 0,
      droppedFrames: 0,
      lastUpdateTime: 0,
    };

    // Событийная шина для внешних подписчиков
    this.events = new EventTarget();

    // Отслеживание активности пользователя
    this.userActivityTimer = null;
    this.isUserActive = true;
  }

  /**
   * Инициализация плагина
   */
  initialize() {
    super.initialize();

    // Подписываемся на события MediaPipeline
    this.on("timeupdate", () => this.onPipelineTimeUpdate());
    this.on("buffer", () => this.onBufferUpdate());

    // Настраиваем отслеживание активности пользователя
    if (this.adaptiveMode) {
      this.setupActivityTracking();
    }

    // Запускаем отслеживание времени
    this.startTracking();

    console.log("[TimelineTracker] Инициализирован с настройками:", {
      updateInterval: this.updateInterval,
      highFrequencyMode: this.highFrequencyMode,
      adaptiveMode: this.adaptiveMode,
      targetFps: this.targetFps,
    });
  }

  /**
   * Деинициализация плагина
   */
  dispose() {
    this.stopTracking();
    this.cleanupActivityTracking();
    super.dispose();

    console.log("[TimelineTracker] Ресурсы освобождены");
  }

  /**
   * Запускает отслеживание времени
   */
  startTracking() {
    if (!this.isActive) return;

    this.lastFpsCheck = performance.now();
    this.frameCount = 0;

    if (this.highFrequencyMode) {
      this.startAnimationFrameTracking();
    } else {
      this.startIntervalTracking();
    }

    this.emitEvent("tracking-started");
  }

  /**
   * Останавливает отслеживание времени
   */
  stopTracking() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    this.emitEvent("tracking-stopped");
  }

  /**
   * Отслеживание через requestAnimationFrame (до 60fps)
   */
  startAnimationFrameTracking() {
    const track = (timestamp) => {
      if (!this.isActive) return;

      this.frameCount++;
      const currentTime = this.pipeline.currentAbsTime;
      const mediaTime = this.pipeline.video.currentTime;

      if (this.shouldEmitUpdate(currentTime, mediaTime)) {
        this.emitTimeUpdate(currentTime, mediaTime, timestamp);
      }

      // Обновляем метрики FPS
      this.updateFpsMetrics(timestamp);

      this.animationId = requestAnimationFrame(track);
    };

    this.animationId = requestAnimationFrame(track);
  }

  /**
   * Отслеживание через setInterval с компенсацией drift
   */
  startIntervalTracking() {
    let lastTime = performance.now();
    let expectedTime = lastTime + this.updateInterval;

    const track = () => {
      if (!this.isActive) return;

      const now = performance.now();
      const drift = now - expectedTime;

      // Компенсируем drift, но не более чем на 50% от интервала
      const nextInterval = Math.max(
        this.updateInterval - drift,
        this.updateInterval * 0.5
      );

      expectedTime = now + nextInterval;

      const currentTime = this.pipeline.currentAbsTime;
      const mediaTime = this.pipeline.video.currentTime;

      if (this.shouldEmitUpdate(currentTime, mediaTime)) {
        this.emitTimeUpdate(currentTime, mediaTime, now);
      }

      this.intervalId = setTimeout(track, nextInterval);
    };

    this.intervalId = setTimeout(track, this.updateInterval);
  }

  /**
   * Определяет, нужно ли эмитировать обновление времени
   */
  shouldEmitUpdate(currentTime, mediaTime) {
    if (!currentTime || isNaN(mediaTime)) return false;

    // Первое обновление
    if (!this.lastEmittedTime) return true;

    // Проверяем изменение времени
    const timeDiff = Math.abs(
      currentTime.getTime() - this.lastEmittedTime.getTime()
    );
    const mediaTimeDiff = Math.abs(
      mediaTime - (this.lastEmittedMediaTime || 0)
    );

    // Эмитируем если:
    // 1. Абсолютное время изменилось более чем на 50мс
    // 2. Media время изменилось более чем на 0.05 сек
    // 3. Прошло более updateInterval мс с последнего обновления
    const timeThreshold = 50; // мс
    const mediaTimeThreshold = 0.05; // сек
    const intervalThreshold = this.updateInterval;

    const now = performance.now();
    const intervalPassed =
      now - this.metrics.lastUpdateTime >= intervalThreshold;

    return (
      timeDiff >= timeThreshold ||
      mediaTimeDiff >= mediaTimeThreshold ||
      intervalPassed
    );
  }

  /**
   * Эмитирует событие обновления времени
   */
  emitTimeUpdate(currentTime, mediaTime, timestamp) {
    const now = performance.now();
    const latency = now - timestamp;

    // Обновляем метрики
    this.updateMetrics(latency);

    // Сохраняем последние значения
    this.lastEmittedTime = currentTime;
    this.lastEmittedMediaTime = mediaTime;
    this.metrics.lastUpdateTime = now;

    // Создаем объект данных для передачи
    const timeData = {
      absoluteTime: currentTime,
      mediaTime: mediaTime,
      timestamp: now,
      latency: latency,
      bufferStart: this.pipeline.bufferStart,
      bufferEnd: this.pipeline.bufferEnd,
      isPlaying: !this.pipeline.video.paused,
      playbackRate: this.pipeline.video.playbackRate,
    };

    // Эмитируем событие для внешних подписчиков
    this.emitEvent("time-update", timeData);

    console.debug("[TimelineTracker] Время обновлено:", {
      absTime: currentTime.toISOString(),
      mediaTime: mediaTime.toFixed(3),
      latency: latency.toFixed(2) + "мс",
    });
  }

  /**
   * Обработчик обновлений от MediaPipeline
   */
  onPipelineTimeUpdate() {
    // Можем использовать для дополнительной синхронизации
    // или для адаптивной частоты обновлений
  }

  /**
   * Обработчик обновлений буфера
   */
  onBufferUpdate() {
    // Реагируем на изменения буфера
  }

  /**
   * Настройка отслеживания активности пользователя
   */
  setupActivityTracking() {
    const onActivity = () => {
      this.isUserActive = true;
      this.setTargetFps(this.maxFps);

      clearTimeout(this.userActivityTimer);
      this.userActivityTimer = setTimeout(() => {
        this.isUserActive = false;
        this.setTargetFps(this.minFps);
      }, 5000); // 5 секунд неактивности
    };

    // Отслеживаем активность
    this.activityEvents = [
      "mousedown",
      "mousemove",
      "keydown",
      "scroll",
      "touchstart",
    ];
    this.activityHandler = onActivity;

    this.activityEvents.forEach((event) => {
      document.addEventListener(event, this.activityHandler, { passive: true });
    });

    // Начальная активность
    onActivity();
  }

  /**
   * Очистка отслеживания активности
   */
  cleanupActivityTracking() {
    if (this.activityEvents && this.activityHandler) {
      this.activityEvents.forEach((event) => {
        document.removeEventListener(event, this.activityHandler);
      });
    }

    if (this.userActivityTimer) {
      clearTimeout(this.userActivityTimer);
    }
  }

  /**
   * Устанавливает целевую частоту обновлений
   */
  setTargetFps(fps) {
    this.targetFps = Math.max(this.minFps, Math.min(this.maxFps, fps));
    this.updateInterval = 1000 / this.targetFps;

    // Перезапускаем отслеживание с новой частотой
    if (this.isActive && !this.highFrequencyMode) {
      this.stopTracking();
      this.startTracking();
    }

    console.log(
      `[TimelineTracker] Частота обновлений изменена на ${this.targetFps} fps`
    );
  }

  /**
   * Обновляет метрики производительности
   */
  updateMetrics(latency) {
    this.metrics.averageLatency =
      this.metrics.averageLatency * 0.9 + latency * 0.1;
    this.metrics.updatesPerSecond++;
  }

  /**
   * Обновляет метрики FPS
   */
  updateFpsMetrics(timestamp) {
    const elapsed = timestamp - this.lastFpsCheck;

    if (elapsed >= 1000) {
      // Каждую секунду
      this.metrics.updatesPerSecond = this.frameCount;
      this.frameCount = 0;
      this.lastFpsCheck = timestamp;

      this.emitEvent("metrics-update", { ...this.metrics });
    }
  }

  /**
   * Эмитирует событие
   */
  emitEvent(type, data = null) {
    const event = new CustomEvent(type, { detail: data });
    this.events.dispatchEvent(event);
  }

  /**
   * Подписка на события плагина
   */
  addEventListener(type, callback) {
    this.events.addEventListener(type, callback);
  }

  /**
   * Отписка от событий плагина
   */
  removeEventListener(type, callback) {
    this.events.removeEventListener(type, callback);
  }

  /**
   * Получение текущих метрик
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Получение текущего состояния времени
   */
  getCurrentTimeState() {
    return {
      absoluteTime: this.pipeline.currentAbsTime,
      mediaTime: this.pipeline.video.currentTime,
      bufferStart: this.pipeline.bufferStart,
      bufferEnd: this.pipeline.bufferEnd,
      isPlaying: !this.pipeline.video.paused,
      playbackRate: this.pipeline.video.playbackRate,
      lastUpdate: this.lastEmittedTime,
    };
  }
}
