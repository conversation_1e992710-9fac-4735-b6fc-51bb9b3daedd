using System.Reactive;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraSettingDialog.PublicAccessView;

public partial class CameraPublicAccessListComponent
{
    private Guid _cameraId;
    private DateTime _lastRefreshTime = DateTime.Now;
    private GetCameraPublicAccessListUseCase.Response? _response;

    private bool _subscribing;
    private CameraPublicAccessSubscribeUseCase.Response? _subscriptionResult;

    [Parameter]
    public Guid CameraId { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (_cameraId != CameraId)
        {
            _cameraId = CameraId;

            await FetchAsync();
            await SubscribeAsync();
        }
    }

    protected async Task FetchAsync()
    {
        await SetLoadingAsync(true);
        try
        {
            _response = await ScopeFactory.MediatorSend(new GetCameraPublicAccessListUseCase.Query(CameraId));
        }
        catch (Exception ex)
        {
            _response = null;
            Snackbar.Add("Не удалось получить список публичных доступов камеры из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetLoadingAsync(false);

        if (_response is null) return;

        switch (_response.Result)
        {
            case GetCameraPublicAccessListUseCase.Result.Success:
                _lastRefreshTime = DateTime.Now;
                break;
            case GetCameraPublicAccessListUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации данных", MudBlazor.Severity.Error);
                break;
            case GetCameraPublicAccessListUseCase.Result.AccessNotFound:
                Snackbar.Add("Публичные доступы для камеры не найдены", MudBlazor.Severity.Error);
                break;
            case GetCameraPublicAccessListUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraPublicAccessListComponent), nameof(GetCameraPublicAccessListUseCase));
                Snackbar.Add($"Не удалось получить список публичных доступов камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraPublicAccessListComponent), nameof(GetCameraPublicAccessListUseCase), _response.Result);
                Snackbar.Add($"Не удалось получить список публичных доступов камеры из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task SubscribeAsync()
    {
        await SetSubscribingAsync(true);
        try
        {
            Unsubscribe();
            _subscriptionResult = await ScopeFactory.MediatorSend(new CameraPublicAccessSubscribeUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), CameraId));
        }
        catch (Exception ex)
        {
            _subscriptionResult = null;
            Snackbar.Add("Не удалось получить подписку на события списка публичных доступов камеры из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetSubscribingAsync(false);

        if (_subscriptionResult is null) return;

        switch (_subscriptionResult.Result)
        {
            case CameraPublicAccessSubscribeUseCase.Result.Success:
                CompositeDisposable.Add(_subscriptionResult.Subscription!);
                break;
            case CameraPublicAccessSubscribeUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
                break;
            case CameraPublicAccessSubscribeUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraPublicAccessListComponent), nameof(CameraPublicAccessSubscribeUseCase));
                Snackbar.Add($"Не удалось получить подписку на события списка публичных доступов камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraPublicAccessListComponent), nameof(CameraPublicAccessSubscribeUseCase), _subscriptionResult.Result);
                Snackbar.Add($"Не удалось получить подписку на события списка публичных доступов камеры из-за ошибки: {_subscriptionResult.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }

    protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [Actions]
    private Task RefreshAsync() => FetchAsync();
    #endregion

    #region [Event Handlers]
    private async void OnAppEventHandler(object appEvent)
    {
        await FetchAsync();
        await UpdateViewAsync();
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
        Logger.LogError(exc, exc.Message);
    }
    #endregion [Event Handlers]
}
