﻿.layout {
	display: grid;
    grid-template: "incident_boards incident_boards"   /* строка 1 – полноширокая */
                   "address map";      /* строка 2 – две колонки  */
    grid-template-rows: min-content min-content;
    grid-template-columns: 1fr 1fr;
    overflow: hidden;
    gap: 16px;
}

.incident_boards {
    grid-area: incident_boards;
}

::deep .address {
    height: fit-content;
    grid-area: address;
    overflow: auto;
}

::deep .map {
    grid-area: map;
    height: -webkit-fill-available;
    overflow: auto;
}

::deep .card_title {
	color: rgba(92, 97, 102, 1);
}

@media screen and (max-width: 767px) {
    .layout {
        display: flex;
        grid-template: "incident_boards"
                       "address"
                       "map";
        overflow: auto;
        flex-direction: column;
	}

    ::deep .address {
        min-height: -webkit-fill-available;
        max-height: 800px;
        overflow: auto;
        scrollbar-gutter: stable;
    }
    
    ::deep .map {
        min-height: 600px;
        max-height: 600px;
        height: 600px;
    }
}