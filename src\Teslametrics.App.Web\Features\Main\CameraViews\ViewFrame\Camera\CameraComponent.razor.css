.stream_content
{
	aspect-ratio: 16/9;
	display: flex;
	align-items: center;
	align-content: center;
	justify-content: center;
}

.empty_cell
{
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: var(--mud-palette-text-secondary);
	gap: 8px;
	height: 100%;
}

::deep .camera_card {
	border-radius: 8px !important;
}

::deep .camera_cell
{
	position: relative;
	width: 100%;
	overflow: hidden;
	height: 100%;
	display: flex;
	justify-content: center;
}

::deep .camera_name
{
    padding: 0px 8px;
    font-size: 0.875rem;
    min-height: 32px;
    display: flex;
    align-items: center;
}
.image_container
{
	aspect-ratio: 16 / 9;
}

.image_container > div {   
    border-radius: 4px;
}