namespace Teslametrics.App.Web.Services.UserDevice;

public class UserDeviceService : IUserDeviceService
{
    private readonly ILogger<UserDeviceService> _logger;

    // Данные об устройстве для текущего circuit (scoped)
    private string _userAgent = string.Empty;
    private bool _isMobile;
    private DeviceType _deviceType = DeviceType.Unknown;
    private OperatingSystem _operatingSystem = OperatingSystem.Unknown;

    public string UserAgent => _userAgent;
    public bool IsMobile => _isMobile;
    public DeviceType DeviceType => _deviceType;
    public OperatingSystem OperatingSystem => _operatingSystem;

    public UserDeviceService(ILogger<UserDeviceService> logger)
    {
        _logger = logger;
    }

    public void SetUserAgent(string userAgent)
    {
        _userAgent = userAgent;
        AnalyzeUserAgent();
        _logger.LogDebug("Обновлен User-Agent: {UserAgent}", userAgent);
    }

    public void SetMobile(bool mobile)
    {
        _isMobile = mobile;
        _logger.LogDebug("Обновлен флаг IsMobile: {IsMobile}", mobile);
    }

    public void InitializeFromHttpContext(HttpContext httpContext)
    {
        var userAgent = httpContext.Request.Headers.UserAgent.ToString();

        if (!string.IsNullOrEmpty(_userAgent))
        {
            _logger.LogWarning("Попытка повторной инициализации UserDeviceService. Текущий User-Agent: {CurrentUserAgent}, Новый User-Agent: {NewUserAgent}",
                _userAgent, userAgent);
            return;
        }

        _userAgent = userAgent;
        AnalyzeUserAgent();

        _logger.LogDebug("Инициализирована информация об устройстве: DeviceType={DeviceType}, OS={OperatingSystem}, IsMobile={IsMobile}",
            _deviceType, _operatingSystem, _isMobile);
    }

    public DeviceType GetDeviceType() => _deviceType;

    public OperatingSystem GetOperatingSystem() => _operatingSystem;

    private void AnalyzeUserAgent()
    {
        if (string.IsNullOrEmpty(_userAgent))
        {
            _deviceType = DeviceType.Unknown;
            _operatingSystem = OperatingSystem.Unknown;
            _isMobile = false;
            return;
        }

        var userAgentLower = _userAgent.ToLowerInvariant();

        // Определение операционной системы
        _operatingSystem = DetermineOperatingSystem(userAgentLower);

        // Определение типа устройства
        _deviceType = DetermineDeviceType(userAgentLower);

        // Установка флага мобильного устройства (планшеты тоже считаются мобильными)
        _isMobile = _deviceType == DeviceType.Mobile || _deviceType == DeviceType.Tablet;
    }

    private static OperatingSystem DetermineOperatingSystem(string userAgentLower)
    {
        // iOS должен проверяться перед macOS, так как iOS содержит "mac"
        if (userAgentLower.Contains("iphone") || userAgentLower.Contains("ipad") || userAgentLower.Contains("ipod"))
        {
            return OperatingSystem.iOS;
        }

        if (userAgentLower.Contains("android"))
        {
            return OperatingSystem.Android;
        }

        if (userAgentLower.Contains("windows"))
        {
            return OperatingSystem.Windows;
        }

        if (userAgentLower.Contains("macintosh") || userAgentLower.Contains("mac os"))
        {
            return OperatingSystem.macOS;
        }

        if (userAgentLower.Contains("linux") || userAgentLower.Contains("ubuntu") || userAgentLower.Contains("debian"))
        {
            return OperatingSystem.Linux;
        }

        return OperatingSystem.Unknown;
    }

    private static DeviceType DetermineDeviceType(string userAgentLower)
    {
        // Проверка на планшеты (должна быть первой, так как планшеты могут содержать "mobile")
        if (userAgentLower.Contains("ipad") ||
            userAgentLower.Contains("kindle") ||
            // Android планшеты часто содержат модель с "SM-T" (Samsung Tablet) или явно указывают "tablet"
            (userAgentLower.Contains("android") && (userAgentLower.Contains("tablet") || userAgentLower.Contains("sm-t"))))
        {
            return DeviceType.Tablet;
        }

        // Проверка на мобильные устройства
        if (userAgentLower.Contains("iphone") ||
            userAgentLower.Contains("ipod") ||
            userAgentLower.Contains("blackberry") ||
            userAgentLower.Contains("windows phone") ||
            userAgentLower.Contains("mobile") ||
            // Android мобильные устройства (если не планшет)
            (userAgentLower.Contains("android") && !userAgentLower.Contains("tablet") && !userAgentLower.Contains("sm-t")))
        {
            return DeviceType.Mobile;
        }

        // По умолчанию считаем десктопом
        return DeviceType.Desktop;
    }
}
