using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Services.UserDevice;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.Filter;

public partial class FilterComponent
{
    #region [Dialog]
    private DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Small, NoHeader = true, BackdropClick = false };
    private bool _isVisible;

    [Inject]
    private IUserDeviceService _userDeviceService { get; set; } = null!;
    #endregion

    #region [Local Filter State]
    // Локальные копии параметров для работы в диалоге
    private DateTime _localDateFrom;
    private DateTime _localDateTo;
    private Guid? _localCityId;
    private Guid? _localBuildingId;
    private Guid? _localFloorId;
    private Guid? _localRoomId;
    private Guid? _localFridgeId;
    #endregion

    private DateRange _dateRange => new(DateFrom, DateTo);
    private TimeSpan _timeFrom => DateFrom.TimeOfDay;
    private TimeSpan _timeTo => DateTo.TimeOfDay;

    [Parameter]
    public DateTime DateFrom { get; set; } = DateTime.Today;
    [Parameter]
    public EventCallback<DateTime> DateFromChanged { get; set; }

    [Parameter]
    public DateTime DateTo { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);
    [Parameter]
    public EventCallback<DateTime> DateToChanged { get; set; }

    [Parameter]
    public Guid? CityId { get; set; }
    [Parameter]
    public EventCallback<Guid?> CityIdChanged { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }
    [Parameter]
    public EventCallback<Guid?> BuildingIdChanged { get; set; }

    [Parameter]
    public Guid? FloorId { get; set; }
    [Parameter]
    public EventCallback<Guid?> FloorIdChanged { get; set; }

    [Parameter]
    public Guid? RoomId { get; set; }
    [Parameter]
    public EventCallback<Guid?> RoomIdChanged { get; set; }

    [Parameter]
    public Guid? FridgeId { get; set; }
    [Parameter]
    public EventCallback<Guid?> FridgeIdChanged { get; set; }

    protected override void OnInitialized()
    {
        if (_userDeviceService.IsMobile)
        {
            _dialogOptions = new()
            {
                CloseOnEscapeKey = true,
                FullWidth = true,
                FullScreen = true,
                NoHeader = true
            };
        }

        base.OnInitialized();
    }

    #region [Local State Management]
    private void InitializeLocalState()
    {
        _localDateFrom = DateFrom;
        _localDateTo = DateTo;
        _localCityId = CityId;
        _localBuildingId = BuildingId;
        _localFloorId = FloorId;
        _localRoomId = RoomId;
        _localFridgeId = FridgeId;
    }

    private async Task ApplyLocalStateAsync()
    {
        await UpdateParametersAsync(
            dateFrom: (_localDateFrom, DateFromChanged),
            dateTo: (_localDateTo, DateToChanged),
            cityId: (_localCityId, CityIdChanged),
            buildingId: (_localBuildingId, BuildingIdChanged),
            floorId: (_localFloorId, FloorIdChanged),
            roomId: (_localRoomId, RoomIdChanged),
            fridgeId: (_localFridgeId, FridgeIdChanged)
        );
    }

    private void OpenDialog()
    {
        InitializeLocalState();
        _isVisible = true;
    }
    #endregion

    private async Task UpdateParametersAsync<T>(params (T? value, EventCallback<T?> callback)[] updates)
    {
        var tasks = new List<Task>();

        foreach (var (value, callback) in updates)
        {
            if (callback.HasDelegate)
                tasks.Add(callback.InvokeAsync(value));
        }

        await Task.WhenAll(tasks);
        StateHasChanged();
    }

    // Перегрузка для работы с именованными параметрами
    private async Task UpdateParametersAsync(
        (DateTime DateFrom, EventCallback<DateTime> DateFromChanged)? dateFrom = null,
        (DateTime DateTo, EventCallback<DateTime> DateToChanged)? dateTo = null,
        (Guid? CityId, EventCallback<Guid?> CityIdChanged)? cityId = null,
        (Guid? BuildingId, EventCallback<Guid?> BuildingIdChanged)? buildingId = null,
        (Guid? FloorId, EventCallback<Guid?> FloorIdChanged)? floorId = null,
        (Guid? RoomId, EventCallback<Guid?> RoomIdChanged)? roomId = null,
        (Guid? FridgeId, EventCallback<Guid?> FridgeIdChanged)? fridgeId = null,
        (IncidentType? IncidentType, EventCallback<IncidentType?> IncidentTypeChanged)? incidentType = null,
        (bool? IsResolved, EventCallback<bool?> IsResolvedChanged)? isResolved = null)
    {
        var tasks = new List<Task>();

        if (dateFrom.HasValue && dateFrom.Value.DateFromChanged.HasDelegate)
            tasks.Add(dateFrom.Value.DateFromChanged.InvokeAsync(dateFrom.Value.DateFrom));

        if (dateTo.HasValue && dateTo.Value.DateToChanged.HasDelegate)
            tasks.Add(dateTo.Value.DateToChanged.InvokeAsync(dateTo.Value.DateTo));

        if (cityId.HasValue && cityId.Value.CityIdChanged.HasDelegate)
            tasks.Add(cityId.Value.CityIdChanged.InvokeAsync(cityId.Value.CityId));

        if (buildingId.HasValue && buildingId.Value.BuildingIdChanged.HasDelegate)
            tasks.Add(buildingId.Value.BuildingIdChanged.InvokeAsync(buildingId.Value.BuildingId));

        if (floorId.HasValue && floorId.Value.FloorIdChanged.HasDelegate)
            tasks.Add(floorId.Value.FloorIdChanged.InvokeAsync(floorId.Value.FloorId));

        if (roomId.HasValue && roomId.Value.RoomIdChanged.HasDelegate)
            tasks.Add(roomId.Value.RoomIdChanged.InvokeAsync(roomId.Value.RoomId));

        if (fridgeId.HasValue && fridgeId.Value.FridgeIdChanged.HasDelegate)
            tasks.Add(fridgeId.Value.FridgeIdChanged.InvokeAsync(fridgeId.Value.FridgeId));

        await Task.WhenAll(tasks);
        StateHasChanged();
    }

    private Task OnDateRangeChanged(DateRange dateRange)
    {
        DateFrom = dateRange.Start ?? new DateTime(DateFrom.Year, DateFrom.Month, DateFrom.Day, _timeFrom.Hours, _timeFrom.Minutes, 0, 0);
        if (dateRange.End is null)
        {
            DateTo = new DateTime(DateFrom.Year, DateFrom.Month, DateFrom.Day, _timeTo.Hours, _timeTo.Minutes, 59, 999);
        }
        else
        {
            DateTo = new DateTime(dateRange.End.Value.Year, dateRange.End.Value.Month, dateRange.End.Value.Day, dateRange.End.Value.Hour, dateRange.End.Value.Minute, 59, 999);
        }

        return UpdateParametersAsync(
            (DateFrom, DateFromChanged),
            (DateTo, DateToChanged)
        );

    }

    private Task OnTimeFromChanged(TimeSpan? timeFrom)
    {
        DateFrom = new DateTime(DateFrom.Year, DateFrom.Month, DateFrom.Day, timeFrom?.Hours ?? 0, timeFrom?.Minutes ?? 0, 0);

        return UpdateParametersAsync(
            (DateFrom, DateFromChanged)
        );
    }

    private Task OnTimeToChanged(TimeSpan? timeTo)
    {
        DateTo = new DateTime(DateTo.Year, DateTo.Month, DateTo.Day, timeTo?.Hours ?? 0, timeTo?.Minutes ?? 0, 59, 999);

        return UpdateParametersAsync(
            (DateTo, DateToChanged)
        );
    }

    private Task OnCityIdChanged(Guid? cityId)
    {
        CityId = cityId;
        BuildingId = null;
        FloorId = null;
        RoomId = null;
        FridgeId = null;

        return UpdateParametersAsync(
            (CityId, CityIdChanged),
            (BuildingId, BuildingIdChanged),
            (FloorId, FloorIdChanged),
            (RoomId, RoomIdChanged),
            (FridgeId, FridgeIdChanged)
        );
    }

    private async Task OnBuildingIdChanged(Guid? buildingId)
    {
        BuildingId = buildingId;
        FloorId = null;
        RoomId = null;
        FridgeId = null;

        await UpdateParametersAsync(
        (BuildingId, BuildingIdChanged),
        (FloorId, FloorIdChanged),
        (RoomId, RoomIdChanged),
        (FridgeId, FridgeIdChanged)
        );
    }

    private Task OnFloorIdChanged(Guid? floorId)
    {
        FloorId = floorId;
        RoomId = null;
        FridgeId = null;

        return UpdateParametersAsync(
            (FloorId, FloorIdChanged),
            (RoomId, RoomIdChanged),
            (FridgeId, FridgeIdChanged)
        );
    }

    private async Task OnRoomIdChanged(Guid? roomId)
    {
        RoomId = roomId;
        FridgeId = null;

        await UpdateParametersAsync(
        (RoomId, RoomIdChanged),
        (FridgeId, FridgeIdChanged)
        );
    }

    private async Task OnFridgeIdChanged(Guid? fridgeId)
    {
        FridgeId = fridgeId;

        await UpdateParametersAsync((FridgeId, FridgeIdChanged));
    }

    #region [Local Dialog Handlers]
    // Методы для работы с локальным состоянием в диалоге
    private void OnLocalCityIdChanged(Guid? cityId)
    {
        _localCityId = cityId;
        _localBuildingId = null;
        _localFloorId = null;
        _localRoomId = null;
        _localFridgeId = null;
        StateHasChanged();
    }

    private void OnLocalBuildingIdChanged(Guid? buildingId)
    {
        _localBuildingId = buildingId;
        _localFloorId = null;
        _localRoomId = null;
        _localFridgeId = null;
        StateHasChanged();
    }

    private void OnLocalFloorIdChanged(Guid? floorId)
    {
        _localFloorId = floorId;
        _localRoomId = null;
        _localFridgeId = null;
        StateHasChanged();
    }

    private void OnLocalRoomIdChanged(Guid? roomId)
    {
        _localRoomId = roomId;
        _localFridgeId = null;
        StateHasChanged();
    }

    private void OnLocalFridgeIdChanged(Guid? fridgeId)
    {
        _localFridgeId = fridgeId;
        StateHasChanged();
    }
    #endregion

    #region [Actions]
    private void Cancel()
    {
        _isVisible = false;
    }

    private async Task Save()
    {
        await ApplyLocalStateAsync();
        _isVisible = false;
    }
    #endregion
}
