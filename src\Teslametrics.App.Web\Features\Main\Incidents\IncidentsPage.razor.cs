using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.WebUtilities;
using MudBlazor;
using Teslametrics.App.Web.Services.UserDevice;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Incidents;

public partial class IncidentsPage
{
    [Inject]
    private NavigationManager NavigationManager { get; set; } = default!;

    [Inject]
    private IUserDeviceService UserDeviceService { get; set; } = null!;

    [Parameter]
    public DateTime DateFrom { get; set; } = DateTime.Today;

    [Parameter]
    public DateTime DateTo { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);
    [Parameter]
    public Guid? CityId { get; set; }
    [Parameter]
    public Guid? BuildingId { get; set; }
    [Parameter]
    public Guid? FloorId { get; set; }
    [Parameter]
    public Guid? RoomId { get; set; }
    [Parameter]
    public Guid? FridgeId { get; set; }
    [Parameter]
    public IncidentType? IncidentType { get; set; }
    [Parameter]
    public bool? IsResolved { get; set; }

    [SupplyParameterFromQuery(Name = "IncidentId")]
    public Guid? IncidentId { get; set; }

    private void OnSwipeEnd(SwipeEventArgs e)
    {
        if (e.SwipeDirection == SwipeDirection.RightToLeft && IncidentId.HasValue)
        {
            IncidentId = null;
            StateHasChanged();
            OnFilterParamsChanged();
        }
    }

    private void OnIncidendIdChanged(Guid? incidentId)
    {
        IncidentId = incidentId;
        OnFilterParamsChanged();
    }

    private void OnFilterParamsChanged()
    {
        var uri = NavigationManager.Uri;
        var uriWithoutQuery = uri.Split('?')[0];
        var queryParameters = new Dictionary<string, string?>();

        if (IncidentId.HasValue)
            queryParameters.Add("IncidentId", IncidentId.Value.ToString());

        var newUri = QueryHelpers.AddQueryString(uriWithoutQuery, queryParameters);
        NavigationManager.NavigateTo(newUri, replace: true);
    }
}
