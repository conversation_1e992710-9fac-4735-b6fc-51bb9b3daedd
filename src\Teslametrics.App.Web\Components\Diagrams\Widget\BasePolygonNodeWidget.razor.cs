using Blazor.Diagrams.Core.Geometry;
using System.Text;
using Teslametrics.App.Web.Components.Diagrams.Behaivor;
using Teslametrics.App.Web.Components.Diagrams.Models;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Components.Diagrams.Widget;

public partial class BasePolygonNodeWidget
{
	private bool isActive => (_polygonBehaivor?.IsEditInProgress ?? false) && (_polygonBehaivor.ActiveNode!.Id == Node.Id);
	protected PolygonNodeBehaivor? _polygonBehaivor;
	protected string _points => Node.VertexPoints.Select(point => $"{point.Position.X},{point.Position.Y}").Join(" "); // TODO:: Перевести в StringBuilder

	protected override void OnInitialized()
	{
		_polygonBehaivor = Diagram.GetBehavior<PolygonNodeBehaivor>();
		if (_polygonBehaivor is not null)
		{
			_polygonBehaivor.EditStarted += EditStartedhandler;
			_polygonBehaivor.EditFinished += EditStartedhandler;
		}

		base.OnInitialized();
	}

	public virtual void Dispose()
	{
		if (_polygonBehaivor is not null)
		{
			_polygonBehaivor.EditStarted -= EditStartedhandler;
			_polygonBehaivor.EditFinished -= EditStartedhandler;
		}
	}

	private void EditStartedhandler(BasePolygonNodeModel model)
	{
		if (model.Id == Node.Id)
			StateHasChanged();
	}

	private string CreateRoundedPolygonPath(double radius)
	{
		var path = new StringBuilder();
		int count = Node.VertexPoints.Count;

		for (int i = 0; i < count; i++)
		{
			Point p0 = Node.VertexPoints[(i - 1 + count) % count].Position; // предыдущая
			Point p1 = Node.VertexPoints[i].Position;                       // текущая
			Point p2 = Node.VertexPoints[(i + 1) % count].Position;         // следующая

			// Векторы
			var v1 = Normalize(p1.X - p0.X, p1.Y - p0.Y);
			var v2 = Normalize(p2.X - p1.X, p2.Y - p1.Y);

			// Точки начала и конца дуги
			var start = new Point(p1.X - v1.X * radius, p1.Y - v1.Y * radius);
			var end = new Point(p1.X + v2.X * radius, p1.Y + v2.Y * radius);

			if (i == 0)
				path.AppendFormat("M {0} {1} ", Math.Floor(start.X), Math.Floor(start.Y));
			else
				path.AppendFormat("L {0} {1} ", Math.Floor(start.X), Math.Floor(start.Y));

			// Угол и направление дуги
			int sweepFlag = CrossProductZ(v1, v2) < 0 ? 0 : 1;

			path.AppendFormat(
				"A {0} {0} 0 0 {1} {2} {3} ",
				radius, sweepFlag, Math.Floor(end.X), Math.Floor(end.Y));
		}

		path.Append("Z"); // замыкаем

		return path.ToString();
	}

	private static (double X, double Y) Normalize(double x, double y)
	{
		double len = Math.Sqrt(x * x + y * y);
		return (x / len, y / len);
	}

	private static double CrossProductZ((double X, double Y) v1, (double X, double Y) v2)
	{
		return v1.X * v2.Y - v1.Y * v2.X;
	}
}
