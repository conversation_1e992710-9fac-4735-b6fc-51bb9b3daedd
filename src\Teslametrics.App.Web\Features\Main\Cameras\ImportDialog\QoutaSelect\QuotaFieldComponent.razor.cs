using System.Linq.Expressions;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Cameras.ImportDialog.QoutaSelect;

public partial class QuotaFieldComponent
{
    #region [Parameters]
    [Parameter]
    [Category(CategoryTypes.FormComponent.Validation)]
    public Expression<Func<string?>>? For { get; set; }

    [Parameter]
    public bool Error { get; set; } = false;

    [Parameter]
    public string? ErrorText { get; set; }

    [Parameter]
    public string? Value { get; set; }

    [Parameter]
    public EventCallback<string> ValueChanged { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid OrganizationId { get; set; }
    #endregion

    private async Task<IEnumerable<string>> SearchAsync(string value, CancellationToken token)
    {
        GetPresetListUseCase.Response? response = null;
        try
        {
            await SetLoadingAsync(true);
            response = await ScopeFactory.MediatorSend(new GetPresetListUseCase.Query(OrganizationId, 0, 25, value), token);
        }
        catch (Exception exc)
        {
            response = null;
            Logger.LogError(exc, exc.Message);
            Snackbar.Add("Не удалось получить список пресетов камеры из-за непредвиденной ошибки.", Severity.Error);
        }

        if (response is null) return [];

        switch (response.Result)
        {
            case GetPresetListUseCase.Result.Success:
                return response.Items.Select(item => item.Name);
            case GetPresetListUseCase.Result.Unknown:
                Snackbar.Add("Не удалось получить список пресетов камеры из-за непредвиденной ошибки.", Severity.Error);
                Logger.LogError("Unexpected result in {Component}, {UseCase}", nameof(QuotaFieldComponent), nameof(GetPresetListUseCase));
                break;
            default:
                Snackbar.Add("Не удалось получить список пресетов камеры из-за непредвиденной ошибки.", Severity.Error);
                Logger.LogError("Unexpected result in {Component}, {UseCase}: {Result}", nameof(QuotaFieldComponent), nameof(GetPresetListUseCase), response.Result);
                break;
        }
        return [];
    }

    #region Event Handlers
    private async Task OnSelectedValudeChanged(string? preset)
    {
        Value = preset;
        if (ValueChanged.HasDelegate)
        {
            await ValueChanged.InvokeAsync(preset);
        }
    }
    #endregion
}
