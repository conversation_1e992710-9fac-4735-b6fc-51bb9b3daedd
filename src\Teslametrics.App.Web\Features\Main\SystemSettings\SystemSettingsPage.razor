@using Blazor.Diagrams.Components.Widgets
@using Blazor.Diagrams.Components
@using Teslametrics.App.Web.Features.Main.SystemSettings.SensorForm
@using Teslametrics.App.Web.Features.Main.SystemSettings.SensorList
@using Teslametrics.App.Web.Features.Main.SystemSettings.CameraList
@using Teslametrics.App.Web.Features.Main.SystemSettings.Navigation
@using Teslametrics.App.Web.Features.Main.SystemSettings.Lists
@using Teslametrics.App.Web.Features.Main.SystemSettings.Edit
@using Teslametrics.App.Web.Features.Main.SystemSettings.Diagram
@attribute [Authorize]
@attribute [Route("/system-settings")]
@inherits InteractiveBaseComponent
<PageTitle>Multimonitor | Настройки системы</PageTitle>
<!-- ... -->
<link href="_content/Z.Blazor.Diagrams/style.min.css"
	  rel="stylesheet" />
<!-- ... -->
<script src="_content/Z.Blazor.Diagrams/script.min.js"></script>
<div class="page_container">
	<!-- Навигационная цепочка -->
	<BreadcrumbNavigationComponent SelectedCity="_selectedCity"
								   SelectedBuilding="_selectedBuilding"
								   SelectedFloor="_selectedFloor"
								   SelectedRoom="_selectedRoom"
								   SelectedFreezer="_selectedFreezer"
								   OnCitySelected="OnSelectedCityChanged"
								   OnBuildingSelected="OnSelectedBuildingChanged"
								   OnFloorSelected="OnSelectedFloorChanged"
								   OnRoomSelected="OnSelectedRoomChanged"
								   OnFreezerSelected="OnSelectedFreezerChanged" />
	<MudPaper Elevation="0"
			  Class="py-4 overflow-hidden"
			  Outlined="true">
		@if (_currentSelection is null)
		{
			<!-- Список городов -->
			<CityListComponent Cities="_page.Cities"
							   SelectedCity="_selectedCity"
							   OnCitySelected="OnSelectedCityChanged"
							   OnCityAdded="@(() => Task.Run(AddCity))"
							   OnCityRemoved="@((city) => Task.Run(() => RemoveCity(city)))" />
		}
		else
		{
			@if (_currentSelection is CityModel city)
			{
				<!-- Редактирование города -->
				<EntityEditComponent Entity="city"
									 OnEntityChanged="StateHasChanged" />

				<!-- Список зданий -->
				<BuildingListComponent City="city"
									   Buildings="city.Buildings"
									   SelectedBuilding="_selectedBuilding"
									   OnBuildingSelected="OnSelectedBuildingChanged"
									   OnBuildingAdded="@(() => Task.Run(AddBuilding))"
									   OnBuildingRemoved="@((building) => Task.Run(() => RemoveBuilding(building)))" />
			}
			@if (_currentSelection is BuildingModel building)
			{
				<!-- Редактирование здания -->
				<EntityEditComponent Entity="building"
									 OnEntityChanged="StateHasChanged" />

				<!-- Список этажей -->
				<FloorListComponent Building="building"
									Floors="building.Floors"
									SelectedFloor="_selectedFloor"
									OnFloorSelected="OnSelectedFloorChanged"
									OnFloorAdded="@(() => Task.Run(AddFloor))"
									OnFloorRemoved="@((floor) => Task.Run(() => RemoveFloor(floor)))" />
			}

			@if (_currentSelection is not null && _currentSelection is not CityModel && _currentSelection is not BuildingModel && _selectedFloor is not null)
			{
				<MudTabs ApplyEffectsToContainer="true"
						 KeepPanelsAlive="true"
						 Class="mt-n4">
					<MudTabPanel Text="Настройки">
						@if (_currentSelection is FloorModel floor)
						{
							<!-- Редактирование этажа -->
							<EntityEditComponent Entity="floor"
												 OnEntityChanged="StateHasChanged" />

							<!-- Список комнат -->
							<RoomListComponent Floor="floor"
											   Building="_selectedBuilding"
											   Rooms="floor.Rooms"
											   SelectedRoom="_selectedRoom"
											   OnRoomSelected="OnSelectedRoomChanged"
											   OnRoomAdded="@(() => Task.Run(AddRoom))"
											   OnRoomRemoved="@((room) => Task.Run(() => RemoveRoom(room)))" />
						}


						@if (_currentSelection is RoomModel room)
						{
							<!-- Редактирование комнаты -->
							<EntityEditComponent Entity="room"
												 OnEntityChanged="StateHasChanged"
												 OnCameraAdded="AddCameraToRoom"
												 OnCameraRemoved="RemoveCameraFromRoom"
												 SearchCamerasFunc="SearchCamerasAsync" />

							<!-- Список холодильников -->
							<FreezerListComponent Freezers="room.Freezers"
												  SelectedFreezer="_selectedFreezer"
												  OnFreezerSelected="OnSelectedFreezerChanged"
												  OnFreezerAdded="@(() => Task.Run(AddFreezer))"
												  OnFreezerRemoved="@((freezer) => Task.Run(() => RemoveFridge(freezer)))" />
						}

						@if (_currentSelection is FreezerModel freezer)
						{
							<!-- Редактирование холодильника -->
							<EntityEditComponent Entity="freezer" />
						}

						@if (_currentSelection is BuildingModel building1)
						{
							<MudPaper Class="ma-4"
									  Outlined="true">
								<YandexMaps @bind-Coordinates="@building1.Coordinates"
											CoordinatesWithAddressChanged="OnCoordinatesWithAddressChanged"
											Width="100%"
											ReadOnly="false"
											For="@(() => building1.Coordinates)"
											Class="ma-n4 rounded-b overflow-hidden"
											Height="400px" />
							</MudPaper>
						}
					</MudTabPanel>
					<MudTabPanel Text="Редактирование плана">
						<!-- Компонент диаграммы плана этажа -->
						@if (Diagram is not null && _polygonBehaivor is not null)
						{
							<FloorPlanDiagramComponent Floor="_selectedFloor"
													   SelectedRoom="_selectedRoom as RoomModel"
													   Diagram="Diagram"
													   IsEditInProgress="_polygonBehaivor.IsEditInProgress"
													   OnFileChanged="OnInputFileChanged"
													   OnImageDeleted="DeleteImage"
													   OnRoomZoneEditStarted="() => { if (_selectedRoom is RoomModel room) StartRoomZoneEdit(room); }"
													   OnRoomZoneEditEnded="EndRoomZoneEdit" />
						}
						else
						{
							<div class="d-flex justify-center align-center"
								 style="height: 400px;">
								<MudProgressCircular Indeterminate="true" />
								<MudText Class="ml-3">Инициализация диаграммы...</MudText>
							</div>
						}
					</MudTabPanel>
				</MudTabs>
			}
		}
	</MudPaper>
	<MudStack Row="true"
			  AlignItems="AlignItems.Center"
			  Justify="Justify.FlexEnd"
			  Class="py-2">
		<MudButton OnClick="SaveAsync">Сохранить</MudButton>
	</MudStack>
</div>