using Microsoft.Maui.Handlers;

namespace Teslametrics.App.Maui.Platforms.Android
{
    public class CustomWebViewHandler : WebViewHandler
    {
        protected override void ConnectHandler(global::Android.Webkit.WebView androidWebView)
        {
            base.ConnectHandler(androidWebView);

			// Hide scrollbars completely
			androidWebView.VerticalScrollBarEnabled = false;
			androidWebView.HorizontalScrollBarEnabled = false;

			// Enable smooth scrolling
			//androidWebView.SmoothScrollingEnabled = true;

			// Configure WebView settings for mobile experience
			var settings = androidWebView.Settings;
			if (settings != null)
			{
				// Enable touch scrolling
				settings.SetSupportZoom(true);
				settings.BuiltInZoomControls = false;
				settings.DisplayZoomControls = false;

				// Optimize for mobile
				settings.UseWideViewPort = true;
				settings.LoadWithOverviewMode = true;
			}
		}
    }
}
