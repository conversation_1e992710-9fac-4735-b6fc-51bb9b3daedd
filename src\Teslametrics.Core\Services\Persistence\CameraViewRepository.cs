using Microsoft.EntityFrameworkCore;
using Teslametrics.Core.Abstractions;
using Teslametrics.Core.Domain.CameraViews;

namespace Teslametrics.Core.Services.Persistence;

public class CameraViewRepository : BaseRepository<CameraViewAggregate>, ICameraViewRepository
{
    public CameraViewRepository(CommandAppDbContext dbContext)
        : base(dbContext)
    {
    }

    public Task<bool> IsNameExistsAsync(string name,
                                        Guid organizationId,
                                        CancellationToken cancellationToken = default) =>
        DbContext.Set<CameraViewAggregate>()
            .AsNoTracking()
            .AnyAsync(entity => entity.Name == name && entity.OrganizationId == organizationId, cancellationToken);
}