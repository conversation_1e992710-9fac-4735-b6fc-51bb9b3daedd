using Blazor.Diagrams.Core;
using Blazor.Diagrams.Core.Events;
using Blazor.Diagrams.Core.Geometry;
using Blazor.Diagrams.Core.Models;
using Blazor.Diagrams.Core.Models.Base;
using Teslametrics.App.Web.Components.Diagrams.Models;

namespace Teslametrics.App.Web.Components.Diagrams.Behaivor;

public class ResizeBehaivor(Diagram diagram) : Behavior(diagram)
{
    private Size _originalSize = null!;
    private Point _originalPosition = null!;
    private Point _originalMousePosition = null!;

    protected NodeModel NodeModel = null!;
    protected ResizerControlPosition Position;

    public void OnResizeStart(NodeModel model, PointerEventArgs eventArgs, ResizerControlPosition position)
    {
        if (model.Locked) return;

        Position = position;
        NodeModel = model;

        _originalMousePosition = new Point(eventArgs.ClientX, eventArgs.ClientY);
        _originalPosition = new Point(model.Position.X, model.Position.Y);
        _originalSize = model.Size!;

        Diagram.PointerMove += OnPointerMove;
        Diagram.PointerUp += OnResizeEnd;
    }

    public override void Dispose()
    {
        Diagram.PointerMove -= OnPointerMove;
        Diagram.PointerUp -= OnResizeEnd;
    }

    protected virtual void OnPointerMove(Model? model, PointerEventArgs args)
    {
        if (NodeModel is null)
            return;

        int height = 0;
        int width = 0;

        double positionX = _originalPosition.X;
        double positionY = _originalPosition.Y;

        switch (Position)
        {
            case ResizerControlPosition.TopLeft:
                width = (int)Math.Round((_originalSize.Width + (_originalMousePosition.X - args.ClientX)));
                height = (int)Math.Round((_originalSize.Height + (_originalMousePosition.Y - args.ClientY)));

                positionY -= height - _originalSize.Height;
                positionX -= (width - _originalSize.Width);
                break;

            case ResizerControlPosition.TopRight:
                width = (int)Math.Round((_originalSize.Width - (_originalMousePosition.X - args.ClientX)));
                height = (int)Math.Round((_originalSize.Height + (_originalMousePosition.Y - args.ClientY)));

                positionY -= height - _originalSize.Height;
                break;

            case ResizerControlPosition.BottomLeft:
                width = (int)Math.Round((_originalSize.Width + (_originalMousePosition.X - args.ClientX)));
                height = (int)Math.Round((_originalSize.Height - (_originalMousePosition.Y - args.ClientY)));

                positionX -= (width - _originalSize.Width);
                break;

            case ResizerControlPosition.BottomRight:
                width = (int)Math.Round((_originalSize.Width - (_originalMousePosition.X - args.ClientX)));
                height = (int)Math.Round((_originalSize.Height - (_originalMousePosition.Y - args.ClientY)));
                break;
        }

        if (Diagram.Options.GridSize is not null) // Приводим к сетке.
        {
            width = width / Diagram.Options.GridSize.Value * Diagram.Options.GridSize.Value;
            height = height / Diagram.Options.GridSize.Value * Diagram.Options.GridSize.Value;
        }

        if (NodeModel is IHasMinimalSize HasMinimalSize)
        {
            if (width < HasMinimalSize.MinimalSize.Width)
            {
                width = (int)Math.Round(HasMinimalSize.MinimalSize.Width);
                positionX = NodeModel.Position.X;
            }

            if (height < HasMinimalSize.MinimalSize.Height)
            {
                height = (int)Math.Round(HasMinimalSize.MinimalSize.Height);
                positionY = NodeModel.Position.Y;
            }
        }
        else
        {
            if (width <= 0)
            {
                width = 32;
                positionX = NodeModel.Position.X;
            }

            if (height <= 0)
            {
                height = 32;
                positionY = NodeModel.Position.Y;
            }
        }

        if (Position != ResizerControlPosition.BottomRight)
            NodeModel.SetPosition(positionX, positionY);

        NodeModel.Size = new(width, height);
        foreach (var port in NodeModel.Ports.Where(port => port.Links.Count != 0))
        {
            port.RefreshLinks();
        }
        NodeModel.Refresh();
    }

    protected virtual void OnResizeEnd(Model? model, PointerEventArgs args)
    {
        Diagram.PointerMove -= OnPointerMove;
        Diagram.PointerUp -= OnResizeEnd;
    }
}
