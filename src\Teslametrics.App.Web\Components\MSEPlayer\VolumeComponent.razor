﻿@if (!Player.Muted || ShowForced)
{
    <div class="volume_container">
        <div class="volume_slider_container">
            <MudSlider T="float"
                       Min="0"
                       Max="1"
                       Step="0.01f"
                       Value="@Player.Volume"
                       ValueChanged="OnVolumeChangedAsync"
                       Class="volume_slider"
                       ValueLabel="true">
                <ValueLabelContent>
                    @(Math.Floor(Player.Volume * 100))%
                </ValueLabelContent>
            </MudSlider>
        </div>
        <div class="volume_button_container">
            <MudIconButton OnClick="OnMuteToggle"
                           Icon="@GetVolumeIcon()"
                           Color="@Color.Primary"
                           Size="Size.Small" />
        </div>
    </div>
}