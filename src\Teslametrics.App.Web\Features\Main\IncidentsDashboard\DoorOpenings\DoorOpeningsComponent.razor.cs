using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.DoorOpenings;

public partial class DoorOpeningsComponent : IAsyncDisposable
{
    private IJSObjectReference? _jsModule;

    private GetDoorOpeningUseCase.Response? _response = null;

    [Inject]
    private IJSRuntime JSRuntime { get; set; } = null!;

    [Parameter]
    public DateTimeOffset DateFrom { get; set; } = DateTime.Today;

    [Parameter]
    public DateTimeOffset DateTo { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);

    [Parameter]
    public Guid? CityId { get; set; }
    [Parameter]
    public Guid? BuildingId { get; set; }
    [Parameter]
    public Guid? FloorId { get; set; }
    [Parameter]
    public Guid? RoomId { get; set; }
    [Parameter]
    public Guid? FridgeId { get; set; }

    public async ValueTask DisposeAsync()
    {
        try
        {
            // Очищаем ресурсы JavaScript модуля
            if (_jsModule != null)
            {
                await _jsModule.InvokeVoidAsync("cleanupChart");
                await _jsModule.DisposeAsync();
                _jsModule = null;
            }
        }
        catch (JSDisconnectedException)
        {
            // Игнорируем ошибки отключения SignalR
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при очистке ресурсов компонента IncidentsCountComponent");
        }

        GC.SuppressFinalize(this);
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        await LoadDataAsync();
        await BuildChartsAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            try
            {
                _jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./Features/Main/IncidentsDashboard/DoorOpenings/DoorOpeningsComponent.razor.js");
                await BuildChartsAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, ex.Message);
                Snackbar.Add("Не удалось загрузить скрипт для построения графика", MudBlazor.Severity.Error);
            }
        }
    }

    private async Task BuildChartsAsync()
    {
        if (_jsModule == null) return;

        try
        {
            await _jsModule.InvokeVoidAsync("initDoorOpeningsChart", _response?.DoorOpenings);
        }
        catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
        {
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
            Snackbar.Add("Не удалось построить график на полученных данных", MudBlazor.Severity.Error);
        }
    }

    private async Task LoadDataAsync()
    {
        if (!CityId.HasValue || !BuildingId.HasValue || !FloorId.HasValue || !RoomId.HasValue || !FridgeId.HasValue) return;

        await SetLoadingAsync(true);
        try
        {
            _response = await ScopeFactory.MediatorSend(new GetDoorOpeningUseCase.Query(DateFrom, DateTo, CityId.Value, BuildingId.Value, FloorId.Value, RoomId.Value, FridgeId.Value));
        }
        catch (Exception ex)
        {
            _response = null;
            Snackbar.Add("Ошибка при получении количества открытия дверей во время запроса к серверу. Обратитесь к администратору.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetLoadingAsync(false);
        if (_response is null) return;

        switch (_response.Result)
        {
            case GetDoorOpeningUseCase.Result.Success:
                break;
            case GetDoorOpeningUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении количества открытия дверей", MudBlazor.Severity.Error);
                break;
            case GetDoorOpeningUseCase.Result.DoorSensorNotFound:
                Snackbar.Add("Нет данных для построения графика", MudBlazor.Severity.Warning);
                break;
            case GetDoorOpeningUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(DoorOpeningsComponent), nameof(GetDoorOpeningUseCase));
                Snackbar.Add($"Не удалось получить количество открытия дверей из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(DoorOpeningsComponent), nameof(GetDoorOpeningUseCase), _response.Result);
                Snackbar.Add($"Не удалось получить количество открытия дверей из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
}