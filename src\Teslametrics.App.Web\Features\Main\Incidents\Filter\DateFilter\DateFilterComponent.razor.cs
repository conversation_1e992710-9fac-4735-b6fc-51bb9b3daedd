using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Incidents.Filter.DateFilter;

public partial class DateFilterComponent
{
    private enum FilterDate
    {
        Today,
        Yesterday,
        Week,
        Month,
        Custom
    };

    private MudBlazor.DateRange _range = new(DateTime.Today, DateTime.Today.AddDays(1).AddMilliseconds(-1));
    private string _rangeText => _range?.Start?.Date == _range?.End?.Date ? $"{_range?.Start:dd.MM}" : $"{_range?.Start:dd.MM} — {_range?.End:dd.MM}";
    private FilterDate _quick = FilterDate.Today;
    private bool _dateRangeOpened = false;

    [Parameter]
    public MudBlazor.DateRange DateRange { get; set; } = new(DateTime.Today, DateTime.Today.AddDays(1).AddMilliseconds(-1));

    [Parameter]
    public EventCallback<MudBlazor.DateRange> DateRangeChanged { get; set; }

    protected override void OnInitialized()
    {
        _range = DateRange;
        UpdateQuickByRange(_range);
        base.OnInitialized();
    }

    protected override void OnParametersSet()
    {
        if (_range != DateRange)            // сравнивайте Start/End, если == не перегружен
        {
            _range = DateRange;
            UpdateQuickByRange(_range);
        }
        base.OnParametersSet();
    }

    private async Task OnDateRangeChanged(MudBlazor.DateRange range)
    {
        _quick = FilterDate.Custom;

        var newRange = range;

        if (newRange.End.HasValue)
            newRange.End = newRange.End.Value.Date.AddDays(1).AddMilliseconds(-1);

        if (DateRangeChanged.HasDelegate)
            await DateRangeChanged.InvokeAsync(newRange);

        // Закрываем меню после выбора диапазона дат (когда выбраны обе даты)
        if (newRange.Start.HasValue && newRange.End.HasValue)
        {
            _dateRangeOpened = false;
        }
    }

    private async Task OnQuickChanged(FilterDate value)
    {
        _quick = value;

        // 1.  Сохраняем время (или null, если его нет)
        TimeSpan? startTime = _range.Start?.TimeOfDay;
        TimeSpan? endTime = _range.End?.TimeOfDay;

        // 2.  Локальная функция: «дата + (возможно) время  →  DateTime?»
        static DateTime At(DateTime date, TimeSpan? time) => time.HasValue ? date.Date + time.Value : date;

        // 2.  Формируем новые даты, *сохранив* время старого диапазона
        _range = value switch
        {
            FilterDate.Today => new(
                At(DateTime.Today, startTime),
                At(DateTime.Today, endTime)),

            FilterDate.Yesterday => new(
                At(DateTime.Today.AddDays(-1), startTime),
                At(DateTime.Today.AddDays(-1), endTime)),

            // Последние 7 суток, включая сегодняшний
            FilterDate.Week => new(
                At(DateTime.Today.AddDays(-6), startTime),
                At(DateTime.Today, endTime)),

            // Последние 30 суток, включая сегодняшний
            FilterDate.Month => new(
                At(DateTime.Today.AddDays(-29), startTime),   // ← было «первый день месяца»
                At(DateTime.Today, endTime)),

            _ => _range
        };

        DateRange = _range;
        if (DateRangeChanged.HasDelegate)
            await DateRangeChanged.InvokeAsync(DateRange);
    }

    private void UpdateQuickByRange(MudBlazor.DateRange r)
    {
        // «Канонические» диапазоны
        var today = new MudBlazor.DateRange(DateTime.Today, DateTime.Today);
        var yesterday = new MudBlazor.DateRange(DateTime.Today.AddDays(-1), DateTime.Today.AddDays(-1));
        var week = new MudBlazor.DateRange(DateTime.Today.AddDays(-6), DateTime.Today);
        var month = new MudBlazor.DateRange(DateTime.Today.AddDays(-29), DateTime.Today); // ← было «первый день месяца»

        _quick = r.EqualsByDate(today) ? FilterDate.Today
              : r.EqualsByDate(yesterday) ? FilterDate.Yesterday
              : r.EqualsByDate(week) ? FilterDate.Week
              : r.EqualsByDate(month) ? FilterDate.Month
              : FilterDate.Custom;
    }

    private void OnDateRangeButtonClicked()
    {
        _dateRangeOpened = true;
    }
}
