@using Teslametrics.App.Web.Events.Cameras
@using Teslametrics.App.Web.Extensions
@using Teslametrics.App.Web.Components.DragAndDrop
@using Teslametrics.MediaServer.Orleans.Camera
@using Teslametrics.Shared
@attribute [StreamRendering(true)]
@inherits InteractiveBaseComponent
@if (_presenter is null) return;
<MudTreeViewItem @bind-Expanded="@Item.Expanded"
                 CanExpand="@Item.Expandable"
                 Items="@Item.Children"
                 Value="@Item.Value"
                 ReadOnly="true">
    <Content>
        <MudTreeViewItemToggleButton @bind-Expanded="@Item.Expanded"
                                     Visible="@Item.HasChildren" />
        <div class="d-flex flex-row align-center mud-width-full gap-2">
            <DragAndDropWrapper Value="Item"
                                Class="d-flex align-center gap-2 mx-n3 px-3">
                <MudIcon Icon="@Item.Icon"
                         Class="ml-0 mr-2" />
                <MudText Class="mud-width-full">@Item.Text</MudText>
                <MudTooltip Text="@_cameraStatusTooltipText"
                            Arrow="true"
                            Placement="Placement.Top">
                    <div class="dot @_presenter.Status"></div>
                </MudTooltip>
            </DragAndDropWrapper>
            <MudMenu Icon="@Icons.Material.Filled.MoreVert">
                @if (_presenter.Status == CameraStatus.Running || _presenter.Status == CameraStatus.Problem)
                {
                    <MudMenuItem OnClick="ShowPlayer"
                                 Icon="@Icons.Material.Outlined.PanoramaFishEye">Просмотр</MudMenuItem>
                }
                <MudMenuItem OnClick="ShowArchive"
                             Icon="@Icons.Material.Filled.FolderZip">Архив</MudMenuItem>
                <AuthorizeView Policy="@AppPermissions.Main.Cameras.Disconnect.GetEnumPermissionString()"
                               Resource="new PolicyRequirementResource(_presenter.OrganizationId, Item.Value)"
                               Context="innerContext">
                    @if (_presenter.Status == CameraStatus.Running || _presenter.Status == CameraStatus.Connecting || _presenter.Status == CameraStatus.Problem)
                    {
                        <MudMenuItem OnClick="DisconnectAsync"
                                     Icon="@Icons.Material.Filled.VisibilityOff">Отключить</MudMenuItem>
                    }
                </AuthorizeView>
                <AuthorizeView Policy="@AppPermissions.Main.Cameras.Connect.GetEnumPermissionString()"
                               Resource="new PolicyRequirementResource(_presenter.OrganizationId, Item.Value)"
                               Context="innerContext">
                    @if (_presenter.Status == CameraStatus.Stopped)
                    {
                        <MudMenuItem OnClick="ConnectAsync"
                                     Icon="@Icons.Material.Filled.Visibility">Подключить</MudMenuItem>
                    }
                </AuthorizeView>
                <AuthorizeView Policy="@AppPermissions.Main.Cameras.Update.GetEnumPermissionString()"
                               Resource="new PolicyRequirementResource(_presenter.OrganizationId, Item.Value)"
                               Context="innerContext">
                    <MudMenuItem OnClick="Edit"
                                 Icon="@Icons.Material.Outlined.Edit">Настройки камеры
                    </MudMenuItem>
                </AuthorizeView>
                <AuthorizeView Policy="@AppPermissions.Main.Cameras.Delete.GetEnumPermissionString()"
                               Resource="new PolicyRequirementResource(_presenter.OrganizationId, Item.Value)"
                               Context="innerContext">
                    <MudDivider Class="my-3" />
                    <MudMenuItem OnClick="Delete"
                                 Icon="@Icons.Material.Outlined.Delete"
                                 IconColor="Color.Warning">
                        Удалить камеру</MudMenuItem>
                </AuthorizeView>
            </MudMenu>
        </div>
    </Content>
</MudTreeViewItem>