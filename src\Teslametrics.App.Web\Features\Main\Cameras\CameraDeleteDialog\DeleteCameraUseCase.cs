using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.Cameras;
using Teslametrics.Core.Domain.Cameras.Events;
using Teslametrics.Core.Domain.PublicLinks;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.TransactionManager;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraDeleteDialog;

public static class DeleteCameraUseCase
{
    public record Command(Guid Id) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Result result)
        {
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Id).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly ICameraRepository _cameraRepository;
        private readonly IPublicLinkRepository _publicLinkRepository;
        private readonly IClusterClient _clusterClient;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       ICameraRepository cameraRepository,
                       IPublicLinkRepository publicLinkRepository,
                       IClusterClient clusterClient,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _cameraRepository = cameraRepository;
            _publicLinkRepository = publicLinkRepository;
            _clusterClient = clusterClient;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var camera = await _cameraRepository.FindAsync(request.Id, cancellationToken);

            if (camera is null)
            {
                return new Response(Result.Success);
            }

            await _cameraRepository.DeleteAsync(request.Id, cancellationToken);

            await _publicLinkRepository.DeleteByCameraIdAsync(request.Id, cancellationToken);

            await _cameraRepository.SaveChangesAsync(cancellationToken);

            List<object> events = [new CameraDeletedEvent(request.Id, camera.OrganizationId)];

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            var mediaServerGrain = _clusterClient.GetGrain<IMediaServerGrain>(Guid.Empty);
            await mediaServerGrain.DisconnectAsync(new IMediaServerGrain.CameraDisconnectRequest(request.Id));

            await transaction.CommitAsync();

            return new Response(Result.Success);
        }
    }
}