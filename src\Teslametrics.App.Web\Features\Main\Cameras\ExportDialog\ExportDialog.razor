﻿@using CsvHelper.Configuration
@using MudExtensions
@using MudExtensions.Utilities
@using Teslametrics.App.Web.Events.Cameras
@attribute [StreamRendering(true)]
@inherits InteractiveBaseComponent
<MudDialog @bind-Visible="_isVisible"
           ActionsClass="align-center justify-center"
           ContentClass="px-0 mx-0"
           Options="_dialogOptions">
    <DialogContent>
        <div class="title d-flex align-center pr-1 pl-4">
            <MudText Typo="Typo.subtitle1">Экспорт</MudText>
            <MudSpacer />
            <MudIconButton OnClick="Cancel"
                           Icon="@Icons.Material.Outlined.Close" />
        </div>
        <div class="d_contents">
            <MudTabs Elevation="0"
                     HideSlider="true"
                     ApplyEffectsToContainer="true"
                     PanelClass="px-4 pt-6"
                     TabHeaderClass="tab_header px-5"
                     TabPanelClass="tab_panel"
                     ActiveTabClass="active_tab"
                     KeepPanelsAlive="true">
                <MudTabPanel Text="Поля экспорта">
                    <MudStack Class="ml-n3 my-2">
                        <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.Name))"
                                     ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.Name))"
                                     Color="Color.Primary"
                                     Label="Название" />
                        <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.FolderName))"
                                     ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.FolderName))"
                                     Color="Color.Primary"
                                     Label="Директория" />
                        <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.Coordinates))"
                                     ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.Coordinates))"
                                     Color="Color.Primary"
                                     Label="Координаты" />
                        <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.TimeZone))"
                                     ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.TimeZone))"
                                     Color="Color.Primary"
                                     Label="Часовой пояс" />
                        <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.QuotaName))"
                                     ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.QuotaName))"
                                     Color="Color.Primary"
                                     Label="Квота" />
                        <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.ViewUri))"
                                     ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.ViewUri))"
                                     Color="Color.Primary"
                                     Label="Поток для видов" />
                        <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.PublicUri))"
                                     ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.PublicUri))"
                                     Color="Color.Primary"
                                     Label="Поток публичного доступа" />
                        <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.ArchiveUri))"
                                     ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.ArchiveUri))"
                                     Color="Color.Primary"
                                     Label="Поток архива" />
                        <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.AutoStart))"
                                     ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.AutoStart))"
                                     Color="Color.Primary"
                                     Label="Автозапуск при перезапуске системы" />
                    </MudStack>
                </MudTabPanel>
                <MudTabPanel Text="Cписок сущностей">
                    <MudDataGrid T="GetCameraListUseCase.Response.Item"
                                 Items="@_exportModels"
                                 SortMode="SortMode.None"
                                 QuickFilter="@_quickFilter"
                                 Hideable="true"
                                 ColumnResizeMode="ResizeMode.Column">
                        <ToolBarContent>
                            <MudText Typo="Typo.h6">Сущности к экспорту</MudText>
                            <MudSpacer />
                            <MudTextField @bind-Value="_searchString"
                                          Placeholder="Поиск"
                                          Adornment="Adornment.Start"
                                          Immediate="true"
                                          AdornmentIcon="@Icons.Material.Filled.Search"
                                          IconSize="Size.Medium" />
                        </ToolBarContent>
                        <Columns>
                            <PropertyColumn Property="x => x.FolderName"
                                            Title="Директория"
                                            Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.FolderName))" />
                            <PropertyColumn Property="x => x.Name"
                                            Title="Название"
                                            Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.Name))" />

                            <PropertyColumn Property="@(x => $"UTC{(x.TimeZone >= TimeSpan.Zero ? "+" : "-")}{x.TimeZone:hh\\:mm}")"
                                            Title="Часловой пояс"
                                            Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.TimeZone))"
                                            HeaderStyle="min-width: 150px;" />

                            <PropertyColumn Property="@(x => x.Coordinates.HasValue ? $"{x.Coordinates.Value.Latitude}, {x.Coordinates.Value.Longitude}" : string.Empty)"
                                            Title="Координаты"
                                            Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.Coordinates))" />

                            <PropertyColumn Property="x => x.QuotaName"
                                            Title="Квота"
                                            Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.QuotaName))" />
                            <PropertyColumn Property="x => x.PublicUri"
                                            Title="Ссылка на поток для публичного просмотра"
                                            Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.PublicUri))"
                                            HeaderStyle="min-width: 300px;" />
                            <PropertyColumn Property="x => x.ViewUri"
                                            Title="Ссылка на поток для видов"
                                            Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.ViewUri))"
                                            HeaderStyle="min-width: 300px;" />
                            <PropertyColumn Property="x => x.ArchiveUri"
                                            Title="Ссылка на поток для архива"
                                            Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.ArchiveUri))"
                                            HeaderStyle="min-width: 300px;" />
                            <PropertyColumn Property="x => x.AutoStart"
                                            Title="Автозапуск при перезапуске системы"
                                            Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.AutoStart))"
                                            HeaderStyle="min-width: 300px;">
                                <EditTemplate>
                                    <MudCheckBox T="bool"
                                                 Value="context.Item.AutoStart" />
                                </EditTemplate>
                            </PropertyColumn>
                        </Columns>
                        <PagerContent>
                            <MudDataGridPager T="GetCameraListUseCase.Response.Item" />
                        </PagerContent>
                    </MudDataGrid>
                </MudTabPanel>
            </MudTabs>
        </div>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel"
                   Variant="Variant.Outlined">Отмена</MudButton>
        <MudButton OnClick="SubmitAsync"
                   Disabled="!_exportFields.Any() || (!_response?.Items?.Any() ?? true) || IsLoading"
                   Color="Color.Primary"
                   Variant="Variant.Filled">Экспорт</MudButton>
    </DialogActions>
</MudDialog>