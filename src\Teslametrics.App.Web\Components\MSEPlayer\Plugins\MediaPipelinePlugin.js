/**
 * Базовый класс для плагинов MediaPipeline
 * Обеспечивает стандартный интерфейс для расширения функциональности плеера
 */
export class MediaPipelinePlugin {
  /**
   * @param {MediaPipeline} pipeline - экземпляр MediaPipeline
   * @param {Object} options - настройки плагина
   */
  constructor(pipeline, options = {}) {
    this.pipeline = pipeline;
    this.options = options;
    this.isActive = false;
    this.eventListeners = new Map();
  }

  /**
   * Инициализация плагина
   * Переопределяется в наследниках
   */
  initialize() {
    this.isActive = true;
  }

  /**
   * Деинициализация плагина
   * Переопределяется в наследниках
   */
  dispose() {
    this.isActive = false;
    // Отписываемся от всех событий
    for (const [event, handler] of this.eventListeners) {
      this.pipeline.ev.off(event, handler);
    }
    this.eventListeners.clear();
  }

  /**
   * Подписка на события MediaPipeline с автоматической очисткой
   * @param {string} event - название события
   * @param {Function} handler - обработчик события
   */
  on(event, handler) {
    this.pipeline.ev.on(event, handler);
    this.eventListeners.set(event, handler);
  }

  /**
   * Отписка от события
   * @param {string} event - название события
   */
  off(event) {
    const handler = this.eventListeners.get(event);
    if (handler) {
      this.pipeline.ev.off(event, handler);
      this.eventListeners.delete(event);
    }
  }
}
