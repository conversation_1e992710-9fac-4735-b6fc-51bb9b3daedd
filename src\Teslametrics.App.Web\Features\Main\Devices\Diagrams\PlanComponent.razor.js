export default class PanZoomManager {
	/** @type {Map<string, panzoom>} */
	#instances = new Map();
	/** @type {Map<string,ResizeObserver>} */
	#observers = new Map();
	/** @type {Map<string, LoadingState>} */
	#loadingStates = new Map();
	/** @type {Map<string, AbortController>} */
	#abortControllers = new Map();

	/**
	 * Инициализирует pan-zoom для контейнера
	 * @param {string} containerId - ID контейнера
	 * @param {Object} options - Опции конфигурации
	 */
	async init(containerId, { maxZoom = 4, minZoom = 0.1, padding = 20 } = {}) {
		try {
			if (this.#instances.has(containerId)) {
				console.warn(`PanZoom instance for ${containerId} already exists`);
				return;
			}

			const elements = this.#validateElements(containerId);
			if (!elements) return;

			const { container, content } = elements;

			// Показываем прелоадер
			this.#showLoader(container);

			// Создаем состояние загрузки
			this.#loadingStates.set(containerId, new LoadingState());

			// Создаем AbortController для отмены операций
			const abortController = new AbortController();
			this.#abortControllers.set(containerId, abortController);

			// Инициализируем panzoom
			const instance = await this.#initializePanZoom(container, content, {
				maxZoom,
				minZoom,
				padding,
				signal: abortController.signal,
			});

			if (!instance) return;

			this.#instances.set(containerId, instance);
			this.#setupEventListeners(
				container,
				content,
				instance,
				padding,
				abortController.signal
			);
		} catch (error) {
			this.#handleError("Initialization failed", error, containerId);
		}
	}

	/**
	 * Масштабирует к определенному элементу
	 * @param {string} containerId - ID контейнера
	 * @param {string} elementId - ID элемента для масштабирования
	 * @param {number} padding - Отступ вокруг элемента
	 */
	async zoomToElement(containerId, elementId, padding = 16) {
		try {
			const elements = this.#validateZoomElements(containerId, elementId);
			if (!elements) return;

			const { instance, container, content, element } = elements;

			// Ждем готовности изображений перед масштабированием
			await this.#waitForImagesReady(content);

			const zoomParams = this.#calculateZoomParameters(
				container,
				element,
				instance,
				padding
			);

			if (!zoomParams) return;

			await this.#performSmoothZoom(instance, zoomParams);
			instance.pause();
		} catch (error) {
			this.#handleError("Zoom to element failed", error, containerId);
		}
	}

	/**
	 * Сбрасывает масштаб к исходному состоянию
	 * @param {string} containerId - ID контейнера
	 */
	async reset(containerId) {
		try {
			const instance = this.#instances.get(containerId);
			if (!instance) {
				console.warn(`No panzoom instance found for ${containerId}`);
				return;
			}

			var paused = instance.isPaused();
			instance.pause();
			const container = document.getElementById(containerId);
			const content = container?.querySelector(".plan-content");
			if (!container || !content) {
				console.warn(`Container or content not found for ${containerId}`);
				return;
			}

			// Показываем прелоадер во время сброса
			this.#showLoader(container);

			// Отменяем предыдущие операции
			const abortController = this.#abortControllers.get(containerId);
			if (abortController) {
				abortController.abort();
			}

			// Создаем новый AbortController
			const newAbortController = new AbortController();
			this.#abortControllers.set(containerId, newAbortController);

			try {
				// Ждем готовности изображений с таймаутом
				await Promise.race([
					this.#waitForImagesReady(content, newAbortController.signal),
					this.#createTimeout(5000, "Reset timeout"),
				]);

				if (newAbortController.signal.aborted) return;

				// Выполняем улучшенную fit операцию
				await this.#performInitialFit(container, content, instance, 20);
			} finally {
				// Скрываем прелоадер
				this.#hideLoader(container);
			}
			if (paused) instance.resume();
		} catch (error) {
			this.#handleError("Reset failed", error, containerId);
		}
	}

	/**
	 * Освобождает ресурсы для контейнера
	 * @param {string} containerId - ID контейнера
	 */
	dispose(containerId) {
		try {
			// Отменяем все активные операции
			const abortController = this.#abortControllers.get(containerId);
			if (abortController) {
				abortController.abort();
				this.#abortControllers.delete(containerId);
			}

			// Отключаем наблюдатель размеров
			const observer = this.#observers.get(containerId);
			if (observer) {
				observer.disconnect();
				this.#observers.delete(containerId);
			}

			// Освобождаем panzoom instance
			const instance = this.#instances.get(containerId);
			if (instance) {
				instance.dispose();
				this.#instances.delete(containerId);
			}

			// Очищаем состояние загрузки
			this.#loadingStates.delete(containerId);
		} catch (error) {
			console.error("Dispose failed:", error);
		}
	}

	/**
	 * Возобновляет взаимодействие с pan-zoom
	 * @param {string} containerId - ID контейнера
	 */
	resume(containerId) {
		this.#instances.get(containerId)?.resume();
	}

	/**
	 * Приостанавливает взаимодействие с pan-zoom
	 * @param {string} containerId - ID контейнера
	 */
	pause(containerId) {
		this.#instances.get(containerId)?.pause();
	}

	/**
	 * Получает состояние загрузки для контейнера
	 * @param {string} containerId - ID контейнера
	 * @returns {LoadingState|null} Состояние загрузки
	 */
	getLoadingState(containerId) {
		return this.#loadingStates.get(containerId) || null;
	}

	/**
	 * Увеличивает масштаб
	 * @param {string} containerId - ID контейнера
	 * @param {number} factor - Коэффициент масштабирования (по умолчанию 1.2)
	 * @returns {Promise<void>}
	 */
	async zoomIn(containerId, factor = 1.2) {
		try {
			const instance = this.#instances.get(containerId);
			if (!instance) {
				console.warn(`No panzoom instance found for ${containerId}`);
				return;
			}

			const container = document.getElementById(containerId);
			if (!container) {
				console.warn(`Container not found for ${containerId}`);
				return;
			}

			const currentTransform = instance.getTransform();
			const newScale = Math.min(
				currentTransform.scale * factor,
				instance.getMaxZoom()
			);

			// Если уже достигнут максимальный масштаб, не выполняем операцию
			if (newScale === currentTransform.scale) {
				console.info(`Maximum zoom level reached for ${containerId}`);
				return;
			}

			await this.#performZoomToScale(instance, container, newScale);
		} catch (error) {
			this.#handleError("Zoom in failed", error, containerId);
		}
	}

	/**
	 * Уменьшает масштаб
	 * @param {string} containerId - ID контейнера
	 * @param {number} factor - Коэффициент масштабирования (по умолчанию 1.2)
	 * @returns {Promise<void>}
	 */
	async zoomOut(containerId, factor = 1.2) {
		try {
			const instance = this.#instances.get(containerId);
			if (!instance) {
				console.warn(`No panzoom instance found for ${containerId}`);
				return;
			}

			const container = document.getElementById(containerId);
			if (!container) {
				console.warn(`Container not found for ${containerId}`);
				return;
			}

			const currentTransform = instance.getTransform();
			const newScale = Math.max(
				currentTransform.scale / factor,
				instance.getMinZoom()
			);

			// Если уже достигнут минимальный масштаб, не выполняем операцию
			if (newScale === currentTransform.scale) {
				console.info(`Minimum zoom level reached for ${containerId}`);
				return;
			}

			await this.#performZoomToScale(instance, container, newScale);
		} catch (error) {
			this.#handleError("Zoom out failed", error, containerId);
		}
	}

	/* ---------- Private Helper Methods ---------- */

	/**
	 * Валидирует основные элементы для инициализации
	 * @param {string} containerId - ID контейнера
	 * @returns {Object|null} Объект с элементами или null при ошибке
	 */
	#validateElements(containerId) {
		const container = document.getElementById(containerId);
		const content = container?.querySelector(".plan-content");

		if (!container || !content) {
			console.error(`Container or content not found for ${containerId}`);
			return null;
		}

		return { container, content };
	}

	/**
	 * Валидирует элементы для операции масштабирования
	 * @param {string} containerId - ID контейнера
	 * @param {string} elementId - ID элемента
	 * @returns {Object|null} Объект с элементами или null при ошибке
	 */
	#validateZoomElements(containerId, elementId) {
		const instance = this.#instances.get(containerId);
		const container = document.getElementById(containerId);
		const content = container?.querySelector(".plan-content");
		const element = document.querySelector(`[data-id="${elementId}"]`);

		if (!instance || !container || !content || !element) {
			console.error("Instance, container, content, or element not found", {
				containerId,
				elementId,
				hasInstance: !!instance,
				hasContainer: !!container,
				hasContent: !!content,
				hasElement: !!element,
			});
			return null;
		}

		return { instance, container, content, element };
	}

	/**
	 * Инициализирует panzoom instance
	 * @param {HTMLElement} container - Контейнер
	 * @param {HTMLElement} content - Контент
	 * @param {Object} options - Опции
	 * @returns {Promise<Object|null>} panzoom instance или null
	 */
	async #initializePanZoom(
		container,
		content,
		{ maxZoom, minZoom, padding, signal }
	) {
		const containerId = container.id;
		const loadingState = this.#loadingStates.get(containerId);

		try {
			if (loadingState) {
				loadingState.startLoading();
			}

			// Ждем готовности изображений с таймаутом
			await Promise.race([
				this.#waitForImagesReady(content, signal),
				this.#createTimeout(10000, "Image loading timeout"), // 10 секунд таймаут
			]);

			if (signal?.aborted) return null;

			// Создаем panzoom instance
			const instance = panzoom(content, { maxZoom, minZoom, bounds: false });

			// Ждем следующий кадр для стабилизации DOM
			await this.#nextFrame();

			// Выполняем начальную подгонку с проверкой готовности
			await this.#performInitialFit(container, content, instance, padding);

			if (loadingState) {
				loadingState.finishLoading();
			}

			return instance;
		} catch (error) {
			if (loadingState) {
				loadingState.addError(error);
				loadingState.finishLoading();
			}

			if (
				error.name !== "AbortError" &&
				error.message !== "Image loading timeout"
			) {
				console.error("PanZoom initialization failed:", error);
			}
			return null;
		}
	}

	/**
	 * Настраивает обработчики событий
	 * @param {HTMLElement} container - Контейнер
	 * @param {HTMLElement} content - Контент
	 * @param {Object} instance - panzoom instance
	 * @param {number} padding - Отступ
	 * @param {AbortSignal} signal - Сигнал отмены
	 */
	#setupEventListeners(container, content, instance, padding, signal) {
		const fit = () => {
			if (signal?.aborted) return;
			this.#fit(container, content, instance, padding);
		};

		// ResizeObserver для отслеживания изменений размера
		const resizeObserver = new ResizeObserver(fit);
		resizeObserver.observe(container);
		this.#observers.set(container.id, resizeObserver);

		// Double-click для сброса
		const handleDoubleClick = () => {
			if (signal?.aborted) return;
			fit();
		};

		container.addEventListener("dblclick", handleDoubleClick);

		// Очистка при отмене
		if (signal) {
			signal.addEventListener("abort", () => {
				resizeObserver.disconnect();
				container.removeEventListener("dblclick", handleDoubleClick);
			});
		}

		// Скрываем прелоадер после инициализации
		this.#hideLoader(container);
	}

	/**
	 * Ожидает готовности всех изображений в контенте
	 * @param {HTMLElement} content - Контент для проверки
	 * @param {AbortSignal} signal - Сигнал отмены
	 * @returns {Promise<void>}
	 */
	async #waitForImagesReady(content, signal = null) {
		const images = content.querySelectorAll("img");
		if (images.length === 0) return;

		const imagePromises = Array.from(images).map((img) => {
			return new Promise((resolve, reject) => {
				if (signal?.aborted) {
					reject(new Error("Aborted"));
					return;
				}

				if (img.complete && img.naturalWidth > 0) {
					resolve();
					return;
				}

				const onLoad = () => {
					cleanup();
					resolve();
				};

				const onError = () => {
					cleanup();
					reject(new Error(`Failed to load image: ${img.src}`));
				};

				const onAbort = () => {
					cleanup();
					reject(new Error("Aborted"));
				};

				const cleanup = () => {
					img.removeEventListener("load", onLoad);
					img.removeEventListener("error", onError);
					signal?.removeEventListener("abort", onAbort);
				};

				img.addEventListener("load", onLoad);
				img.addEventListener("error", onError);
				signal?.addEventListener("abort", onAbort);
			});
		});

		try {
			await Promise.all(imagePromises);
		} catch (error) {
			if (error.message !== "Aborted") {
				console.warn("Some images failed to load:", error);
			}
			throw error;
		}
	}

	/**
	 * Вычисляет параметры для масштабирования к элементу
	 * @param {HTMLElement} container - Контейнер
	 * @param {HTMLElement} element - Элемент для масштабирования
	 * @param {Object} instance - panzoom instance
	 * @param {number} padding - Отступ
	 * @returns {Object|null} Параметры масштабирования
	 */
	#calculateZoomParameters(container, element, instance, padding) {
		try {
			const containerRect = container.getBoundingClientRect();
			const transform = instance.getTransform();

			const elementWidth = element.offsetWidth;
			const elementHeight = element.offsetHeight;
			const elementLeft = element.offsetLeft;
			const elementTop = element.offsetTop;

			if (elementWidth === 0 || elementHeight === 0) {
				console.warn("Element has zero dimensions");
				return null;
			}

			// Рассчитаем масштаб, чтобы элемент с padding влез в контейнер
			const zoomX = (containerRect.width - padding * 2) / elementWidth;
			const zoomY = (containerRect.height - padding * 2) / elementHeight;
			const targetScale = Math.min(zoomX, zoomY, instance.getMaxZoom());

			// Центр элемента внутри content
			const elementCenterX = elementLeft + elementWidth / 2;
			const elementCenterY = elementTop + elementHeight / 2;

			// Куда сдвинуть, чтобы элемент оказался по центру контейнера
			const scaledElementCenterX = elementCenterX * targetScale;
			const scaledElementCenterY = elementCenterY * targetScale;

			const newX = containerRect.width / 2 - scaledElementCenterX;
			const newY = containerRect.height / 2 - scaledElementCenterY;

			return {
				start: {
					scale: transform.scale,
					x: transform.x,
					y: transform.y,
				},
				end: {
					scale: targetScale,
					x: newX,
					y: newY,
				},
			};
		} catch (error) {
			console.error("Failed to calculate zoom parameters:", error);
			return null;
		}
	}

	/**
	 * Выполняет плавное масштабирование
	 * @param {Object} instance - panzoom instance
	 * @param {Object} zoomParams - Параметры масштабирования
	 * @param {number} duration - Длительность анимации
	 * @returns {Promise<void>}
	 */
	async #performSmoothZoom(instance, zoomParams, duration = 400) {
		return new Promise((resolve) => {
			const { start, end } = zoomParams;
			const startTime = performance.now();

			const animate = (time) => {
				const elapsed = time - startTime;
				const progress = Math.min(elapsed / duration, 1);

				// Easing function для более плавной анимации
				const easeProgress = 1 - Math.pow(1 - progress, 3);

				const currentScale =
					start.scale + (end.scale - start.scale) * easeProgress;
				const currentX = start.x + (end.x - start.x) * easeProgress;
				const currentY = start.y + (end.y - start.y) * easeProgress;

				instance.zoomAbs(0, 0, currentScale);
				instance.moveTo(currentX, currentY);

				if (progress < 1) {
					requestAnimationFrame(animate);
				} else {
					resolve();
				}
			};

			requestAnimationFrame(animate);
		});
	}

	/**
	 * Показывает прелоадер
	 * @param {HTMLElement} container - Контейнер
	 */
	#showLoader(container) {
		const loader = container.querySelector(".plan-loader");
		if (loader) {
			loader.style.display = "flex";
			loader.classList.remove("hidden");
		}
	}

	/**
	 * Скрывает прелоадер
	 * @param {HTMLElement} container - Контейнер
	 */
	#hideLoader(container) {
		const loader = container.querySelector(".plan-loader");
		if (loader) {
			loader.classList.add("hidden");
			// Полностью скрываем элемент после анимации
			setTimeout(() => {
				if (loader.classList.contains("hidden")) {
					loader.style.display = "none";
				}
			}, 300); // Соответствует времени CSS transition
		}
	}

	/**
	 * Выполняет операцию fit (подгонка изображения под контейнер)
	 * @param {HTMLElement} container - Контейнер
	 * @param {HTMLElement} content - Контент
	 * @param {Object} instance - panzoom instance
	 * @param {number} padding - Отступ
	 */
	#fit(container, content, instance, padding) {
		try {
			const { width: cw, height: ch } = container.getBoundingClientRect();
			const img = content.querySelector("img");
			const w = img?.naturalWidth || content.offsetWidth;
			const h = img?.naturalHeight || content.offsetHeight;

			if (!w || !h) {
				console.warn("Cannot fit: invalid dimensions", { w, h });
				return;
			}

			const scale = Math.min(
				(cw - 2 * padding) / w,
				(ch - 2 * padding) / h,
				instance.getMaxZoom()
			);

			const x = (cw - w * scale) / 2;
			const y = (ch - h * scale) / 2;

			instance.zoomAbs(0, 0, scale);
			instance.moveTo(x, y);
		} catch (error) {
			console.error("Fit operation failed:", error);
		}
	}

	/**
	 * Обрабатывает ошибки
	 * @param {string} message - Сообщение об ошибке
	 * @param {Error} error - Объект ошибки
	 * @param {string} containerId - ID контейнера
	 */
	#handleError(message, error, containerId) {
		console.error(`${message} for container ${containerId}:`, error);

		const loadingState = this.#loadingStates.get(containerId);
		if (loadingState) {
			loadingState.addError(error);
			loadingState.finishLoading();
		}

		// Скрываем прелоадер при ошибке
		const container = document.getElementById(containerId);
		if (container) {
			this.#hideLoader(container);
		}
	}

	/**
	 * Создает промис с таймаутом
	 * @param {number} ms - Время в миллисекундах
	 * @param {string} message - Сообщение об ошибке
	 * @returns {Promise<never>}
	 */
	#createTimeout(ms, message) {
		return new Promise((_, reject) => {
			setTimeout(() => reject(new Error(message)), ms);
		});
	}

	/**
	 * Ожидает следующий кадр анимации
	 * @returns {Promise<void>}
	 */
	#nextFrame() {
		return new Promise((resolve) => requestAnimationFrame(resolve));
	}

	/**
	 * Выполняет начальную подгонку с проверками
	 * @param {HTMLElement} container - Контейнер
	 * @param {HTMLElement} content - Контент
	 * @param {Object} instance - panzoom instance
	 * @param {number} padding - Отступ
	 * @returns {Promise<void>}
	 */
	async #performInitialFit(container, content, instance, padding) {
		// Ждем стабилизации размеров
		let attempts = 0;
		const maxAttempts = 5;

		while (attempts < maxAttempts) {
			const { width: cw, height: ch } = container.getBoundingClientRect();
			const img = content.querySelector("img");
			const w = img?.naturalWidth || content.offsetWidth;
			const h = img?.naturalHeight || content.offsetHeight;

			if (cw > 0 && ch > 0 && w > 0 && h > 0) {
				this.#fit(container, content, instance, padding);
				return;
			}

			attempts++;
			await new Promise((resolve) => setTimeout(resolve, 50)); // Ждем 50мс
		}

		console.warn("Failed to perform initial fit after maximum attempts");
	}

	/**
	 * Выполняет масштабирование к определенному уровню относительно центра контейнера
	 * @param {Object} instance - panzoom instance
	 * @param {HTMLElement} container - Контейнер
	 * @param {number} targetScale - Целевой масштаб
	 * @param {number} duration - Длительность анимации
	 * @returns {Promise<void>}
	 */
	async #performZoomToScale(instance, container, targetScale, duration = 300) {
		const currentTransform = instance.getTransform();
		const containerRect = container.getBoundingClientRect();

		// Центр контейнера
		const centerX = containerRect.width / 2;
		const centerY = containerRect.height / 2;

		// Вычисляем новую позицию для сохранения центрирования
		const scaleDiff = targetScale / currentTransform.scale;
		const newX = centerX - (centerX - currentTransform.x) * scaleDiff;
		const newY = centerY - (centerY - currentTransform.y) * scaleDiff;

		const zoomParams = {
			start: {
				scale: currentTransform.scale,
				x: currentTransform.x,
				y: currentTransform.y,
			},
			end: {
				scale: targetScale,
				x: newX,
				y: newY,
			},
		};

		await this.#performSmoothZoom(instance, zoomParams, duration);
	}
}

/**
 * Класс для отслеживания состояния загрузки
 */
class LoadingState {
	constructor() {
		this.isLoading = false;
		this.loadStartTime = null;
		this.errors = [];
	}

	startLoading() {
		this.isLoading = true;
		this.loadStartTime = performance.now();
		this.errors = [];
	}

	finishLoading() {
		this.isLoading = false;
		this.loadStartTime = null;
	}

	addError(error) {
		this.errors.push({
			error,
			timestamp: performance.now(),
		});
	}

	getLoadDuration() {
		return this.loadStartTime ? performance.now() - this.loadStartTime : 0;
	}
}

// --- Единый менеджер, экспортируем функции для .NET interop ----------
const manager = new PanZoomManager();

/**
 * Инициализирует pan-zoom для контейнера
 * @param {string} containerId - ID контейнера
 * @param {Object} opts - Опции конфигурации
 * @returns {Promise<void>}
 */
export async function init(containerId, opts) {
	try {
		// Загружаем библиотеку panzoom если она еще не загружена
		if (!window.panzoom) {
			await import(
				"https://cdn.jsdelivr.net/npm/panzoom@9.4.3/dist/panzoom.min.js"
			);
		}

		// Инициализируем менеджер
		await manager.init(containerId, opts);
	} catch (error) {
		console.error("Failed to initialize PanZoom:", error);
		throw error;
	}
}

/**
 * Масштабирует к определенному элементу
 * @param {string} containerId - ID контейнера
 * @param {string} id - ID элемента
 * @returns {Promise<void>}
 */
export async function zoomToElement(containerId, id) {
	try {
		await manager.zoomToElement(containerId, id);
	} catch (error) {
		console.error("Failed to zoom to element:", error);
	}
}

/**
 * Сбрасывает масштаб к исходному состоянию
 * @param {string} containerId - ID контейнера
 * @returns {Promise<void>}
 */
export async function reset(containerId) {
	try {
		await manager.reset(containerId);
	} catch (error) {
		console.error("Failed to reset zoom:", error);
	}
}

/**
 * Освобождает ресурсы для контейнера
 * @param {string} containerId - ID контейнера
 */
export function dispose(containerId) {
	try {
		manager.dispose(containerId);
	} catch (error) {
		console.error("Failed to dispose PanZoom:", error);
	}
}

/**
 * Возобновляет взаимодействие с pan-zoom
 * @param {string} containerId - ID контейнера
 */
export function unlock(containerId) {
	try {
		manager.resume(containerId);
	} catch (error) {
		console.error("Failed to unlock PanZoom:", error);
	}
}

/**
 * Приостанавливает взаимодействие с pan-zoom
 * @param {string} containerId - ID контейнера
 */
export function lock(containerId) {
	try {
		manager.pause(containerId);
	} catch (error) {
		console.error("Failed to lock PanZoom:", error);
	}
}

/**
 * Получает информацию о состоянии загрузки
 * @param {string} containerId - ID контейнера
 * @returns {Object|null} Состояние загрузки
 */
export function getLoadingState(containerId) {
	return manager.getLoadingState?.(containerId) || null;
}

/**
 * Увеличивает масштаб
 * @param {string} containerId - ID контейнера
 * @returns {Promise<void>}
 */
export async function zoomIn(containerId) {
	try {
		await manager.zoomIn(containerId);
	} catch (error) {
		console.error("Failed to zoom in:", error);
	}
}

/**
 * Уменьшает масштаб
 * @param {string} containerId - ID контейнера
 * @returns {Promise<void>}
 */
export async function zoomOut(containerId) {
	try {
		await manager.zoomOut(containerId);
	} catch (error) {
		console.error("Failed to zoom out:", error);
	}
}
