@using Teslametrics.App.Web.Components.DragAndDrop
@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<div class="mud-height-full sidebar tree">
	@if (IsLoading && _items.Count == 0)
	{
		@for (var i = 0; i < Random.Shared.Next(3, 8); i++)
		{
			<MudStack Row="true"
					  Class="px-4 py-1">
				<MudSkeleton SkeletonType="SkeletonType.Circle"
							 Height="24px"
							 Width="24px"
							 Class="mt-1" />
				<MudSkeleton Width="@($"{Random.Shared.Next(30, 70)}%")"
							 Height="40px" />
			</MudStack>

		}

	}
	<NoItemsFoundComponent HasItems="IsLoading || _items.Count > 0"
						   LastRefreshTime="_lastRefreshTime" />
	@if (!IsLoading && _items.Count > 0)
	{
		<MudTreeView T="Guid"
					 Items="@_items"
					 SelectionMode="SelectionMode.SingleSelection"
					 Class="tree overflow-auto"
					 SelectedValue="@(ViewId is null ? (OrganizationId ?? Guid.Empty) : ViewId.Value)"
					 AutoExpand="true">
			<ItemTemplate Context="Item">
				@switch (Item)
				{
					case OrganizationTreeItemPresenter.OrganizationItemPresenter orgPresenter:
						<OrganizationTreeItemPresenter Presenter="orgPresenter"
													   @key="orgPresenter.Value" />
						break;
					case ViewTreeItemPresenter.ViewItemPresenter viewPresenter:
						<ViewTreeItemPresenter Presenter="viewPresenter"
											   OnSelect="SelectViewAsync"
											   @key="viewPresenter.Value" />
						break;
				}
			</ItemTemplate>
		</MudTreeView>
	}
	<MudStack Row="true"
			  Class="mb-4 pl-4 pt-2"
			  Style="min-height: 36px;"
			  AlignItems="AlignItems.Center">
		<div class="d-flex flex-row align-center bg3 mud-width-full mud-height-full justify-center br_8">
			<MudText Typo="Typo.caption">Последнее обновление: @_lastRefreshTime.ToLocalTime()</MudText>
		</div>
		<div class="d-flex flex-row align-center gap-3">
			<MudIconButton OnClick="RefreshAsync"
						   Icon="@Icons.Material.Outlined.Refresh"
						   Disabled="IsLoading"
						   Color="Color.Primary"
						   Size="Size.Medium"
						   Variant="Variant.Filled" />
			<SubscriptionErrorComponent Show="@(!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))"
										RetrySubscribe="SubscribeAsync" />
		</div>
	</MudStack>
</div>