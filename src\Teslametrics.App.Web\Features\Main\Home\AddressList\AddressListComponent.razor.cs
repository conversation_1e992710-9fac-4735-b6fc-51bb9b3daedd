using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Reactive;
using System.Reactive.Linq;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Home.AddressList;

public partial class AddressListComponent
{
    private bool _subscribing;
    private SubscribeAddressListUseCase.Response? _subscribeResponse;
    GetAddressListUseCase.Response? _response;
    private IReadOnlyCollection<GetAddressListUseCase.Response.Building> _selected = [];

    [Inject]
    private NavigationManager NavigationManager { get; set; } = null!;

    [Parameter]
    public IEnumerable<Guid> Selected { get; set; } = [];

    [Parameter]
    public EventCallback<IEnumerable<Guid>> SelectedChanged { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await FetchDataAsync();
        await SubscribeToUpdatesAsync();
    }

    private async Task FetchDataAsync()
    {
        await SetLoadingAsync(true);
        try
        {
            _response = await ScopeFactory.MediatorSend(new GetAddressListUseCase.Query());
        }
        catch (Exception ex)
        {
            _response = null;
            Logger.LogError(ex, "Ошибка при загрузке списка адресов");
            Snackbar.Add("Произошла ошибка при загрузке списка адресов", Severity.Error);
        }

        await SetLoadingAsync(false);
        if (_response is null) return;
        switch (_response.Result)
        {
            case GetAddressListUseCase.Result.Success:
                _selected = _response.Cities.SelectMany(c => c.Buildings).Where(b => Selected.Contains(b.Id)).ToList();
                break;
            case GetAddressListUseCase.Result.PlanNotFound:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(AddressListComponent), nameof(GetAddressListUseCase), _response.Result);
                Snackbar.Add($"Не удалось получить список адресов из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
            case GetAddressListUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(AddressListComponent), nameof(GetAddressListUseCase));
                Snackbar.Add($"Не удалось получить список адресов из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(AddressListComponent), nameof(GetAddressListUseCase), _response.Result);
                Snackbar.Add($"Не удалось получить список адресов из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task SubscribeToUpdatesAsync()
    {
        if (_response is null || !_response.IsSuccess || _subscribing) return;
        try
        {
            Unsubscribe();
            await SetSubscribingAsync(true);
            _subscribeResponse = await ScopeFactory.MediatorSend(new SubscribeAddressListUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError)));
        }
        catch (Exception ex)
        {
            _subscribeResponse = null;
            Snackbar.Add($"Не удалось подписаться на события списка этажей из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
        finally
        {
            await SetSubscribingAsync(false);
        }

        if (_subscribeResponse is null) return;

        switch (_subscribeResponse.Result)
        {
            case SubscribeAddressListUseCase.Result.Success:
                CompositeDisposable.Add(_subscribeResponse.Subscription!);
                break;
            case SubscribeAddressListUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события списка адресов", MudBlazor.Severity.Error);
                break;
            case SubscribeAddressListUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(AddressListComponent), nameof(SubscribeAddressListUseCase));
                Snackbar.Add($"Не удалось подписаться на события списка адресов из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(AddressListComponent), nameof(SubscribeAddressListUseCase), _subscribeResponse.Result);
                Snackbar.Add($"Не удалось подписаться на события списка адресов из-за ошибки: {_subscribeResponse.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task OnBuildingSelected(IEnumerable<GetAddressListUseCase.Response.Building> selected)
    {
        _selected = [.. selected];
        Selected = _selected.Select(x => x.Id);
        if (SelectedChanged.HasDelegate)
            await SelectedChanged.InvokeAsync(Selected);
    }

    private async void OnCitySelected(GetAddressListUseCase.Response.City city)
    {
        if (city.Buildings.All(item => _selected.Contains(item)))
        {
            _selected = _selected.Except(city.Buildings).ToList();
        }
        else
        {
            _selected = _selected.Union(city.Buildings).ToList();
        }
        Selected = _selected.Select(x => x.Id);
        if (SelectedChanged.HasDelegate)
            await SelectedChanged.InvokeAsync(Selected);
    }

    private void NavigateToDeviceList(GetAddressListUseCase.Response.City city, GetAddressListUseCase.Response.Building building)
    {
        NavigationManager.NavigateTo($"/devices?cityId={city.Id}&buildingId={building.Id}");
    }
    private void Unsubscribe()
    {
        if (_subscribeResponse?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscribeResponse.Subscription);
            _subscribeResponse.Subscription.Dispose();
        }
    }

    protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [Event Handlers]
    private async void OnAppEventHandler(object appEvent)
    {
        // В любом случае обновляем данные
        await InvokeAsync(async () =>
        {
            await FetchDataAsync();
            StateHasChanged();
        });
    }

    private void OnError(Exception ex)
    {
        Logger.LogError(ex, "Ошибка в подписке на обновления списка адресов");
        Snackbar.Add("Произошла ошибка при получении обновлений списка адресов", Severity.Error);
    }
    #endregion [Event Handlers]
}
