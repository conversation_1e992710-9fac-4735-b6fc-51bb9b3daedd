﻿@using CsvHelper.Configuration
@using Microsoft.AspNetCore.Components.Forms
@using System.Text
@using System.Globalization

<MudFileUpload T="IBrowserFile"
			   @ref="@_fileUpload"
			   OnFilesChanged="OnInputFileChanged"
			   AppendMultipleFiles
			   Hidden="false"
			   Class="flex-1 d-flex justify-center align-content-center my-0"
			   InputClass="absolute mud-width-full mud-height-full d-flex justify-center align-content-center overflow-hidden z-1"
			   InputStyle="opacity:0"
			   @ondrop="@ClearDragClass"
			   @ondragenter="@SetDragClass"
			   @ondragleave="@ClearDragClass"
			   @ondragend="@ClearDragClass">
	<ActivatorContent>
		<MudPaper Height="300px"
				  Outlined="true"
				  Class="@DragClass">
			<MudStack AlignItems="AlignItems.Center">
				<MudIcon Icon="@Icons.Material.Outlined.CloudUpload"
						 Title="Favorite"
						 Style="font-size: 6rem;" />
				<MudStack Row="@Row"
						  Justify="Justify.Center"
						  AlignItems="AlignItems.Center"
						  Class="@LabelClass">
					<MudFab HtmlTag="label"
							Class="z-2"
							Color="Color.Secondary"
							Label="@UploadFileButtonText"
							StartIcon="@Icons.Material.Outlined.CloudUpload"
							Size="Size.Large" />
					<MudText Typo="Typo.h6">
						@UploadFileContainerText
					</MudText>
				</MudStack>
			</MudStack>
		</MudPaper>
	</ActivatorContent>
</MudFileUpload>
