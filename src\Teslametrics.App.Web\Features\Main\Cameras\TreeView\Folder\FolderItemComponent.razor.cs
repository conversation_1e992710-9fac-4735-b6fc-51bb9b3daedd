using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Cameras.TreeView.Folder;

public partial class FolderItemComponent
{
	private DirectoryTreeViewComponent.TreeItemPresenter? _presenter => Item as DirectoryTreeViewComponent.TreeItemPresenter;
	private bool _selected => SelectedFolderId is not null && SelectedFolderId == _presenter?.Id;

	#region [Parameters]
	[Parameter]
	[EditorRequired]
	public TreeItemData<Guid> Item { get; set; } = null!;

	[Parameter]
	public Guid? SelectedFolderId { get; set; }

	[Parameter]
	[EditorRequired]
	public EventCallback<TreeItemData<Guid>> OnSelect { get; set; }
	#endregion [Parameters]

	protected override bool ShouldRender()
	{
		if (_presenter is null)
			return false;

		return base.ShouldRender();
	}

	private async Task SelectHandler()
	{
		if (OnSelect.HasDelegate)
			await OnSelect.InvokeAsync(Item);
	}

	private void Create()
	{
		if (_presenter is null) return;
		EventSystem.Publish(new CameraCreateEto(_presenter.OrganizationId, Item.Value));
	}

	private void Delete()
	{
		if (_presenter is null) return;
		EventSystem.Publish(new CameraGroupDeleteEto(_presenter.OrganizationId, Item.Value));
	}

	private void Edit()
	{
		if (_presenter is null) return;
		EventSystem.Publish(new FolderUpdateEto(_presenter.OrganizationId, Item.Value));
	}

	private async Task DisconnectAsync()
	{
		if (IsLoading) return;

		DisconnectCameraListUseCase.Response? response;
		try
		{
			await SetLoadingAsync(true);
			response = await ScopeFactory.MediatorSend(new DisconnectCameraListUseCase.Command(Item.Value));
		}
		catch (Exception ex)
		{
			response = null;
			Snackbar.Add($"Не удалось отключить камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetLoadingAsync(false);

		if (response is null) return;
		switch (response.Result)
		{
			case DisconnectCameraListUseCase.Result.Success:
				Snackbar.Add("Отправлен запрос на отключение камер", MudBlazor.Severity.Success);
				break;
			case DisconnectCameraListUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось отключить камеры из-за ошибки валидации", MudBlazor.Severity.Error);
				break;
			case DisconnectCameraListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FolderItemComponent), nameof(DisconnectCameraListUseCase));
				Snackbar.Add($"Не удалось отключить камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(FolderItemComponent), nameof(DisconnectCameraListUseCase), response.Result);
				Snackbar.Add($"Не удалось отключить камеры из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private async Task ConnectAsync()
	{
		if (IsLoading) return;

		ConnectCameraListUseCase.Response? response;
		try
		{
			await SetLoadingAsync(true);
			response = await ScopeFactory.MediatorSend(new ConnectCameraListUseCase.Command(Item.Value));
		}
		catch (Exception ex)
		{
			response = null;
			Snackbar.Add($"Не удалось подключить камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetLoadingAsync(false);

		if (response is null) return;
		switch (response.Result)
		{
			case ConnectCameraListUseCase.Result.Success:
				Snackbar.Add("Отправлен запрос на подключение камер", MudBlazor.Severity.Success);
				break;
			case ConnectCameraListUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось подключить камеры из-за ошибки валидации", MudBlazor.Severity.Error);
				break;
			case ConnectCameraListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FolderItemComponent), nameof(ConnectCameraListUseCase));
				Snackbar.Add($"Не удалось подключить камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(FolderItemComponent), nameof(ConnectCameraListUseCase), response.Result);
				Snackbar.Add($"Не удалось подключить камеры из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;
		}
	}
}
