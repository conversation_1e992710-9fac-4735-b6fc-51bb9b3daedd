﻿@inherits InteractiveBaseComponent
<MudAutocomplete T="GetCityListUseCase.Response.Item"
                 SearchFunc="@SearchCityAsync"
                 ToStringFunc="@(e => e == null ? null : e.Name)"
                 Value="@_selectedCity"
                 ValueChanged="@CityValueChanged"
                 Label="Город"
                 Margin="Margin.Dense"
                 Clearable="true"
                 ResetValueOnEmptyText="true"
                 Variant="Variant.Outlined" />