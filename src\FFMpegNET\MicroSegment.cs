namespace FFMpegNET;

/// <summary>
/// Микросегмент видео - короткий сегмент, начинающийся с I-кадра
/// </summary>
public class MicroSegment
{
    public byte[]? Payload { get; set; }
    public DateTimeOffset StartTime { get; set; }
    public double Duration { get; set; }

    public MicroSegment()
    {
    }

    public MicroSegment(byte[] payload, DateTimeOffset startTime, double duration)
    {
        Payload = payload;
        StartTime = startTime;
        Duration = duration;
    }
}
