using System.Data;
using System.Text.Json;
using Dapper;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Devices.Breadcrumbs;

public static class GetBreadcrumbsUseCase
{
    public record Query(Guid CityId, Guid BuildingId, Guid FloorId, Guid? RoomId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Item? Floor { get; init; }

        public Item? Room { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Item floor, Item? room = null)
        {
            Floor = floor;
            Room = room;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;
        }

        public record Item(Guid Id, string Name);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        PlanNotFound,
        CityNotFound,
        BuildingNotFound,
        FloorNotFound,
        RoomNotFound
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IDbConnection _dbConnection;

        public Handler(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!await CheckTableExistsAsync())
            {
                return new Response(Result.PlanNotFound);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Plans.Props.Page)
                .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

            var pageJson = await _dbConnection.ExecuteScalarAsync<string?>(template.RawSql);

            if (string.IsNullOrEmpty(pageJson))
            {
                return new Response(Result.PlanNotFound);
            }

            var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

            var city = page.Cities.FirstOrDefault(c => c.Id == request.CityId);

            if (city is null)
            {
                return new Response(Result.CityNotFound);
            }

            var building = city.Buildings.FirstOrDefault(b => b.Id == request.BuildingId);

            if (building is null)
            {
                return new Response(Result.BuildingNotFound);
            }

            var floor = building.Floors.FirstOrDefault(f => f.Id == request.FloorId);

            if (floor is null)
            {
                return new Response(Result.FloorNotFound);
            }

            if (request.RoomId is null)
            {
                return new Response(new Response.Item(floor.Id, floor.Number.ToString()));
            }

            var room = floor.Rooms.FirstOrDefault(r => r.Id == request.RoomId);

            if (room is null)
            {
                return new Response(Result.RoomNotFound);
            }

            return new Response(new Response.Item(floor.Id, floor.Number.ToString()), new Response.Item(room.Id, room.Name));
        }

        private async Task<bool> CheckTableExistsAsync()
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = Db.Plans.Table });

            return tableExists > 0;
        }
    }
}
