@using Teslametrics.App.Web.Abstractions
@using Teslametrics.App.Web.Extensions
@using Microsoft.Extensions.Options
@inject IServiceProvider ServiceProvider
<MudPopoverProvider />
<MudDialogProvider />
<MudSnackbarProvider />
<InitializeTimeZone />
<Router AppAssembly="typeof(App).Assembly">
    <Found Context="routeData">
        <AuthorizeRouteView RouteData="@routeData"
                            DefaultLayout="typeof(Features.Main.MainLayout)">
            <NotAuthorized>
                <NotAuthorizedView />
            </NotAuthorized>
            <Authorizing>
                <MudProgressCircular Color="Color.Secondary" />
            </Authorizing>
        </AuthorizeRouteView>
        <FocusOnNavigate RouteData="@routeData"
                         Selector="h1" />
    </Found>
    <NotFound>
        <LayoutView Layout="typeof(Features.Main.MainLayout)">
            <Error />
        </LayoutView>
    </NotFound>
</Router>
<ReconnectModalComponent />