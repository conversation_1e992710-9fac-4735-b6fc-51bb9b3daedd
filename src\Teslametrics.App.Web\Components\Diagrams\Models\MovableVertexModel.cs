using Blazor.Diagrams.Core.Geometry;
using Blazor.Diagrams.Core.Models.Base;

namespace Teslametrics.App.Web.Components.Diagrams.Models;

public class MovableVertexModel(BasePolygonNodeModel parent, Point? position = null) : MovableModel(position)
{
	public BasePolygonNodeModel Parent { get; } = parent;

	public override void SetPosition(double x, double y)
	{
		base.SetPosition(x, y);
		Refresh();
		Parent.Refresh();
	}
}
