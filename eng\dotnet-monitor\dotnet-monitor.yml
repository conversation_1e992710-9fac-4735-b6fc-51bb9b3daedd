name: teslametrics
services:
  dotnet-monitor:
    image: mcr.microsoft.com/dotnet/monitor:latest
    container_name: dotnet-monitor
    restart: always
    privileged: true
    user: root
    ports:
      - "52323:52323"
    volumes:
      - /var/run:/var/run         # Для доступа к диагностическому сокету
      - /tmp:/tmp                 # Для временных файлов и дампов
      - ./dotnet-monitor-settings.json:/etc/dotnet-monitor/settings.json:ro
      - dotnet_diagnostics:/diag
    command: ["collect", "--urls", "http://*:52323"]

volumes:
  dotnet_diagnostics:
