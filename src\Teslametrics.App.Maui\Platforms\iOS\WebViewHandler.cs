using Microsoft.Maui.Handlers;
using WebKit;
using UIKit;

namespace Teslametrics.App.Maui.Platforms.iOS
{
    public class CustomWebViewHandler : WebViewHandler
    {
        protected override void ConnectHandler(WKWebView platformView)
        {
            base.ConnectHandler(platformView);

            if (platformView != null)
            {
                // Hide scrollbars
                platformView.ScrollView.ShowsVerticalScrollIndicator = false;
                platformView.ScrollView.ShowsHorizontalScrollIndicator = false;
                
                // Enable smooth scrolling and bounce effect (native iOS behavior)
                platformView.ScrollView.Bounces = true;
                platformView.ScrollView.AlwaysBounceVertical = true;
                platformView.ScrollView.AlwaysBounceHorizontal = false;
                
                // Configure for mobile experience
                platformView.ScrollView.DecelerationRate = UIScrollView.DecelerationRateNormal;
                
                // Allow zoom but hide zoom controls
                platformView.Configuration.Preferences.SetValueForKey(
                    Foundation.NSObject.FromObject(true), 
                    new Foundation.NSString("allowsInlineMediaPlayback"));
            }
        }
    }
}
