﻿<div class="item d-flex gap-2">
    <MudIcon Icon="@TeslaIcons.State.Warning"
             Color="@Color.Error"
             Size="@Size.Small" />
    <div class="d-flex gap-2 flex-column">
        <MudText>Обнаружена протечка</MudText>
        <MudLink Href="@($"/incidents?IncidentId={Item.Incident!.Id}")"
                 Class="link">Подробнее</MudLink>
    </div>
</div>

@code {
    [Parameter]
    [EditorRequired]
    public GetFridgeUseCase.Response.LeakModel Item { get; set; } = null!;
}
