﻿@using Teslametrics.App.Web.Features.Main.CameraViews.Drawer.Edit
@using Teslametrics.App.Web.Features.Main.CameraViews.Drawer.Create
@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<DrawerComponent Open="IsOpened"
                 OpenChanged="OnOpenChanged"
                 Options="_options">
    <CascadingValue IsFixed="true"
                    Value="this">
        @switch (_mode)
        {
            case DrawerMode.Edit:
                @if (_organizationId.HasValue && _viewId.HasValue)
                {
                    <AuthorizeView Policy="@AppPermissions.Main.CameraViews.Update.GetEnumPermissionString()"
                                   Resource="new PolicyRequirementResource(_organizationId.Value, _viewId.Value)"
                                   Context="innerContext">
                        <Authorized Context="innerContext">
                            <DrawerEditComponent ViewId="@_viewId.Value"
                                                 OrganizationId="@_organizationId.Value" />
                        </Authorized>
                        <NotAuthorized>
                            <NotAuthorizedComponent Class="mud-height-full" />
                        </NotAuthorized>
                    </AuthorizeView>
                }
                else
                {
                    <FailedToRetrieveDataComponent />
                }
                break;

            case DrawerMode.Create:
                @if (_organizationId.HasValue)
                {
                    <AuthorizeView Policy="@AppPermissions.Main.CameraViews.Create.GetEnumPermissionString()"
                                   Resource="new PolicyRequirementResource(_organizationId.Value, null)"
                                   Context="innerContext">
                        <Authorized>
                            <DrawerCreateComponent OrganizationId="@_organizationId.Value" />
                        </Authorized>
                        <NotAuthorized>
                            <NotAuthorizedComponent Class="mud-height-full" />
                        </NotAuthorized>
                    </AuthorizeView>
                }
                else
                {
                    <FailedToRetrieveDataComponent />
                }
                break;
        }
    </CascadingValue>
</DrawerComponent>