namespace Teslametrics.Shared;
[GenerateSerializer]
public class TemperatureModel : BaseSensorModel
{
    [Id(3)]
    public float MinTemp { get; set; }

    [Id(4)]
    public float MaxTemp { get; set; }

    // Конструктор без параметров для System.Text.Json
    public TemperatureModel()
        : base(Guid.Empty, string.Empty, null)
    {
        MinTemp = -30;
        MaxTemp = 30;
    }

    public TemperatureModel(string name = "", string? displayName = null, float minTemp = -30, float maxTemp = 30)
        : this(GuidGenerator.New(), name, displayName, minTemp, maxTemp)
    {
    }

    public TemperatureModel(Guid id, string name, string? displayName, float minTemp = -30, float maxTemp = 30)
        : base(id, name, displayName)
    {
        MinTemp = minTemp;
        MaxTemp = maxTemp;
    }
}