using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Events.CameraView;

namespace Teslametrics.App.Web.Features.Main.CameraViews.TreeView;

public partial class OrganizationTreeItemPresenter
{
    public class OrganizationItemPresenter : MudBlazor.TreeItemData<Guid>
    {
        public int CameraCount { get; set; }
        public int ViewCount { get; set; }
        public override bool Expandable => false;
        public bool IsOrganization => true;
        public Guid Id => Value;
        public OrganizationItemPresenter(Guid id, string title) : base(id)
        {
            Text = title;
        }
    }

    [Parameter, EditorRequired]
    public OrganizationItemPresenter Presenter { get; set; }

    private void CreateView() => EventSystem.Publish(new CameraViewCreateEto(Presenter.Value));
}
