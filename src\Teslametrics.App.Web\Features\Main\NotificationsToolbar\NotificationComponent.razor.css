::deep .menu_icon {
    border-radius: 12px;
}

::deep .menu_icon>button {
    height: 48px;
    width: 48px;
    border-radius: 12px;
    box-shadow: none;
    background-color: var(--mud-palette-surface);
}

::deep .button_badge .mud-badge {
    height: 13px;
    font-size: 10px;
    inset: auto auto calc(100% - 18px) calc(100% - 32px) !important;
    line-height: 1.27;
}

::deep .list {
    display: grid;
    grid-template-rows: auto 1fr;
    overflow: hidden;
    max-height: 400px;
}

/* list rows */
::deep .notif-item {
    border-bottom: 1px solid #eef3f8;
}

::deep .notif-item:hover {
    background: var(--mud-palette-background);
}

::deep .notif-item:last-child {
    border-bottom: none;
}

.dotted {
    width: 8px;
    height: 8px;
    background: var(--mud-palette-primary);
    border-radius: 50%;
}

/* misc tweaks */
::deep .fw-500 {
    font-weight: 500;
}

::deep .icon {
    fill: var(--color-neutral-40);
}

::deep .mud-badge-root {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--mud-palette-surface);
    border-radius: 12px;
    color: var(--mud-palette-text-primary);
}

::deep .mud-badge-root .mud-badge-wrapper {
    margin-left: 8px;
}