using Microsoft.EntityFrameworkCore;
using Teslametrics.Core.Abstractions;
using Teslametrics.Core.Domain.CameraPresets;

namespace Teslametrics.Core.Services.Persistence;

public class PresetRepository : BaseRepository<PresetAggregate>, IPresetRepository
{
    public PresetRepository(CommandAppDbContext dbContext)
        : base(dbContext)
    {
    }

    public Task<bool> IsCameraPresetExistsAsync(Guid presetId, CancellationToken cancellationToken = default) =>
        DbContext.Set<PresetAggregate>()
            .AsNoTracking()
            .AnyAsync(entity => entity.Id == presetId, cancellationToken);

    public Task<bool> IsCameraPresetNameExistsAsync(string name, CancellationToken cancellationToken = default) =>
        DbContext.Set<PresetAggregate>()
            .AsNoTracking()
            .AnyAsync(entity => entity.Name == name, cancellationToken);
}
