.content_grid {
  display: grid;
  grid-template-columns: 2fr 3fr 3fr;
  gap: 16px;
}

.mud-dialog-fullscreen .content_grid {
    grid-template-columns: 1fr;
}

@media screen and (max-width: 768px) {
    .mud-dialog-fullscreen .content_grid {
        grid-template-columns: 1fr;
    }
}

::deep .information {
    border-radius: 12px;
    background-color: var(--color-bg-2);
}

::deep.content_grid > :nth-child(1):last-child {
    grid-column: span 3;   /* занимает и 2-ю, и 3-ю колонки */
}

::deep.content_grid > :nth-child(2):last-child {
    grid-column: span 2;   /* занимает и 2-ю, и 3-ю колонки */
}

.tile_grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

::deep .tile:last-child:nth-child(odd) {
    grid-column: span 2;
}

::deep .caption {
    color: var(--color-text-input-tile);
}

::deep .item {
    background: var(--color-system-err-bg);
    border-radius: 12px;
    padding: 16px;
}

::deep .tile {
    border-radius: 12px;
}
