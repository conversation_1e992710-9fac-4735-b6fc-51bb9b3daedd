@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Localization
@using Teslametrics.App.Web.Features.Authentication
@using Teslametrics.App.Web.Features.Main.AuthToolbarComponent
@using Teslametrics.App.Web.Features.Main.NotificationsToolbar
@using Teslametrics.Shared
@inherits LayoutComponentBase
<MudThemeProvider @ref="@_mudThemeProvider"
                  IsDarkMode="@(_isDarkMode ?? false)"
                  Theme="CustomTheme" />
<div class="d_contents">
    <CascadingValue Value="_mudThemeProvider">
        <AuthorizeView Context="layoutContext">
            <Authorized>
                <MudLayout UserAttributes="@(new Dictionary<string, object>() { { "id", "main_layout" } })"
                           Class="@(_isDarkMode ?? false ? "mud_theme_dark" : "mud_theme_light")">
                    <MudAppBar Elevation="0"
                               Gutters="false"
                               Class="appbar">
                        <div class="d-flex toolbar_items">
                            <DarkModeSwitchComponent IsDarkMode="@(_isDarkMode ?? false)"
                                                     IsDarkModeChanged="OnDarkModeChanged" />
                            <MudChip T="string"
                                     Class="px-4 py-2 time_chip ma-0">@(DateTime.Now.ToLongDateString())</MudChip>
                            <AuthorizeView>
                                <NotificationComponent />
                                <AuthToolbarComponent />
                            </AuthorizeView>
                        </div>
                    </MudAppBar>
                    <NavigationMenuComponent @bind-Open="@_drawerOpen" />
                    <MudMainContent UserAttributes="@(new Dictionary<string, object>() { { "id", "layout_main_content_wrapper" } })"
                                    Class="mud-height-full overflow-auto">
                        @Body
                    </MudMainContent>
                    @if (_userDeviceService.IsMobile)
                    {
                        <BottomNavigationBar @bind-NavBarOpened="_drawerOpen" />
                    }
                </MudLayout>
            </Authorized>
            <NotAuthorized>
                <MudLayout Style="height: 100vh;">
                    <div class="pa-4 h-100 d-flex align-center justify-center mud-height-full mud-width-full">
                        @Body
                    </div>
                </MudLayout>
            </NotAuthorized>
        </AuthorizeView>
    </CascadingValue>
</div>