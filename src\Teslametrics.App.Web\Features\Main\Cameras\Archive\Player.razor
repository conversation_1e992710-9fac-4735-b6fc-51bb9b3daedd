<div class="player_container">
	<video id="@_playerId"
		   class="video-js vjs-default-skin vjs-16-9"
		   width="100%"
		   controls
		   preload="none"
		   @key="_playerId">
		<source src="/streams/@CameraId/stream.m3u8"
				type="application/x-mpegURL" />
	</video>
	@* <div class="player_controlbar px-4 d-flex align-center">
		<MudToggleIconButton Toggled="true"
							 Icon="@Icons.Material.Filled.PlayArrow"
							 Color="@Color.Primary"
							 ToggledIcon="@Icons.Material.Filled.Stop"
							 ToggledColor="@Color.Primary"
							 Size="Size.Small" />
		<div class="player_timeline"></div>
		<MudToggleIconButton Toggled="true"
							 Icon="@Icons.Material.Filled.FullscreenExit"
							 Color="@Color.Primary"
							 ToggledIcon="@Icons.Material.Filled.Fullscreen"
							 ToggledColor="@Color.Primary"
							 Size="Size.Small" />
	</div> *@
</div>
<template id="player_event_template">
	<div class="player-event">
	</div>
</template>
<template id="player_time_label_template">
	<div class="vjs-time-label"></div>
</template>
<template id="player_event_tooltip">
	<div class="event-tooltip">
		<div>
			<span>Событие: </span>
			<span class="event-name"></span>
		</div>
		<span class="time"></span>
	</div>
</template>
<template id="progressbar_tooltip_container">
	<div class="vjs_progressbar_tooltip_container"></div>
</template>
<template id="player_thumbnail_preview">
	<div class="vjs-thumbnail-preview mud-paper mud-elevation-3">
		<span class="vjs-preview-time"></span>
	</div>
</template>
<template id="player_event_timeline">
	<div class="event_timeline">
	</div>
</template>
<template id="player_to_current_time">
	<div class="player_to_current_time">
		<MudIcon Icon="@Icons.Material.Filled.Place" />
	</div>
</template>