::deep .ygrid.crisp {
    stroke: var(--color-neutral-80) !important;
}

::deep .legend {
    display: flex;
    align-items: center;
    gap: 8px;
}

::deep .legend::before {
    content: " ";
    display: block;
    width: 10px;
    height: 10px;
    border-radius: 5px;
}

::deep .legend.current_period::before {
    background: var(--color-primary-800);
}

::deep .legend.previous_period::before {
    background: #33C7F8;
}

::deep .legend.reference_values::before {
    background: #FFD85C;
}