@using Microsoft.AspNetCore.Authorization
@using Teslametrics.App.Web.Features.Main.IncidentsDashboard.Filter
@using Teslametrics.App.Web.Features.Main.IncidentsDashboard.IncidentsCount
@using Teslametrics.App.Web.Features.Main.IncidentsDashboard.IncidentTypes
@using Teslametrics.App.Web.Features.Main.IncidentsDashboard.DoorOpenings
@using Teslametrics.App.Web.Features.Main.IncidentsDashboard.HumidityDynamic
@using Teslametrics.App.Web.Features.Main.IncidentsDashboard.TemperatureDynamic
@page "/incidents-dashboard"
@attribute [Authorize]
@inherits InteractiveBaseComponent
<PageTitle>Multimonitor | Информация по инцидентам и датчикам</PageTitle>
<div class="grid pb-4 overflow-auto">
     <MudGrid Spacing="4"
              Class="align-start incidents_dashboard">
          <MudItem xs="12"
                   Class="item">
               <FilterComponent DateFrom="@Start"
                                DateTo="@End"
                                CityId="@CityId"
                                BuildingId="@BuildingId"
                                FloorId="@FloorId"
                                RoomId="@RoomId"
                                FridgeId="@DeviceId"
                                DateFromChanged="@OnDateFromChanged"
                                DateToChanged="@OnDateToChanged"
                                CityIdChanged="@OnCityIdChanged"
                                BuildingIdChanged="@OnBuildingIdChanged"
                                FloorIdChanged="@OnFloorIdChanged"
                                RoomIdChanged="@OnRoomIdChanged"
                                FridgeIdChanged="@OnFridgeIdChanged" />
          </MudItem>

          <!-- Incidents Chart -->
          <MudItem xs="12"
                   md="6"
                   Class="item">
               <IncidentsCountComponent DateFrom="@Start"
                                        DateTo="@End"
                                        CityId="@CityId"
                                        BuildingId="@BuildingId"
                                        FloorId="@FloorId"
                                        RoomId="@RoomId"
                                        FridgeId="@DeviceId" />
          </MudItem>

          <!-- Incident Types Chart -->
          <MudItem xs="12"
                   md="6"
                   Class="item">
               <IncidentTypesComponent DateFrom="@_startOffset"
                                       DateTo="@_endOffset"
                                       CityId="@CityId"
                                       BuildingId="@BuildingId"
                                       FloorId="@FloorId"
                                       RoomId="@RoomId"
                                       FridgeId="@DeviceId" />
          </MudItem>

          @if (CityId.HasValue && BuildingId.HasValue && RoomId.HasValue && DeviceId.HasValue && FloorId.HasValue)
          {
               <!-- Temperature Chart -->
               <MudItem xs="12"
                        md="6"
                        Class="item">
                    <TemperatureDynamicComponent DateFrom="@_startOffset"
                                                 DateTo="@_endOffset"
                                                 CityId="@CityId.Value"
                                                 BuildingId="@BuildingId.Value"
                                                 RoomId="@RoomId.Value"
                                                 FloorId="@FloorId.Value"
                                                 FridgeId="@DeviceId.Value" />
               </MudItem>

               <!-- Humidity Chart -->
               <MudItem xs="12"
                        md="6">
                    <HumidityDynamicComponent DateFrom="@_startOffset"
                                              DateTo="@_endOffset"
                                              CityId="@CityId.Value"
                                              BuildingId="@BuildingId.Value"
                                              RoomId="@RoomId.Value"
                                              FloorId="@FloorId.Value"
                                              FridgeId="@DeviceId.Value" />
               </MudItem>

               <!-- Door Openings Chart -->
               <MudItem xs="12"
                        Class="item">
                    <DoorOpeningsComponent DateFrom="@_startOffset"
                                           DateTo="@_endOffset"
                                           CityId="@CityId"
                                           BuildingId="@BuildingId"
                                           RoomId="@RoomId"
                                           FloorId="@FloorId"
                                           FridgeId="@DeviceId" />
               </MudItem>
          }
     </MudGrid>
</div>
