using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Text.Json;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.ImportDialog.CoordinatesSelector;

public partial class CoordinatesSelector
{
    private bool _isMapDialogVisible;
    private Coordinates? _selectedCoordinates;
    private string _coordinatesText = string.Empty;

    private readonly DialogOptions _dialogOptions = new()
    {
        MaxWidth = MaxWidth.Medium,
        FullWidth = true,
        CloseButton = true,
        BackdropClick = true
    };

    [Parameter]
    public Coordinates? Value { get; set; }

    [Parameter]
    public EventCallback<Coordinates?> ValueChanged { get; set; }

    [Parameter]
    public bool HasValidationError { get; set; } = false;

    [Parameter]
    public string? ValidationErrorText { get; set; }

    protected class CoordinatesSelectorConverter : MudBlazor.Converter<Coordinates?, string?>
    {
        public CoordinatesSelectorConverter()
        {
            SetFunc = coordinates => coordinates?.ToString() ?? string.Empty;
            GetFunc = str => string.IsNullOrEmpty(str) ? null : TryParseCoordinates(str);
        }

        private static Coordinates? TryParseCoordinates(string str)
        {
            try
            {
                // Попробуем распарсить JSON формат
                if (str.StartsWith("{") && str.EndsWith("}"))
                {
                    return JsonSerializer.Deserialize<Coordinates>(str);
                }

                // Попробуем распарсить формат "(lat, lng)"
                if (str.StartsWith("(") && str.EndsWith(")"))
                {
                    var content = str.Trim('(', ')');
                    var parts = content.Split(',');
                    if (parts.Length == 2 &&
                        double.TryParse(parts[0].Trim(), out var lat) &&
                        double.TryParse(parts[1].Trim(), out var lng))
                    {
                        return new Coordinates(lat, lng);
                    }
                }

                return null;
            }
            catch
            {
                return null;
            }
        }
    }

    public CoordinatesSelector() : base(new CoordinatesSelectorConverter())
    {
    }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        UpdateCoordinatesText();
    }

    private void UpdateCoordinatesText()
    {
        _coordinatesText = Value?.ToString() ?? string.Empty;
    }

    private void OpenMapDialog()
    {
        _selectedCoordinates = Value;
        _isMapDialogVisible = true;
    }

    private void CancelMapSelection()
    {
        _isMapDialogVisible = false;
        _selectedCoordinates = null;
    }

    private async Task ConfirmMapSelection()
    {
        Value = _selectedCoordinates;
        UpdateCoordinatesText();

        if (ValueChanged.HasDelegate)
        {
            await ValueChanged.InvokeAsync(Value);
        }

        await WriteValueAsync(Value);
        await BeginValidateAsync();
        FieldChanged(Value);

        _isMapDialogVisible = false;
    }

    private async Task ClearCoordinates()
    {
        Value = null;
        UpdateCoordinatesText();

        if (ValueChanged.HasDelegate)
        {
            await ValueChanged.InvokeAsync(Value);
        }

        await WriteValueAsync(Value);
        await BeginValidateAsync();
        FieldChanged(Value);
    }
}
