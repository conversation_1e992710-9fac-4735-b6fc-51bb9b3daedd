﻿.floor_info {
    display: grid;
    gap: 24px;
    grid-template-rows: auto auto 1fr;
    overflow: hidden;
}

::deep .floor_info .mud-breadcrumbs {
    padding: 8px;
}

::deep.floor_info .title {
    color: rgba(98, 99, 101, 1);
    padding-top: 8px;
    padding-right: 12px;
    padding-bottom: 8px;
    padding-left: 12px;
}

::deep .incidents {
    border-radius: 12px;
    justify-content: space-between;
    border-width: 1px;
}

::deep .incidents.error {
    background: rgba(254, 243, 242, 1);
    border: 1px solid rgba(255, 0, 30, 1);
}

.container {
    overflow: hidden;
    display: grid;
    grid-template-columns: 0.705fr 1fr;
    grid-template-rows: auto 1fr;
    gap: 8px;
}

::deep.container>*:first-child {
    grid-column: 1 / -1;
}

.container::deep .no_building_selected {
    grid-column: 1 / -1;
    padding: 16px;
}

@media screen and (max-width: 767px) {
    .layout {
        grid-template-rows: auto 1fr 400px;
        grid-template-columns: 1fr;
        padding: 8px;
    }
}