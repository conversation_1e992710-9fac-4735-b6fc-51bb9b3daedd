{"compileOnSave": true, "compilerOptions": {"target": "ES2020", "module": "ES2020", "moduleResolution": "node", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "outDir": "./wwwroot/js/compiled", "rootDirs": ["./TypeScript", "./wwwroot/js", "./Components", "./Features"], "baseUrl": ".", "paths": {"@components/*": ["Components/*"], "@features/*": ["Features/*"], "@typescript/*": ["TypeScript/*"], "@js/*": ["wwwroot/js/*"]}, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true}, "include": ["TypeScript/**/*.ts", "wwwroot/js/**/*.ts", "Components/**/*.ts", "Features/**/*.ts"], "exclude": ["node_modules", "wwwroot/js/compiled", "wwwroot/js/**/*.js", "wwwroot/lib", "bin", "obj", "**/*.min.js", "**/node_modules"]}