using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.MediaServer.Orleans.Wirenboard;

namespace Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.FridgeNode.Sensors;

public static class SubscribeSensorUseCase
{
    public record Request(ISensorObserver Observer, string TopicName) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response()
        {
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.TopicName).NotEmpty();
            RuleFor(r => r.Observer).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IValidator<Request> _validator;
        private readonly IClusterClient _clusterClient;

        public Handler(IValidator<Request> validator,
                       IClusterClient clusterClient)
        {
            _validator = validator;
            _clusterClient = clusterClient;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var grain = _clusterClient.GetGrain<IWirenboardSensorDataGrain>(Guid.Empty);
            await grain.SubscribeAsync(new IWirenboardSensorDataGrain.SubscribeRequest(request.TopicName, request.Observer));

            return new Response();
        }
    }
}
