using System.Security.AccessControl;

namespace Teslametrics.App.Web.Components;

public static class TeslaIcons
{
    public static class Grid
    {
        public const string Custom = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0.90332 7.95117H8.2793C8.50188 7.95117 8.68243 8.13196 8.68262 8.35449V13.0967C8.68262 13.3194 8.50199 13.5 8.2793 13.5H0.90332C0.680625 13.5 0.5 13.3194 0.5 13.0967V8.35449C0.500188 8.13196 0.680741 7.95117 0.90332 7.95117ZM10.9893 7.95117H13.0967C13.3193 7.95117 13.4998 8.13196 13.5 8.35449V13.0967C13.5 13.3194 13.3194 13.5 13.0967 13.5H10.9893C10.7666 13.5 10.5859 13.3194 10.5859 13.0967V8.35449C10.5861 8.13196 10.7667 7.95117 10.9893 7.95117ZM0.90332 0.5H3.01074C3.23344 0.5 3.41406 0.680625 3.41406 0.90332V5.64551C3.41388 5.86804 3.23332 6.04883 3.01074 6.04883H0.90332C0.68074 6.04883 0.500187 5.86804 0.5 5.64551V0.90332C0.5 0.680625 0.680625 0.5 0.90332 0.5ZM5.7207 0.5H13.0967C13.3194 0.5 13.5 0.680625 13.5 0.90332V5.64551C13.4998 5.86804 13.3193 6.04883 13.0967 6.04883H5.7207C5.49812 6.04883 5.31757 5.86804 5.31738 5.64551V0.90332C5.31738 0.680625 5.49801 0.5 5.7207 0.5Z\" stroke=\"currentColor\"/></svg>";
        public const string Grid1Plus5 = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 15 15\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"0.5\" y=\"0.5\" width=\"9\" height=\"9\" rx=\"1.5\" stroke=\"currentColor\"/><rect x=\"11.5\" y=\"0.5\" width=\"3\" height=\"3\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"11.5\" y=\"6\" width=\"3\" height=\"3\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"11.5\" y=\"11.5\" width=\"3\" height=\"3\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"6\" y=\"11.5\" width=\"3\" height=\"3\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"0.5\" y=\"11.5\" width=\"3\" height=\"3\" rx=\"0.7\" stroke=\"currentColor\"/></svg>";

        public const string Grid2Plus8 = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 15 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"0.5\" y=\"1\" width=\"6\" height=\"6\" rx=\"1.5\" stroke=\"currentColor\"/><rect x =\"8.5\" y=\"1\" width=\"6\" height=\"6\" rx=\"1.5\" stroke=\"currentColor\"/><rect x =\"12.5\" y=\"9\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x =\"12.5\" y=\"13\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x =\"8.5\" y=\"13\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x =\"8.5\" y=\"9\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x =\"4.5\" y=\"13\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x =\"4.5\" y=\"9\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x =\"0.5\" y=\"13\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x =\"0.5\" y=\"9\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/></svg>";

        public const string Grid1Plus12 = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 15 15\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"0.5\" y=\"0.5\" width=\"6\" height=\"6\" rx=\"1.5\" stroke=\"currentColor\"/><rect x=\"12.5\" y=\"0.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"8.5\" y=\"0.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"12.5\" y=\"4.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"8.5\" y=\"4.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"12.5\" y=\"8.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"12.5\" y=\"12.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"8.5\" y=\"12.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"8.5\" y=\"8.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"4.5\" y=\"12.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"4.5\" y=\"8.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"0.5\" y=\"12.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"0.5\" y=\"8.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/></svg>";

        public const string Grid1Plus7 = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 15 15\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"0.5\" y=\"0.5\" width=\"10\" height=\"10\" rx=\"1.5\" stroke=\"currentColor\"/><rect x=\"12.5\" y=\"0.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"12.5\" y=\"4.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"12.5\" y=\"8.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"12.5\" y=\"12.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"8.5\" y=\"12.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"4.5\" y=\"12.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"0.5\" y=\"12.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/></svg>";
        public const string Grid3Plus4 = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 15 15\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"0.5\" y=\"0.5\" width=\"6\" height=\"6\" rx=\"1.5\" stroke=\"currentColor\"/><rect x=\"0.5\" y=\"8.5\" width=\"6\" height=\"6\" rx=\"1.5\" stroke=\"currentColor\"/><rect x=\"8.5\" y=\"0.5\" width=\"6\" height=\"6\" rx=\"1.5\" stroke=\"currentColor\"/><rect x=\"12.5\" y=\"8.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"12.5\" y=\"12.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"8.5\" y=\"12.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/><rect x=\"8.5\" y=\"8.5\" width=\"2\" height=\"2\" rx=\"0.7\" stroke=\"currentColor\"/></svg>";
    }

    public static class Actions
    {
        public const string CreateDirectory = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 18 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M9.0013 7.16667V12.1667M6.5013 9.66667H11.5013M17.3346 13.8333C17.3346 14.2754 17.159 14.6993 16.8465 15.0118C16.5339 15.3244 16.11 15.5 15.668 15.5H2.33464C1.89261 15.5 1.46868 15.3244 1.15612 15.0118C0.843563 14.6993 0.667969 14.2754 0.667969 13.8333V2.16667C0.667969 1.72464 0.843563 1.30072 1.15612 0.988155C1.46868 0.675595 1.89261 0.5 2.33464 0.5H6.5013L8.16797 3H15.668C16.11 3 16.5339 3.17559 16.8465 3.48816C17.159 3.80072 17.3346 4.22464 17.3346 4.66667V13.8333Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string Import = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 14 13\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M13 8.5V11.1667C13 11.5203 12.8595 11.8594 12.6095 12.1095C12.3594 12.3595 12.0203 12.5 11.6667 12.5H2.33333C1.97971 12.5 1.64057 12.3595 1.39052 12.1095C1.14048 11.8594 1 11.5203 1 11.1667V8.5M3.66667 5.16667L7 8.5M7 8.5L10.3333 5.16667M7 8.5V0.5\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string Export = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M13 9V11.6667C13 12.0203 12.8595 12.3594 12.6095 12.6095C12.3594 12.8595 12.0203 13 11.6667 13H2.33333C1.97971 13 1.64057 12.8595 1.39052 12.6095C1.14048 12.3594 1 12.0203 1 11.6667V9M10.3333 4.33333L7 1M7 1L3.66667 4.33333M7 1V9\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string ZoomOut = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 12 2\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1.33398 1H10.6673\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string ZoomIn = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M6.00065 1.33301V10.6663M1.33398 5.99967H10.6673\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string Add = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M6 1V11M1 6H11\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string StartView = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M5.33203 14H10.6654M7.9987 11.3333V14M2.66536 2H13.332C14.0684 2 14.6654 2.59695 14.6654 3.33333V10C14.6654 10.7364 14.0684 11.3333 13.332 11.3333H2.66536C1.92898 11.3333 1.33203 10.7364 1.33203 10V3.33333C1.33203 2.59695 1.92898 2 2.66536 2Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string Play = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M3.33203 2L12.6654 8L3.33203 14V2Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";
    }

    public static class Sensors
    {
        public const string Door = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1 11H11M3.5 6.5H3.505M3 1H9C9.55228 1 10 1.44772 10 2V11H2L2 2C2 1.44772 2.44771 1 3 1Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string Temperature = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 9 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M6.33342 9.83984V2.33317C6.33342 1.89114 6.15782 1.46722 5.84526 1.15466C5.5327 0.842099 5.10878 0.666504 4.66675 0.666504C4.22472 0.666504 3.8008 0.842099 3.48824 1.15466C3.17568 1.46722 3.00008 1.89114 3.00008 2.33317V9.83984C2.46493 10.1974 2.05896 10.7176 1.84213 11.3236C1.6253 11.9296 1.60909 12.5892 1.79592 13.2051C1.98274 13.821 2.36269 14.3605 2.87964 14.7439C3.39659 15.1273 4.02314 15.3343 4.66675 15.3343C5.31036 15.3343 5.93691 15.1273 6.45386 14.7439C6.97081 14.3605 7.35075 13.821 7.53758 13.2051C7.7244 12.5892 7.7082 11.9296 7.49137 11.3236C7.27453 10.7176 6.86856 10.1974 6.33342 9.83984Z\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string Leak = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M7.33301 5.83366H3.33301V2.50033H8.74676M7.33301 5.83366H12.6663V2.50033H8.74676M7.33301 5.83366L8.74676 2.50033M7.99842 8.50033L9.41342 9.86757C9.69326 10.1378 9.88387 10.4821 9.96115 10.857C10.0384 11.2319 9.9989 11.6205 9.84756 11.9737C9.69622 12.3269 9.43988 12.6288 9.11095 12.8412C8.78202 13.0536 8.39529 13.167 7.99967 13.167C7.60406 13.167 7.21733 13.0536 6.8884 12.8412C6.55947 12.6288 6.30313 12.3269 6.15179 11.9737C6.00045 11.6205 5.96092 11.2319 6.0382 10.857C6.11548 10.4821 6.30609 10.1378 6.58593 9.86757L7.99842 8.50033ZM3.33301 7.16699V1.16699H1.33301V7.16699H3.33301ZM12.6663 1.16699V7.16699H14.6663V1.16699H12.6663Z\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string Humidity = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 14 15\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M6.99992 1.29297L10.7733 5.0663C11.5195 5.81204 12.0278 6.76235 12.2339 7.79701C12.4399 8.83167 12.3345 9.9042 11.9309 10.8789C11.5274 11.8537 10.8438 12.6868 9.96666 13.273C9.08953 13.8592 8.05824 14.1721 7.00326 14.1721C5.94828 14.1721 4.91699 13.8592 4.03985 13.273C3.16272 12.6868 2.47913 11.8537 2.07557 10.8789C1.672 9.9042 1.56659 8.83167 1.77266 7.79701C1.97873 6.76235 2.48703 5.81204 3.23326 5.0663L6.99992 1.29297Z\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string Wifi = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 12 10\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M8.11991 2.87984C8.39884 3.15846 8.62012 3.48933 8.77109 3.85352C8.92206 4.21772 8.99977 4.6081 8.99977 5.00234C8.99977 5.39659 8.92206 5.78697 8.77109 6.15117C8.62012 6.51536 8.39884 6.84622 8.11991 7.12484M3.87991 7.11984C3.60098 6.84122 3.3797 6.51036 3.22873 6.14616C3.07776 5.78197 3.00005 5.39159 3.00005 4.99734C3.00005 4.6031 3.07776 4.21272 3.22873 3.84852C3.3797 3.48433 3.60098 3.15346 3.87991 2.87484M9.53491 1.46484C10.4723 2.40248 10.9988 3.67402 10.9988 4.99984C10.9988 6.32566 10.4723 7.5972 9.53491 8.53484M2.46491 8.53484C1.52755 7.5972 1.00098 6.32566 1.00098 4.99984C1.00098 3.67402 1.52755 2.40248 2.46491 1.46484M6.99991 4.99984C6.99991 5.55213 6.55219 5.99984 5.99991 5.99984C5.44762 5.99984 4.99991 5.55213 4.99991 4.99984C4.99991 4.44756 5.44762 3.99984 5.99991 3.99984C6.55219 3.99984 6.99991 4.44756 6.99991 4.99984Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string Power = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 12 8\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2.5 7H1.5C1.23478 7 0.98043 6.89464 0.792893 6.70711C0.605357 6.51957 0.5 6.26522 0.5 6V2C0.5 1.73478 0.605357 1.48043 0.792893 1.29289C0.98043 1.10536 1.23478 1 1.5 1H3.095M7.5 1H8.5C8.76522 1 9.01957 1.10536 9.20711 1.29289C9.39464 1.48043 9.5 1.73478 9.5 2V6C9.5 6.26522 9.39464 6.51957 9.20711 6.70711C9.01957 6.89464 8.76522 7 8.5 7H6.905M11.5 4.5V3.5M5.5 1L3.5 4H6.5L4.5 7\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

    }

    public static class State
    {
        public const string Warning = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M8.00004 5.3335V8.00016M8.00004 10.6668H8.00671M14.6667 8.00016C14.6667 11.6821 11.6819 14.6668 8.00004 14.6668C4.31814 14.6668 1.33337 11.6821 1.33337 8.00016C1.33337 4.31826 4.31814 1.3335 8.00004 1.3335C11.6819 1.3335 14.6667 4.31826 14.6667 8.00016Z\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string Success = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M14.6667 7.38674V8.00007C14.6659 9.43769 14.2004 10.8365 13.3396 11.988C12.4788 13.1394 11.2689 13.9817 9.89028 14.3893C8.51166 14.797 7.03821 14.748 5.68969 14.2498C4.34116 13.7516 3.18981 12.8308 2.40735 11.6248C1.62488 10.4188 1.25323 8.99211 1.34783 7.55761C1.44242 6.12312 1.99818 4.75762 2.93223 3.66479C3.86628 2.57195 5.12856 1.81033 6.53083 1.4935C7.9331 1.17668 9.40022 1.32163 10.7134 1.90674M14.6667 2.66674L8.00004 9.34007L6.00004 7.34007\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";
    }

    public static class Devices
    {
        public const string Fridge = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 14 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1.66638 14.6668H12.3331M3.66639 8.66683H3.67305V9.3335M1.66638 6.66683C1.66638 6.66683 1.66638 11.6668 1.66638 12.6668C1.66638 13.6668 2.33305 14.0002 2.99972 14.0002C3.66638 14.0002 10.333 14.0002 10.9997 14.0002C11.6664 14.0002 12.333 13.6668 12.333 12.6668C12.333 11.6668 12.333 6.66683 12.333 6.66683M1.66638 6.66683L1.66638 2.66683C1.66638 1.93045 2.26334 1.3335 2.99972 1.3335H10.9997C11.7361 1.3335 12.333 1.93045 12.333 2.66683V6.66683M1.66638 6.66683H12.333M3.67305 4.00016V4.66683\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string Camera = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M15.3333 2.66683L10.6666 6.00016L15.3333 9.3335V2.66683Z\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M9.33329 1.3335H1.99996C1.26358 1.3335 0.666626 1.93045 0.666626 2.66683V9.3335C0.666626 10.0699 1.26358 10.6668 1.99996 10.6668H9.33329C10.0697 10.6668 10.6666 10.0699 10.6666 9.3335V2.66683C10.6666 1.93045 10.0697 1.3335 9.33329 1.3335Z\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string Sensors = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M22 12H18L15 21L9 3L6 12H2\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";
    }

    public static class Notifications
    {
        public const string Bell = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 20 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M11.73 20C11.5542 20.3031 11.3018 20.5547 10.9982 20.7295C10.6946 20.9044 10.3504 20.9965 10 20.9965C9.64962 20.9965 9.30539 20.9044 9.00177 20.7295C8.69816 20.5547 8.44581 20.3031 8.27 20M16 7C16 5.4087 15.3679 3.88258 14.2426 2.75736C13.1174 1.63214 11.5913 1 10 1C8.4087 1 6.88258 1.63214 5.75736 2.75736C4.63214 3.88258 4 5.4087 4 7C4 14 1 16 1 16H19C19 16 16 14 16 7Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";
    }

    public static class Profile
    {
        public const string UserOutline = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 18 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M17 19V17C17 15.9391 16.5786 14.9217 15.8284 14.1716C15.0783 13.4214 14.0609 13 13 13H5C3.93913 13 2.92172 13.4214 2.17157 14.1716C1.42143 14.9217 1 15.9391 1 17V19M13 5C13 7.20914 11.2091 9 9 9C6.79086 9 5 7.20914 5 5C5 2.79086 6.79086 1 9 1C11.2091 1 13 2.79086 13 5Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";
    }

    public static class PageIcons
    {
        public const string Dashboard = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M5.66667 1H1V5.66667H5.66667V1Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M13 1H8.33333V5.66667H13V1Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M13 8.33333H8.33333V13H13V8.33333Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M5.66667 8.33333H1V13H5.66667V8.33333Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string Analytics = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 10 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M5 11.3332V4.6665M9 11.3332V0.666504M1 11.3332V8.6665\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";
    }

    public static class Theme
    {
        public const string DarkMode = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M19.0009 10.79C18.8436 12.4922 18.2047 14.1144 17.1591 15.4668C16.1135 16.8192 14.7044 17.8458 13.0966 18.4265C11.4888 19.0073 9.74886 19.1181 8.08038 18.7461C6.4119 18.3741 4.88387 17.5345 3.6751 16.3258C2.46633 15.117 1.62682 13.589 1.25479 11.9205C0.882753 10.252 0.993594 8.51208 1.57434 6.9043C2.15508 5.29651 3.18171 3.88737 4.53409 2.84175C5.88647 1.79614 7.50867 1.15731 9.21088 1C8.21429 2.34827 7.73473 4.00945 7.85941 5.68141C7.98409 7.35338 8.70474 8.92506 9.89028 10.1106C11.0758 11.2961 12.6475 12.0168 14.3195 12.1415C15.9914 12.2662 17.6526 11.7866 19.0009 10.79Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

        public const string LightMode = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M12 1V3M12 21V23M4.22 4.22L5.64 5.64M18.36 18.36L19.78 19.78M1 12H3M21 12H23M4.22 19.78L5.64 18.36M18.36 5.64L19.78 4.22M17 12C17 14.7614 14.7614 17 12 17C9.23858 17 7 14.7614 7 12C7 9.23858 9.23858 7 12 7C14.7614 7 17 9.23858 17 12Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";
    }

}
