// Держим ссылку на построенный график, чтобы можно было переинициализировать
let humidityPlot = null;
const rootStyles = getComputedStyle(document.documentElement);
const paperClr =
	rootStyles.getPropertyValue("--mud-palette-surface").trim() || "#e0e0e0";

const layout = {
	// 1) сам «лист» (вся область SVG / Canvas), подсказки
	paper_bgcolor: paperClr,
	// 2) внутренняя область построения (ось‑ось‑сетка)
	plot_bgcolor: "transparent",
	showlegend: false,
	margin: {
		//  ← вся «белая рамка» внутри SVG
		t: 40,
		r: 20,
		l: 40,
		b: 40,
	},
	hovermode: "x unified",
	shapes: [],
	xaxis: {
		type: "date",
		tickformat: "%d.%m", // 17.07,
		range: [],
	},
	yaxis: {
		zeroline: true,
		zerolinewidth: 1,
		autorange: false, //  — и выключаем авто‑масштаб
		range: [],
		dtick: 5,
	},
};

const config = {
	responsive: true,
	displayModeBar: false, // убираем тулбар Plotly
	locale: "ru", // формат dd.MM HH:mm будет русским
};

function waitForPlotly() {
	return new Promise((resolve, reject) => {
		if (typeof Plotly !== "undefined") {
			resolve();
			return;
		}
		let tries = 0;
		(function check() {
			if (typeof Plotly !== "undefined") return resolve();
			if (++tries > 100) return reject(new Error("Plotly not loaded"));
			setTimeout(check, 100);
		})();
	});
}

function buildYAxisRange(yCur, yPrev, ref, padFrac = 0.05) {
	// Берём только числовые y (`null` / `NaN` пропускаем)
	const all = [...yCur, ...yPrev]
		.filter((v) => v.value != null && !Number.isNaN(v.value))
		.map((x) => x.value);

	let ymin = Math.min(...all);
	let ymax = Math.max(...all);

	// Берём в расчёт референсные линии
	if (ref) {
		ymin = Math.min(ymin, ref.minHumidity);
		ymax = Math.max(ymax, ref.maxHumidity);
	}

	const pad = (ymax - ymin) * padFrac; // padFrac% сверху и снизу
	return [ymin - pad, ymax + pad];
}

export async function initHumidityChart(
	divId,
	xAxis,
	yCur,
	yPrev,
	refVals,
	dateRange
) {
	await waitForPlotly().catch((err) => {
		console.error(err);
		return;
	});
	// Сносим прежний, если был
	if (humidityPlot) {
		Plotly.purge(divId); // удаляем старый график из DOM
		humidityPlot = null;
	}

	/* ---------- 0. «Нет данных» ---------- */
	if (!xAxis || xAxis.length === 0) {
		Plotly.newPlot(
			divId,
			[], // пустой data‑массив
			{
				paper_bgcolor: "transparent",
				plot_bgcolor: "transparent",
				xaxis: { visible: false },
				yaxis: { visible: false },
				annotations: [
					{
						text: "Нет данных для отображения",
						xref: "paper",
						yref: "paper",
						x: 0.5,
						y: 0.5,
						showarrow: false,
						font: { size: 16, color: "#888" },
					},
				],
				margin: { t: 40, r: 10, b: 40, l: 48 },
			},
			{ displayModeBar: false }
		).then((plot) => (humidityPlot = plot));
		return;
	}

	const traceCur = {
		x: xAxis,
		y: yCur.map((p) => p.value),
		customdata: yCur.map((p) => p.date), // любые данные
		mode: "lines+markers",
		connectgaps: true, // ←  соединяем через null
		name: "За период",
		line: { width: 2, color: "#1A5A65" },
		marker: { size: 3 },
		hovertemplate:
			"<b>%{customdata|%d.%m.%Y %H:%M:%S}</b><br>" + // тут используем
			"%{y:.2f}%<extra></extra>",
	};

	const tracePrev = {
		x: xAxis,
		y: yPrev.map((p) => p.value),
		customdata: yPrev.map((p) => p.date), // любые данные
		mode: "lines+markers",
		name: "За прошлый период",
		connectgaps: true, // ←  соединяем через null
		line: { width: 2, color: "#33C7F8" },
		marker: { size: 3 },
		hovertemplate:
			"<b>%{customdata|%d.%m.%Y %H:%M:%S}</b><br>" + // тут используем
			"%{y:.2f}%<extra></extra>",
	};

	const shapes = [];
	if (refVals) {
		shapes.push(
			{
				type: "line",
				x0: dateRange.dateFrom,
				x1: dateRange.dateTo,
				y0: refVals.minHumidity,
				y1: refVals.minHumidity,
				line: { color: "#FFD85C", width: 3 },
			},
			{
				type: "line",
				x0: dateRange.dateFrom,
				x1: dateRange.dateTo,
				y0: refVals.maxHumidity,
				y1: refVals.maxHumidity,
				line: { color: "#FFD85C", width: 3 },
			}
		);
	}

	const yRange = buildYAxisRange(yCur, yPrev, refVals, 0.2);

	const tick = Math.floor((yRange[1] - yRange[0]) / 7);

	layout.shapes = shapes;
	layout.yaxis.range = yRange;
	layout.yaxis.dtick = tick;
	layout.xaxis.tickformat = "%d.%m";
	layout.xaxis.range = [dateRange.dateFrom, dateRange.dateTo];

	Plotly.newPlot(divId, [traceCur, tracePrev], layout, config).then(
		(plot) => (humidityPlot = plot)
	);
}
