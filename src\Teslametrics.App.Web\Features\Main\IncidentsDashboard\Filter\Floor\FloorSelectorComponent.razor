﻿@inherits InteractiveBaseComponent
<MudAutocomplete T="GetFloorListUseCase.Response.Item"
                 SearchFunc="@SearchAsync"
                 ToStringFunc="@(e => e == null ? null : e.Number.ToString())"
                 Value="@_selected"
                 Margin="Margin.Dense"
                 ValueChanged="@ValueChanged"
                 Label="Этаж"
                 Clearable="true"
                 ResetValueOnEmptyText="true"
                 Variant="Variant.Outlined"
                 Disabled="BuildingId is null || CityId is null" />

@code {
}