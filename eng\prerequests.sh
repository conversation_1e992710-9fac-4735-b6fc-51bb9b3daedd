#graylog admin pass : q8qkoNtn4fZ17sLUiUuSgqkPWHTwtbRIgDSrhyNEh6Iqz2pFjjdOGc6XhGQ9uOjjX5rfyxGeghFu8eZI28pKU4hdsPkIWr3V

#INSTALL GITLAB RUNNER
sudo apt update
sudo apt install ca-certificates curl
curl -L "https://packages.gitlab.com/install/repositories/runner/gitlab-runner/script.deb.sh" | sudo bash
sudo apt install gitlab-runner
sudo gitlab-runner register
#sudo usermod -a -G sudo gitlab-runner
sudo visudo
#add
gitlab-runner ALL=(ALL) NOPASSWD: ALL

sudo usermod -aG gitlab-runner {current_user}

#INSTALL DOTNET SDK 8

sudo apt install -y dotnet-sdk-8.0
sudo dotnet tool install dotnet-ef --tool-path /usr/bin
#export PATH="$PATH:$HOME/.dotnet/tools/"
#add to /home/<USER>/.bashrc
#export PATH="$PATH:$HOME/.dotnet/tools/"

#INSTALL DOTNET SDK 9
wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt update
sudo apt install -y dotnet-sdk-9.0

#INSTALL DOCKER

sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
  $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt update
sudo apt install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
sudo usermod -aG docker gitlab-runner
sudo gitlab-runner restart

#UFW
#sudo ufw allow 80/tcp

sudo apt install xfsprogs
sudo mkfs.xfs /dev/*
sudo mkdir -p /mnt/drive-*
sudo mount -t xfs /dev/* /mnt/drive-*

sudo blkid /dev/* #copy UUID
sudo nano /etc/fstab
#add
UUID=XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX /mnt/drive-* xfs defaults 0 0

sudo docker compose -f env-{args}-compose.yml up -d


