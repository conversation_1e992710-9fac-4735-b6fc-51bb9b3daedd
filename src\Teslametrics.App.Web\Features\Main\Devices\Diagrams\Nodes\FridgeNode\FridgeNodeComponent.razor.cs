using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MudBlazor;
using Teslametrics.App.Web.Components;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Features.Main.Devices.RoomContent;

namespace Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.FridgeNode;

public partial class FridgeNodeComponent
{
    private IEnumerable<GetFridgeUseCase.Response.TemperatureModel>? _temperature => _fridgeData?.Fridge.Sensors.OfType<GetFridgeUseCase.Response.TemperatureModel>();
    private IEnumerable<GetFridgeUseCase.Response.HumidityModel>? _humidity => _fridgeData?.Fridge.Sensors.OfType<GetFridgeUseCase.Response.HumidityModel>();
    private IEnumerable<GetFridgeUseCase.Response.DoorModel>? _door => _fridgeData?.Fridge.Sensors.OfType<GetFridgeUseCase.Response.DoorModel>();
    private IEnumerable<GetFridgeUseCase.Response.LeakModel>? _leak => _fridgeData?.Fridge.Sensors.OfType<GetFridgeUseCase.Response.LeakModel>();
    private IEnumerable<GetFridgeUseCase.Response.PowerModel>? _power => _fridgeData?.Fridge.Sensors.OfType<GetFridgeUseCase.Response.PowerModel>();

    private bool _isLoading = false;
    private GetFridgeUseCase.Response? _fridgeData;

    private async Task OnMouseEnterAsync()
    {
        await FetchFridgeDataAsync();
    }

    private async Task FetchFridgeDataAsync()
    {
        if (_isLoading) return;

        try
        {
            _isLoading = true;
            _fridgeData = await ScopeFactory.MediatorSend(new GetFridgeUseCase.Query(Element.Id));
        }
        catch (Exception ex)
        {
            Logger.LogError("Ошибка при получении данных холодильника: {Message}", ex.Message);
            Snackbar.Add($"Не удалось получить данные холодильника из-за ошибки сообщения с сервером.", MudBlazor.Severity.Error);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private string TempValueProcessor(object? value) => value switch
    {
        null => "Нет связи с датчиком",
        double doubleValue => Math.Round(doubleValue, 2) + "°C",
        _ => value?.ToString() ?? "-",
    };

    private string HumidityValueProcessor(object? value) => value switch
    {
        null => "Нет связи с датчиком",
        _ => (value ?? 0) + "%",
    };

    private string DoorValueProcessor(object value) => value switch
    {
        null => "Нет связи с датчиком",
        bool boolValue => boolValue ? "Открыта" : "Закрыта",
        _ => value?.ToString() ?? "-",
    };

    private string PowerValueProcessor(object value) => value switch
    {
        null => "Нет связи с датчиком",
        bool boolValue => boolValue ? "В норме" : "Нет электроснабжения",
        _ => value?.ToString() ?? "-",
    };

    private string LeakValueProcessor(object value) => value switch
    {
        null => "Нет связи с датчиком",
        bool boolValue => boolValue ? "Обнаружена протечка" : "Не обнаружена",
        _ => value?.ToString() ?? "-",
    };
}
