using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Components;
using Teslametrics.App.Web.Events.CameraView;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.CameraViews.TreeView;

public partial class ViewTreeItemPresenter
{
    [Inject]
    private NavigationManager _navigationManager { get; set; } = null!;

    public class ViewItemPresenter : MudBlazor.TreeItemData<Guid>
    {
        public int CameraCount { get; set; }
        public int ViewCount { get; set; }
        public override bool Expandable => true;
        public Guid OrganizationId { get; set; }
        public Guid Id => Value;
        public Guid ParentId { get; init; }

        public ViewItemPresenter(Guid id, Guid parentId, string title, GridType? gridType = null) : base(id)
        {
            Text = title;
            ParentId = parentId;
            Icon = gridType switch
            {
                GridType.GridCustom => TeslaIcons.Grid.Custom,
                GridType.Grid1Plus5 => TeslaIcons.Grid.Grid1Plus5,
                GridType.Grid1Plus7 => TeslaIcons.Grid.Grid1Plus7,
                GridType.Grid1Plus12 => TeslaIcons.Grid.Grid1Plus12,
                GridType.Grid2Plus8 => TeslaIcons.Grid.Grid2Plus8,
                GridType.Grid3Plus4 => TeslaIcons.Grid.Grid3Plus4,
                _ => TeslaIcons.Grid.Custom
            };
        }
    }

    [Parameter]
    [EditorRequired]
    public EventCallback<ViewItemPresenter> OnSelect { get; set; }

    [Parameter, EditorRequired]
    public ViewItemPresenter Presenter { get; set; }

    private Task SelectAsync()
    {
        if (OnSelect.HasDelegate)
            return OnSelect.InvokeAsync(Presenter);

        return Task.CompletedTask;
    }

    private void EditView() => EventSystem.Publish(new CameraViewEditEto(Presenter.OrganizationId, Presenter.Id));

    private void ShowView() => _navigationManager.NavigateTo($"/camera_views_detail?OrganizationId={Presenter.OrganizationId}&ViewId={Presenter.Id}", false);
    private void DeleteView() => EventSystem.Publish(new CameraViewDeleteEto(Presenter.OrganizationId, Presenter.Id));
}
