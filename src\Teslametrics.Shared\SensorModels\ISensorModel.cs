using System.Text.Json.Serialization;

namespace Teslametrics.Shared;

[JsonPolymorphic]
[JsonDerivedType(typeof(TemperatureModel), typeDiscriminator: "temperature")]
[JsonDerivedType(typeof(DoorModel), typeDiscriminator: "door")]
[JsonDerivedType(typeof(HumidityModel), typeDiscriminator: "humidity")]
[JsonDerivedType(typeof(LeakModel), typeDiscriminator: "leak")]
[JsonDerivedType(typeof(PowerModel), typeDiscriminator: "power")]
public interface ISensorModel
{
    Guid Id { get; set; }
    string Name { get; set; }
    string? DisplayName { get; set; }
}