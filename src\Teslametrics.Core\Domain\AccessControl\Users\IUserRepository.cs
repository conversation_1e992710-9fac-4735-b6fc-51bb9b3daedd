﻿using Teslametrics.Core.Abstractions;

namespace Teslametrics.Core.Domain.AccessControl.Users;

public interface IUserRepository : IRepository<UserAggregate>
{
    public Task<UserAggregate?> FindByUsernameAsync(string username, CancellationToken cancellationToken = default);

    public Task<List<UserAggregate>> GetUsersByOrganizationIdAsync(Guid id, CancellationToken cancellationToken = default);

    public Task<List<UserAggregate>> GetUsersInRoleAsync(Guid roleId, CancellationToken cancellationToken = default);

    public Task<bool> IsUsernameExists(string username, CancellationToken cancellationToken = default);

    public Task<bool> IsUserExistsAsync(Guid id, CancellationToken cancellationToken = default);

    public Task<List<UserAggregate>> GetAllUsersAsync(CancellationToken cancellationToken = default);
}