using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Shared;
using Teslametrics.Core.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraSettingDialog.Quota;

public static class GetQuotaUseCase
{
    public record Query(Guid QuotaId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; set; }
        public string Name { get; set; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id, string name)
        {
            Id = id;
            Name = name;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Id = Guid.Empty;
            Name = string.Empty;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        QuotaNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(r => r.QuotaId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken = default)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.CameraQuotas.Props.Id)
                .Select(Db.CameraQuotas.Props.Name)
                .Where(Db.CameraQuotas.Props.Id, ":QuotaId", SqlOperator.Equals, new { request.QuotaId })
                .Build(QueryType.Standard, Db.CameraQuotas.Table, RowSelection.AllRows);

            var quota = await _dbConnection.QuerySingleOrDefaultAsync<CameraQuotaModel>(template.RawSql, template.Parameters);

            if (quota is null)
            {
                return new Response(Result.QuotaNotFound);
            }

            return new Response(quota.Id,
                                quota.Name);
        }
    }

    public record CameraQuotaModel(Guid Id,
                                   string Name);
}