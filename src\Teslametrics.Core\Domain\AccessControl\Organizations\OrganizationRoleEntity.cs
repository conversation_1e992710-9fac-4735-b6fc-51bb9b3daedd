using Teslametrics.Shared;

namespace Teslametrics.Core.Domain.AccessControl.Organizations;

public class OrganizationRoleEntity
{
    private readonly List<RolePermissionEntity> _resourcePermissions;

    public Guid Id { get; private set; }

    public string Name { get; private set; }

    public bool IsAdmin { get; private set; }

    public IReadOnlyCollection<RolePermissionEntity> ResourcePermissions => _resourcePermissions;

    public bool IsSystem => Id == SystemConsts.RootRoleId;

    public static OrganizationRoleEntity Create(Guid id, string name, bool isAdmin, IEnumerable<(Guid Id, Guid OrganizationId, ResourcePermission ResourcePermission)> permissions)
    {
        return new OrganizationRoleEntity(id,
                                          name,
                                          isAdmin,
                                          permissions.Select(p => RolePermissionEntity.Create(p.Id, p.OrganizationId, id, p.ResourcePermission)));
    }

    private OrganizationRoleEntity()
    {
        Name = string.Empty;
        _resourcePermissions = [];
    }

    private OrganizationRoleEntity(Guid id, string name, bool isAdmin, IEnumerable<RolePermissionEntity> permissions)
    {
        Id = id;
        Name = name;
        IsAdmin = isAdmin;
        _resourcePermissions = permissions.ToList();
    }

    public bool Update(string name, bool isAdmin, IEnumerable<(Guid Id, Guid OrganizationId, ResourcePermission ResourcePermission)> permissions)
    {
        bool isUpdated = false;

        if (Name != name)
        {
            Name = name;
            isUpdated = true;
        }

        if (IsAdmin != isAdmin)
        {
            IsAdmin = isAdmin;
            isUpdated = true;
        }

        var currentPermissions = ResourcePermissions.Select(p => p.Id).ToHashSet();
        var newPermissions = permissions.Select(p => p.Id).ToHashSet();

        if (!currentPermissions.SetEquals(newPermissions))
        {
            var removedPersmissions = currentPermissions.Except(newPermissions);
            _resourcePermissions.RemoveAll(p => removedPersmissions.Contains(p.Id));

            var addedPermissions = newPermissions.Except(currentPermissions);
            _resourcePermissions.AddRange(permissions.Where(p => addedPermissions.Contains(p.Id))
                .Select(p => RolePermissionEntity.Create(p.Id, p.OrganizationId, Id, p.ResourcePermission)));

            isUpdated = true;
        }

        return isUpdated;
    }
}