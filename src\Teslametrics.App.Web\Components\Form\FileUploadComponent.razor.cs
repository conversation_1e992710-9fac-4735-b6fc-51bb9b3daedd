﻿using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Components;

namespace Teslametrics.App.Web.Components.Form;

public partial class FileUploadComponent
{
	private MudBlazor.MudFileUpload<IBrowserFile>? _fileUpload;
	private static string DefaultDragClass = "d-flex flex-column justify-center align-content-center mud-background-gray py-8 px-2 border-dashed border-2";
	private string DragClass = DefaultDragClass;

	#region
	[Parameter, EditorRequired]
	public string UploadFileButtonText { get; set; } = null!;

	[Parameter, EditorRequired]
	public string UploadFileContainerText { get; set; } = null!;
	#endregion

	[Parameter]
	public string Accept { get; set; } = ".csv";

	[Parameter]
	public EventCallback OnReset { get; set; }

	[Parameter]
	public EventCallback<Abstractions.File> OnFileRead { get; set; }

	[Parameter]
	public bool Row { get; set; } = true;

	[Parameter]
	public string? LabelClass { get; set; }

	private async Task OnInputFileChanged(InputFileChangeEventArgs args)
	{
		if (OnReset.HasDelegate)
		{
			await OnReset.InvokeAsync();
		}

		ClearDragClass();
		var files = args.GetMultipleFiles();
		var file = files.FirstOrDefault() as IBrowserFile;
		if (file is not null)
		{
			//fileName = file.Name;
			await ReadFile(file);
		}
	}

	private async Task ReadFile(IBrowserFile browserFile)
	{
		long maxFileSize = 1024 * 1024 * 15;
		await using var stream = new MemoryStream();
		var buffer = new byte[browserFile.Size];

		await using var newFileStream = browserFile.OpenReadStream(maxFileSize);

		int bytesRead;
		double totalRead = 0;
		while ((bytesRead = await newFileStream.ReadAsync(buffer)) != 0)
		{
			totalRead += bytesRead;
			await stream.WriteAsync(buffer, 0, bytesRead);
		}

		if (OnFileRead.HasDelegate)
		{
			Abstractions.File file = new(browserFile.Name, browserFile.LastModified, browserFile.Size, browserFile.ContentType, stream.GetBuffer());
			await OnFileRead.InvokeAsync(file);
		}
	}

	private void SetDragClass()
	{
		DragClass = $"{DefaultDragClass} mud-border-primary";
	}

	private void ClearDragClass()
	{
		DragClass = DefaultDragClass;
	}
}
