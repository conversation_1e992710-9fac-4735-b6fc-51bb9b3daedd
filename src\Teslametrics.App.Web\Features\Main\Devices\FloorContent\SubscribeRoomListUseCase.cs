using System.Reactive.Linq;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Features.Main.SystemSettings;
using Teslametrics.Core.Services.DomainEventBus;

namespace Teslametrics.App.Web.Features.Main.Devices.FloorContent;

public static class SubscribeRoomListUseCase
{

    public record Request(IObserver<object> Observer) : BaseRequest<Response>; // В теории могу ещё и ID датчиков прокинуть! Но их тогда нужно добавить в Get

    public record Response : BaseResponse
    {
        public IDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

    // События, на которые можно подписаться
    public record UpdatedEvent; // Изменился план и нужно перезапросить список комнат

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        RoomNotFound
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IValidator<Request> _validator;
        private readonly IDomainEventBus _domainEventBus;

        public Handler(IValidator<Request> validator,
                       IDomainEventBus domainEventBus)
        {
            _validator = validator;
            _domainEventBus = domainEventBus;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var eventStream = await _domainEventBus.GetEventStreamAsync();

            // Подписываемся на события, связанные с комнатой и её содержимым
            var subscription = eventStream
                .Where(e => e switch
                {
                    PlanUpdatedEvent => true,
                    _ => false
                })
                .Select(e => e switch
                {
                    PlanUpdatedEvent => new UpdatedEvent(),
                    _ => throw new AppException("Invalid event type")
                })
                .Subscribe(request.Observer);

            return new Response(subscription);
        }
    }
}
