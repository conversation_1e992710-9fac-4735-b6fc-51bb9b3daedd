using Microsoft.AspNetCore.Components.Server.Circuits;

namespace Teslametrics.App.Web.Services.UserDevice;

public static class UserDeviceModule
{
    public static void Install(IServiceCollection services)
    {
        // Регистрируем основной сервис как Scoped - каждый Blazor circuit получит свой экземпляр
        services.AddScoped<IUserDeviceService, UserDeviceService>();

        // Регистрируем CircuitHandler для автоматической инициализации
        services.AddScoped<CircuitHandler, UserDeviceCircuitHandler>();
    }

    // Т.к. у SSR с пререндором свой scope, а у Circut свой - то обрабатываем двумя подходами
    public static void Use(IApplicationBuilder app)
    {
        app.UseMiddleware<UserAgentMiddleware>();
    }
}
