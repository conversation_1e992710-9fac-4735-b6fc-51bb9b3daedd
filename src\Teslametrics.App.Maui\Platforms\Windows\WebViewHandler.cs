using Microsoft.Maui.Handlers;

namespace Teslametrics.App.Maui.Platforms.Windows
{
    public class CustomWebViewHandler : WebViewHandler
    {
        protected override void ConnectHandler(Microsoft.UI.Xaml.Controls.WebView2 platformView)
        {
            base.ConnectHandler(platformView);

            // For Windows, we'll rely primarily on the CSS injection from MainPage.xaml.cs
            // The WebView2 control in Windows will use the CSS we inject via JavaScript
            // This is a simpler approach that avoids API compatibility issues
        }
    }
}
