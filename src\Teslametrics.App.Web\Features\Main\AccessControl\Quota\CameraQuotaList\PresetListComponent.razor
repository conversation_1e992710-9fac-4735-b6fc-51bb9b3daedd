@using Microsoft.AspNetCore.Components.Authorization
@using Teslametrics.Shared
@attribute [StreamRendering]
@inherits InteractiveBaseComponent
<div class="mud-height-full overflow-hidden">
    <MudDataGrid T="QuotaItem"
                 Items="@(_items)"
                 Filterable="false"
                 SortMode="SortMode.None"
                 Outlined="false"
                 Elevation="0"
                 CurrentPage="_currentPage"
                 Bordered="true"
                 RowsPerPage="Limit"
                 RowsPerPageChanged="RowsPerPageChanged"
                 Loading="IsLoading"
                 Height="100%"
                 Class="quota_table"
                 Striped="true"
                 FixedHeader="true"
                 Hover="true">
        <ToolBarContent>
            <MudText Typo="Typo.h6">Квоты камер</MudText>
            <MudSpacer />
            <MudStack Row="true"
                      AlignItems="AlignItems.Center"
                      Justify="Justify.Center">
                @if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
                {
                    <MudTooltip Arrow="true"
                                Placement="Placement.Start"
                                Text="Ошибка подписки на события">
                        <MudIconButton OnClick="SubscribeAsync"
                                       Icon="@Icons.Material.Filled.ErrorOutline"
                                       Color="Color.Error" />
                    </MudTooltip>
                    <MudIconButton OnClick="RefreshAsync"
                                   Icon="@Icons.Material.Filled.Refresh"
                                   Color="Color.Primary" />
                }
                <MudTextField T="string"
                              Value="SearchString"
                              ValueChanged="OnSearchChanged"
                              Placeholder="Поиск"
                              Label="Поиск"
                              Clearable="true"
                              Adornment="Adornment.Start"
                              AdornmentIcon="@Icons.Material.Filled.Search"
                              IconSize="Size.Medium"
                              Immediate="true" />
                <AuthorizeView Policy="@AppPermissions.Main.CameraQuotas.Create.GetEnumPermissionString()"
                               Context="innerContext"
                               Resource="new PolicyRequirementResource(OrganizationId, null)">
                    <MudButton OnClick="Add"
                               Color=" Color.Primary"
                               Variant="Variant.Outlined">Добавить квоту</MudButton>
                </AuthorizeView>
            </MudStack>
        </ToolBarContent>
        <Columns>
            <PropertyColumn Property="x => x.Name"
                            Title="Наименование"
                            CellClass="cell"
                            Editable="false" />
            <PropertyColumn Property="x => x.UsedQuota"
                            Title="Использовано"
                            CellClass="cell"
                            Editable="false"
                            HeaderClass="cell" />
            <PropertyColumn Property="x => x.TotalQuota - x.UsedQuota"
                            Title="Осталось"
                            CellClass="cell"
                            Editable="false"
                            HeaderClass="cell">
                <CellTemplate>
                    @if (context.Item.TotalQuota == -1)
                    {
                        <MudText Color="Color.Success">
                            ∞
                        </MudText>
                    }
                    else
                    {

                        <MudText Color="@GetQuotaColor(context.Item)">
                            @(context.Item.TotalQuota - context.Item.UsedQuota)
                        </MudText>
                    }
                </CellTemplate>
            </PropertyColumn>
            <PropertyColumn Property="x => x.TotalQuota"
                            CellClass="totalQuotaCell"
                            Title="Всего">
                <EditTemplate>
                    <MudForm Class="align-end">
                        <MudStack Row="true">
                            @if (context.Item.NewQuota != -1)
                            {
                                <MudNumericField T="int"
                                                 @bind-Value="context.Item.NewQuota"
                                                 For="() => context.Item.NewQuota"
                                                 Min="@context.Item.TotalQuota"
                                                 Validation="_validator.Validation"
                                                 Immediate="true"
                                                 Required="@context.Selected"
                                                 Class="input"
                                                 RequiredError="Задайте значение" />
                            }
                            <MudTooltip Arrow="true"
                                        Placement="Placement.Top"
                                        RootClass="d-flex align-center"
                                        Text="@(context.Item.NewQuota == -1 ? "Квота не ограничена" : "Сделать квоту неограниченной")">
                                <MudCheckBox T="bool"
                                             Value="context.Item.NewQuota == -1"
                                             ValueChanged="() => context.Item.NewQuota = context.Item.NewQuota != -1 ? -1 : context.Item.TotalQuota == -1 ? context.Item.UsedQuota : context.Item.TotalQuota"
                                             Label="@(context.Item.NewQuota == -1 ? "Не ограничена" : "")"
                                             LabelPlacement="@Placement.Start" />
                            </MudTooltip>
                        </MudStack>
                    </MudForm>
                </EditTemplate>
                <CellTemplate>
                    @if (context.Item.TotalQuota == -1)
                    {
                        <MudText Color="Color.Success">
                            ∞
                        </MudText>
                    }
                    else
                    {

                        <MudText Color="@GetQuotaColor(context.Item)">
                            @context.Item.TotalQuota
                        </MudText>
                    }
                </CellTemplate>
            </PropertyColumn>

            <AuthorizeView Policy="@($"{AppPermissions.Main.CameraQuotas.Update.GetEnumPermissionString()},{AppPermissions.Main.CameraQuotas.Delete.GetEnumPermissionString()}")"
                           Context="contextMenuContext"
                           Resource="new PolicyRequirementResource(OrganizationId, null)">

                <TemplateColumn CellClass="d-flex justify-end">
                    <HeaderTemplate>
                    </HeaderTemplate>
                    <EditTemplate>
                        <MudIconButton Icon="@Icons.Material.Outlined.Save"
                                       Color="@(_isValid ? Color.Success : Color.Error)"
                                       OnClick="() => SaveAsync(context.Item)" />
                    </EditTemplate>
                    <CellTemplate>
                        <MudButtonGroup Color="Color.Primary"
                                        Variant="Variant.Outlined">
                            <AuthorizeView Policy="@AppPermissions.Main.CameraQuotas.Update.GetEnumPermissionString()"
                                           Context="innerContext"
                                           Resource="new PolicyRequirementResource(OrganizationId, context.Item.Id)">
                                <Authorized>
                                    <MudButton StartIcon="@Icons.Material.Outlined.Edit"
                                               OnClick="@(() => Edit(context.Item))">
                                        Редактировать
                                    </MudButton>
                                </Authorized>
                                <NotAuthorized>
                                    <MudButton StartIcon="@Icons.Material.Outlined.Panorama"
                                               OnClick="@(() => Select(context.Item))">
                                        Редактировать
                                    </MudButton>
                                </NotAuthorized>
                            </AuthorizeView>
                            <MudMenu Icon="@Icons.Material.Filled.ArrowDropDown"
                                     Style="align-self: auto;">
                                <MudMenuItem Icon="@Icons.Material.Outlined.PanoramaFishEye"
                                             OnClick="@(() => Select(context.Item))">
                                    Просмотр
                                </MudMenuItem>
                                <AuthorizeView Policy="@AppPermissions.Main.CameraQuotas.Update.GetEnumPermissionString()"
                                               Context="innerContext"
                                               Resource="new PolicyRequirementResource(OrganizationId, context.Item.Id)">
                                    <MudMenuItem Icon="@Icons.Material.Outlined.Edit"
                                                 OnClick="@(() => Edit(context.Item))">
                                        Редактировать
                                    </MudMenuItem>
                                </AuthorizeView>
                                <AuthorizeView Policy="@AppPermissions.Main.CameraQuotas.Delete.GetEnumPermissionString()"
                                               Context="deleteContext"
                                               Resource="new PolicyRequirementResource(OrganizationId, context.Item.Id)">
                                    <MudMenuItem OnClick="() => DeleteAsync(context.Item)"
                                                 Icon="@Icons.Material.Outlined.Delete"
                                                 IconColor="Color.Error">
                                        Удалить
                                    </MudMenuItem>
                                </AuthorizeView>
                            </MudMenu>
                        </MudButtonGroup>
                    </CellTemplate>
                </TemplateColumn>
            </AuthorizeView>
        </Columns>
        <RowLoadingContent>
            <MudSkeleton />
        </RowLoadingContent>
        <NoRecordsContent>
            <MudStack Class="mud-width-full"
                      AlignItems="AlignItems.Center"
                      Justify="Justify.Center">
                <MudButton Variant="Variant.Filled"
                           Color="Color.Primary"
                           OnClick="RefreshAsync">Обновить</MudButton>
            </MudStack>
        </NoRecordsContent>
        <PagerContent>
            <MudDataGridPager T="QuotaItem"
                              InfoFormat="{first_item}-{last_item} из {all_items}"
                              RowsPerPageString="Строк на страницу:" />
        </PagerContent>
    </MudDataGrid>
</div>