﻿@if (BuildingId is null)
{
    <MudPaper Class="d-flex flex-column align-center justify-center" Style="height: 100%; min-height: 300px; padding: 2rem; text-align: center;">
        <MudIcon Icon="@Icons.Material.Filled.Apartment" Size="Size.Large" Color="Color.Primary" />
        <MudText Typo="Typo.h5" Class="mt-2">Выберите здание</MudText>
        <MudText Typo="Typo.body1" Class="mt-1" Color="Color.Secondary">
            Для начала работы выберите здание из списка. После этого можно будет выбрать этаж.
        </MudText>
    </MudPaper>
}
@code {
    [Parameter]
    public Guid? CityId { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }
}
