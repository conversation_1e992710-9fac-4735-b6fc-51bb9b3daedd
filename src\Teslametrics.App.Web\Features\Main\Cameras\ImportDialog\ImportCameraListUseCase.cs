using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.AccessControl.Organizations;
using Teslametrics.Core.Domain.Cameras;
using Teslametrics.Core.Domain.Folders;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.TransactionManager;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.ImportDialog;

public static class ImportCameraListUseCase
{
    public record Command(Guid OrganizationId, List<Command.Camera> Cameras) : BaseRequest<Response>
    {
        public record Camera(string Name,
                             TimeSpan TimeZone,
                             Coordinates? Coordinates,
                             string FolderName,
                             string ArchiveUri,
                             string ViewUri,
                             string PublicUri,
                             string QuotaName,
                             bool AutoStart,
                             bool StartOnCreate);
    };

    public record Response : BaseResponse
    {
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public List<string> SuccessfullyAddedCameras { get; init; }

        public List<string> CamerasWithIncorrectQuota { get; init; }

        public List<string> CamerasQuotaLimitReached { get; init; }

        public Response(List<string> successfullyAddedCameras, List<string> camerasWithIncorrectQuota, List<string> camerasQuotaLimitReached)
        {
            Result = Result.Success;
            SuccessfullyAddedCameras = successfullyAddedCameras;
            CamerasWithIncorrectQuota = camerasWithIncorrectQuota;
            CamerasQuotaLimitReached = camerasQuotaLimitReached;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;
            SuccessfullyAddedCameras = [];
            CamerasWithIncorrectQuota = [];
            CamerasQuotaLimitReached = [];
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        OrganizationNotFound
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.OrganizationId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly ICameraRepository _cameraRepository;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly IFolderRepository _folderRepository;
        private readonly IClusterClient _clusterClient;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       ICameraRepository cameraRepository,
                       IOrganizationRepository organizationRepository,
                       IFolderRepository folderRepository,
                       IClusterClient clusterClient,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _cameraRepository = cameraRepository;
            _organizationRepository = organizationRepository;
            _folderRepository = folderRepository;
            _clusterClient = clusterClient;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var organization = await _organizationRepository.FindAsync(request.OrganizationId, cancellationToken);
            if (organization is null)
            {
                return new Response(Result.OrganizationNotFound);
            }

            var successfullyAddedCameras = new List<(Guid CameraId, string CameraName, string ArchiveUri, string ViewUri, string PublicUri, bool StartOnCreate)>();
            var camerasWithIncorrectQuota = new List<string>();
            var camerasQuotaLimitReached = new List<string>();

            var events = new List<object>();

            foreach (var camera in request.Cameras)
            {
                var cameraQuota = organization.CameraQuotas.SingleOrDefault(cq => cq.Name == camera.QuotaName);
                if (cameraQuota is null)
                {
                    camerasWithIncorrectQuota.Add(camera.Name);
                    continue;
                }

                var cameraCount = await _cameraRepository.GetCameraCountByQuotaAsync(cameraQuota.Id, cancellationToken);
                if (cameraQuota.Limit != -1 && cameraQuota.Limit <= cameraCount)
                {
                    camerasQuotaLimitReached.Add(camera.Name);
                    continue;
                }

                var folder = await _folderRepository.FindByNameAsync(camera.FolderName, request.OrganizationId, cancellationToken);
                if (folder is null)
                {
                    var (createdFolder, folderEvents) = FolderAggregate.Create(Guid.NewGuid(), request.OrganizationId, null, camera.FolderName);

                    await _folderRepository.AddAsync(createdFolder, cancellationToken);

                    events.AddRange(folderEvents);
                    folder = createdFolder;
                }

                var (createdCamera, cameraEvents) = CameraAggregate.Create(GuidGenerator.New(),
                                                                     request.OrganizationId,
                                                                     folder.Id,
                                                                     cameraQuota.Id,
                                                                     camera.Name,
                                                                     camera.TimeZone,
                                                                     camera.Coordinates?.Latitude,
                                                                     camera.Coordinates?.Longitude,
                                                                     camera.ArchiveUri,
                                                                     camera.ViewUri,
                                                                     camera.PublicUri,
                                                                     false,
                                                                     null,
                                                                     camera.AutoStart);

                await _cameraRepository.AddAsync(createdCamera, cancellationToken);
                await _cameraRepository.SaveChangesAsync(cancellationToken);

                successfullyAddedCameras.Add((createdCamera.Id, createdCamera.Name, createdCamera.ArchiveUri, createdCamera.ViewUri, createdCamera.PublicUri, camera.StartOnCreate));
            }

            await _cameraRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            foreach (var (cameraId, cameraName, archiveUri, viewUri, publicUri, startOnCreate) in successfullyAddedCameras)
            {
                if (startOnCreate)
                {
                    var grain = _clusterClient.GetGrain<IMediaServerGrain>(Guid.Empty);
                    await grain.ConnectRtspAsync(new IMediaServerGrain.CameraConnectRtspRequest(cameraId, archiveUri, viewUri, publicUri));
                }
            }

            return new Response(successfullyAddedCameras.Select(c => c.CameraName).ToList(), camerasWithIncorrectQuota, camerasQuotaLimitReached);
        }
    }
}