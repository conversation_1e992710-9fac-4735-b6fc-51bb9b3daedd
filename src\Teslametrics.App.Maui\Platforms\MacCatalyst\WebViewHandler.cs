using Microsoft.Maui.Handlers;
using WebKit;
using UIKit;

namespace Teslametrics.App.Maui.Platforms.MacCatalyst
{
    public class CustomWebViewHandler : WebViewHandler
    {
        protected override void ConnectHandler(WKWebView platformView)
        {
            base.ConnectHandler(platformView);

            if (platformView != null)
            {
                // Hide scrollbars for MacCatalyst (similar to iOS but adapted for Mac)
                platformView.ScrollView.ShowsVerticalScrollIndicator = false;
                platformView.ScrollView.ShowsHorizontalScrollIndicator = false;
                
                // Configure scrolling behavior for Mac
                platformView.ScrollView.Bounces = false; // Less bouncy on Mac
                platformView.ScrollView.AlwaysBounceVertical = false;
                platformView.ScrollView.AlwaysBounceHorizontal = false;
                
                // Smooth scrolling
                platformView.ScrollView.DecelerationRate = UIScrollView.DecelerationRateNormal;
                
                // Configure for desktop-mobile hybrid experience
                platformView.Configuration.Preferences.SetValueForKey(
                    Foundation.NSObject.FromObject(true), 
                    new Foundation.NSString("allowsInlineMediaPlayback"));
            }
        }
    }
}
