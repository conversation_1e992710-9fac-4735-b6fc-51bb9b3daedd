using Orleans.Providers;

namespace Orleans.Hosting
{
    public static class ClientBuilderStreamingExtensions
    {
        /// <summary>
        /// Adds a new in-memory stream provider to the client, using the default message serializer
        /// (<see cref="DefaultLiveMessageBodySerializer"/>).
        /// </summary>
        /// <param name="builder">The builder.</param>
        /// <param name="name">The stream provider name.</param>
        /// <param name="configure">The configuration delegate.</param>
        /// <returns>The client builder.</returns>
        public static IClientBuilder AddLiveStreams(
            this IClientBuilder builder,
            string name,
            Action<IClusterClientLiveStreamConfigurator>? configure = null)
        {
            return AddLiveStreams<DefaultLiveMessageBodySerializer>(builder, name, configure);
        }

        /// <summary>
        /// Adds a new in-memory stream provider to the client.
        /// </summary>
        /// <typeparam name="TSerializer">The type of the t serializer.</typeparam>
        /// <param name="builder">The builder.</param>
        /// <param name="name">The stream provider name.</param>
        /// <param name="configure">The configuration delegate.</param>
        /// <returns>The client builder.</returns>
        public static IClientBuilder AddLiveStreams<TSerializer>(
            this IClientBuilder builder,
            string name,
            Action<IClusterClientLiveStreamConfigurator>? configure = null)
            where TSerializer : class, ILiveMessageBodySerializer
        {
            //the constructor wire up DI with all default components of the streams , so need to be called regardless of configureStream null or not
            var memoryStreamConfigurator = new ClusterClientLiveStreamConfigurator<TSerializer>(name, builder);
            configure?.Invoke(memoryStreamConfigurator);
            return builder;
        }
    }
}