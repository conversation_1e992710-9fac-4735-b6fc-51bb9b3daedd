using Microsoft.AspNetCore.Components;
using MudBlazor;

namespace Teslametrics.App.Web.Components.CSVImport;

public partial class CsvHeaderMapper
{
    public record CsvHeader(string Name)
    {
        /// <summary>
        /// Уникальный идентификатор экземпляра заголовка для отслеживания в разных зонах.
        /// </summary>
        public string InstanceId { get; init; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Идентификатор зоны, к которой принадлежит этот экземпляр заголовка.
        /// Пустая строка означает пул доступных заголовков.
        /// </summary>
        public string ZoneId { get; init; } = string.Empty;

        /// <summary>
        /// Создает новый экземпляр заголовка для указанной зоны.
        /// </summary>
        public CsvHeader ForZone(string zoneId) => this with { ZoneId = zoneId, InstanceId = Guid.NewGuid().ToString() };

        /// <summary>
        /// Проверяет логическое равенство заголовков по имени (игнорируя InstanceId и ZoneId).
        /// </summary>
        public virtual bool Equals(CsvHeader? other)
        {
            return other != null && Name.Equals(other.Name, StringComparison.OrdinalIgnoreCase);
        }

        public override int GetHashCode()
        {
            return Name.GetHashCode(StringComparison.OrdinalIgnoreCase);
        }
    };

    public record ModelField(string Field, string Label, bool Required);
    public record HeaderMapping(CsvHeader CsvHeader, ModelField ModelField);

    private List<CsvHeader> _allHeaderInstances = [];
    private MudDropContainer<CsvHeader>? _containerRef;

    #region [Parameters]
    [Parameter, EditorRequired]
    public IEnumerable<ModelField> Fields { get; set; } = null!;

    [Parameter, EditorRequired]
    public IEnumerable<CsvHeader> Headers { get; set; } = null!;

    [Parameter]
    public IEnumerable<HeaderMapping> Mapping { get; set; } = [];

    [Parameter]
    public EventCallback<IEnumerable<HeaderMapping>> MappingChanged { get; set; }

    [Parameter]
    public bool AllowDuplicateHeaders { get; set; }

    [Parameter]
    public HeaderMapperLocales LocaleStrings { get; set; } = new();
    #endregion

    /// <summary>
    /// Паттерн MappedHeaders для упрощения управления состоянием.
    /// Предоставляет четкую связь между CSV заголовками и полями модели.
    /// </summary>
    public IEnumerable<HeaderMapping> MappedHeaders => Mapping;

    /// <summary>
    /// Проверяет, является ли маппинг валидным (все обязательные поля назначены).
    /// </summary>
    public bool IsValid => Fields.Where(f => f.Required)
                                 .All(field => Mapping.Any(m => m.ModelField.Equals(field)));

    protected override Task OnParametersSetAsync()
    {
        UpdateAllHeaderInstances();
        RefreshContainer();
        return base.OnParametersSetAsync();
    }

    #region [Header Management]
    /// <summary>
    /// Создает экземпляры заголовков для всех зон (пул + поля модели).
    /// Каждый экземпляр знает свою зону, что позволяет точно определить источник drag-операции.
    /// </summary>
    private void UpdateAllHeaderInstances()
    {
        _allHeaderInstances.Clear();

        // Создаем экземпляры для пула доступных заголовков
        foreach (var header in Headers)
        {
            if (AllowDuplicateHeaders)
            {
                // При разрешении дублирования всегда показываем все заголовки в пуле
                _allHeaderInstances.Add(header.ForZone(string.Empty));
            }
            else
            {
                // При запрете дублирования показываем только неназначенные заголовки
                if (!Mapping.Any(m => m.CsvHeader.Equals(header)))
                {
                    _allHeaderInstances.Add(header.ForZone(string.Empty));
                }
            }
        }

        // Создаем экземпляры для каждого назначенного маппинга
        foreach (var mapping in Mapping)
        {
            var instanceForField = mapping.CsvHeader.ForZone(mapping.ModelField.Field);
            _allHeaderInstances.Add(instanceForField);
        }
    }
    #endregion

    #region [Drop Handling]
    /// <summary>
    /// Обрабатывает событие сброса заголовка в зону.
    /// </summary>
    private async void OnDrop(MudItemDropInfo<CsvHeader> dropInfo)
    {
        if (dropInfo?.Item is null) return;

        if (dropInfo.DropzoneIdentifier == string.Empty)
        {
            await HandleRemovalDrop(dropInfo);
        }
        else
        {
            await HandleFieldDrop(dropInfo);
        }

        RefreshContainer();
    }

    /// <summary>
    /// Обрабатывает сброс заголовка в пустую зону (удаление маппинга).
    /// Использует информацию о зоне источника для точного определения удаляемого маппинга.
    /// </summary>
    private async Task HandleRemovalDrop(MudItemDropInfo<CsvHeader> dropInfo)
    {
        var headerInstance = dropInfo.Item!;

        if (AllowDuplicateHeaders)
        {
            // При разрешении дублирования используем ZoneId для точного определения источника
            if (!string.IsNullOrEmpty(headerInstance.ZoneId))
            {
                // Удаляем конкретный маппинг для источника
                var sourceField = Fields.FirstOrDefault(f => f.Field == headerInstance.ZoneId);
                if (sourceField != null)
                {
                    var mappingToRemove = Mapping.FirstOrDefault(m =>
                        m.CsvHeader.Equals(headerInstance) && m.ModelField.Equals(sourceField));

                    if (mappingToRemove != null)
                    {
                        var updatedMapping = Mapping.ToList();
                        updatedMapping.Remove(mappingToRemove);
                        await UpdateMappingAsync(updatedMapping);
                    }
                }
            }
        }
        else
        {
            // При запрете дублирования удаляем все маппинги с этим заголовком
            var updatedMapping = Mapping.Where(m => !m.CsvHeader.Equals(headerInstance)).ToList();
            await UpdateMappingAsync(updatedMapping);
        }
    }

    /// <summary>
    /// Обрабатывает сброс заголовка на поле модели.
    /// Поддерживает обмен заголовков местами для улучшения UX.
    /// </summary>
    private async Task HandleFieldDrop(MudItemDropInfo<CsvHeader> dropInfo)
    {
        var headerInstance = dropInfo.Item!;
        var targetField = Fields.FirstOrDefault(f => f.Field == dropInfo.DropzoneIdentifier);

        if (targetField is null) return;

        var updatedMapping = Mapping.ToList();

        // Проверяем условия для обмена заголовков местами
        if (ShouldSwapHeaders(headerInstance, targetField, out var sourceField, out var targetMapping, out var sourceMapping))
        {
            await HandleHeaderSwap(sourceField!, targetField, sourceMapping!, targetMapping!, updatedMapping);
            return;
        }

        // Стандартная логика замещения/назначения
        await HandleStandardFieldDrop(headerInstance, targetField, updatedMapping);
    }

    /// <summary>
    /// Определяет, следует ли поменять заголовки местами.
    /// </summary>
    private bool ShouldSwapHeaders(
        CsvHeader headerInstance,
        ModelField targetField,
        out ModelField? sourceField,
        out HeaderMapping? targetMapping,
        out HeaderMapping? sourceMapping)
    {
        sourceField = null;
        targetMapping = null;
        sourceMapping = null;

        // Проверяем, что источник - это поле модели (не пул)
        if (string.IsNullOrEmpty(headerInstance.ZoneId))
            return false;

        sourceField = Fields.FirstOrDefault(f => f.Field == headerInstance.ZoneId);
        if (sourceField == null || sourceField.Equals(targetField))
            return false;

        // Проверяем, что целевое поле имеет назначенный заголовок
        targetMapping = Mapping.FirstOrDefault(m => m.ModelField.Equals(targetField));
        if (targetMapping == null)
            return false;

        // Проверяем, что исходное поле имеет перетаскиваемый заголовок
        var tempSourceField = sourceField; // Создаем локальную переменную для использования в лямбда-выражении
        sourceMapping = Mapping.FirstOrDefault(m =>
            m.CsvHeader.Equals(headerInstance) && m.ModelField.Equals(tempSourceField));
        if (sourceMapping == null)
            return false;

        // Проверяем, что заголовки разные (нет смысла менять одинаковые)
        if (targetMapping.CsvHeader.Equals(headerInstance))
            return false;

        return true;
    }

    /// <summary>
    /// Выполняет обмен заголовков между двумя полями модели.
    /// </summary>
    private async Task HandleHeaderSwap(
        ModelField sourceField,
        ModelField targetField,
        HeaderMapping sourceMapping,
        HeaderMapping targetMapping,
        List<HeaderMapping> updatedMapping)
    {
        // Удаляем оба существующих маппинга
        updatedMapping.Remove(sourceMapping);
        updatedMapping.Remove(targetMapping);

        // Создаем новые маппинги с обменом заголовков
        var newSourceMapping = new HeaderMapping(targetMapping.CsvHeader, sourceField);
        var newTargetMapping = new HeaderMapping(sourceMapping.CsvHeader, targetField);

        updatedMapping.Add(newSourceMapping);
        updatedMapping.Add(newTargetMapping);

        await UpdateMappingAsync(updatedMapping);
    }

    /// <summary>
    /// Обрабатывает стандартное назначение/замещение заголовка на поле.
    /// </summary>
    private async Task HandleStandardFieldDrop(
        CsvHeader headerInstance,
        ModelField targetField,
        List<HeaderMapping> updatedMapping)
    {
        // Удаляем существующий маппинг для целевого поля (замещение)
        var existingMappingForField = updatedMapping.FirstOrDefault(m => m.ModelField.Equals(targetField));
        if (existingMappingForField != null)
        {
            updatedMapping.Remove(existingMappingForField);
        }

        // При перемещении из другого поля удаляем старый маппинг источника
        if (!string.IsNullOrEmpty(headerInstance.ZoneId))
        {
            var sourceField = Fields.FirstOrDefault(f => f.Field == headerInstance.ZoneId);
            if (sourceField != null && !sourceField.Equals(targetField))
            {
                var oldMapping = updatedMapping.FirstOrDefault(m =>
                    m.CsvHeader.Equals(headerInstance) && m.ModelField.Equals(sourceField));
                if (oldMapping != null)
                {
                    updatedMapping.Remove(oldMapping);
                }
            }
        }
        else if (!AllowDuplicateHeaders)
        {
            // При перемещении из пула и запрете дублирования удаляем все маппинги с этим заголовком
            var duplicateMappings = updatedMapping.Where(m => m.CsvHeader.Equals(headerInstance)).ToList();
            foreach (var duplicate in duplicateMappings)
            {
                updatedMapping.Remove(duplicate);
            }
        }

        // Создаем базовый заголовок без ZoneId для маппинга
        var baseHeader = new CsvHeader(headerInstance.Name);
        var newMapping = new HeaderMapping(baseHeader, targetField);
        updatedMapping.Add(newMapping);

        await UpdateMappingAsync(updatedMapping);
    }
    #endregion

    #region [Utility Methods]
    /// <summary>
    /// Определяет, в какой зоне должен отображаться экземпляр заголовка.
    /// Использует ZoneId экземпляра для точного размещения.
    /// </summary>
    private bool ItemSelector(CsvHeader headerInstance, string zoneIdentifier)
    {
        // Экземпляр отображается в той зоне, для которой он был создан
        return headerInstance.ZoneId == zoneIdentifier;
    }

    /// <summary>
    /// Определяет, можно ли сбросить заголовок в указанную зону.
    /// </summary>
    private bool CanDrop(CsvHeader header, string identifier)
    {
        if (identifier == string.Empty)
        {
            return true; // Всегда можно вернуть в пул
        }

        var targetField = Fields.FirstOrDefault(f => f.Field == identifier);
        if (targetField is null) return false;

        // Проверяем, есть ли уже маппинг для этого поля
        var existingMapping = Mapping.FirstOrDefault(m => m.ModelField.Equals(targetField));

        if (existingMapping != null)
        {
            // Если заголовок уже назначен на это поле, разрешаем (это перемещение внутри поля)
            if (existingMapping.CsvHeader.Equals(header))
            {
                return true;
            }

            // Если это другой заголовок, то разрешаем только замещение
            // (логика замещения будет обработана в HandleFieldDrop)
            return true;
        }

        // Если поле свободно, проверяем ограничения дублирования заголовков
        if (!AllowDuplicateHeaders)
        {
            // При запрете дублирования проверяем, не назначен ли этот заголовок на другое поле
            return !Mapping.Any(m => m.CsvHeader.Equals(header));
        }

        return true; // Поле свободно и ограничений нет
    }

    /// <summary>
    /// Обновляет маппинг и уведомляет о изменениях.
    /// </summary>
    private async Task UpdateMappingAsync(IEnumerable<HeaderMapping> newMapping)
    {
        Mapping = newMapping;

        if (MappingChanged.HasDelegate)
        {
            await MappingChanged.InvokeAsync(Mapping);
        }

        // Обновляем контейнер для перерисовки элементов в правильных зонах
        RefreshContainer();
    }
    #endregion

    private void RefreshContainer()
    {
        //update the binding to the container
        StateHasChanged();

        //the container refreshes the internal state
        _containerRef?.Refresh();
    }
}