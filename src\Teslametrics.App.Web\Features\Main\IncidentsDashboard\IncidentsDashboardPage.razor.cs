using Microsoft.AspNetCore.Components;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard;

public partial class IncidentsDashboardPage
{
    private DateTimeOffset _startOffset => new(Start, _time.LocalTimeZone.GetUtcOffset(Start));
    private DateTimeOffset _endOffset => new(End, _time.LocalTimeZone.GetUtcOffset(Start));

    [Inject]
    private TimeProvider _time { get; set; } = null!;

    [Inject]
    private NavigationManager NavigationManager { get; set; } = null!;

    [SupplyParameterFromQuery(Name = "start")]
    public DateTime Start { get; set; } = DateTime.Today;

    [SupplyParameterFromQuery(Name = "end")]
    public DateTime End { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);

    [SupplyParameterFromQuery(Name = "cityId")]
    public Guid? CityId { get; set; }

    [SupplyParameterFromQuery(Name = "buildingId")]
    public Guid? BuildingId { get; set; }

    [SupplyParameterFromQuery(Name = "floorId")]
    public Guid? FloorId { get; set; }

    [SupplyParameterFromQuery(Name = "roomId")]
    public Guid? RoomId { get; set; }

    [SupplyParameterFromQuery(Name = "deviceId")]
    public Guid? DeviceId { get; set; }

    protected override void OnParametersSet()
    {
        if (Start == default)                       // параметр отсутствовал
            Start = DateTime.Today;

        if (End == default)
            End = DateTime.Today.AddDays(1).AddMilliseconds(-1);
    }

    private void OnDateFromChanged(DateTime value)
    {
        Start = value;
        UpdateQueryString();
    }

    private void OnDateToChanged(DateTime value)
    {
        End = value;
        UpdateQueryString();
    }

    private void OnCityIdChanged(Guid? value)
    {
        CityId = value;
        UpdateQueryString();
    }

    private void OnBuildingIdChanged(Guid? value)
    {
        BuildingId = value;
        UpdateQueryString();
    }

    private void OnFloorIdChanged(Guid? value)
    {
        FloorId = value;
        UpdateQueryString();
    }

    private void OnRoomIdChanged(Guid? value)
    {
        RoomId = value;
        UpdateQueryString();
    }

    private void OnFridgeIdChanged(Guid? value)
    {
        DeviceId = value;
        UpdateQueryString();
    }

    private void UpdateQueryString()
    {
        var query = new Dictionary<string, object?>
        {
            ["start"] = Start.ToString("o"),
            ["end"] = End.ToString("o"),
            ["cityId"] = CityId,
            ["buildingId"] = BuildingId,
            ["floorId"] = FloorId,
            ["roomId"] = RoomId,
            ["deviceId"] = DeviceId
        };

        var newUri = NavigationManager.GetUriWithQueryParameters(query);
        NavigationManager.NavigateTo(newUri);
    }
}
