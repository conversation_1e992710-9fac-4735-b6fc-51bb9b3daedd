﻿@using System.Linq.Expressions

@inherits BaseAutocompleteComponent<string>
@inject ISnackbar Snackbar

<MudAutocomplete T="string"
				 Label="@Label"
				 Value="@Value"
				 TextChanged="ValueChangedHandler"
				 SearchFunc="SearchHandler"
				 ToStringFunc="@DisplayFunction"
				 AdornmentColor="@Color.Primary"
				 Validation="@Validation"
				 Clearable="Clearable"
				 MaxItems="MaxItems"
				 ShowProgressIndicator="@IsLoading"
				 Immediate="Immediate"
				 For="For"
				 HelperText="@HelperText"
				 SelectValueOnTab="true"
				 Disabled="Disabled"
				 UserAttributes="@UserAttributes"
				 ResetValueOnEmptyText="false"
				 CoerceText="true"
				 CoerceValue="true">
	<ItemTemplate Context="element">
		<MudText>
			@DisplayFunction(element)
		</MudText>
	</ItemTemplate>
</MudAutocomplete>

@code {
	public CsvDelimeterSelectComponent()
	{
		DisplayExpression = element => element;
		Items = new List<string>() { ",", ";" };
	}

	protected override bool IsObjectEquals(string element, string element2) => element == element2;

	#region Event Handlers
	private Task<IEnumerable<string>> SearchHandler(string query, CancellationToken token)
	{
		if (string.IsNullOrEmpty(query))
		{
			return Task.FromResult(Items);
		}

		if (Value is not null && query == DisplayFunction(Value))
		{
			return Task.FromResult(Items);
		}

		return Task.FromResult(Items.Where(element => element.Contains(query, StringComparison.OrdinalIgnoreCase)));
	}
	#endregion
}
