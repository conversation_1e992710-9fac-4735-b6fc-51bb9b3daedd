using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Moq;
using Teslametrics.App.Web.Services.UserDevice;
using Xunit;

namespace UnitTests;

public class UserDeviceServiceTests
{
    [Theory]
    [InlineData("Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15", DeviceType.Mobile, Teslametrics.App.Web.Services.UserDevice.OperatingSystem.iOS, true)]
    [InlineData("Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15", DeviceType.Tablet, Teslametrics.App.Web.Services.UserDevice.OperatingSystem.iOS, true)]
    [InlineData("Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36", DeviceType.Mobile, Teslametrics.App.Web.Services.UserDevice.OperatingSystem.Android, true)]
    [InlineData("Mozilla/5.0 (Linux; Android 11; SM-T870) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Safari/537.36", DeviceType.Tablet, Teslametrics.App.Web.Services.UserDevice.OperatingSystem.Android, true)]
    [InlineData("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", DeviceType.Desktop, Teslametrics.App.Web.Services.UserDevice.OperatingSystem.Windows, false)]
    [InlineData("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36", DeviceType.Desktop, Teslametrics.App.Web.Services.UserDevice.OperatingSystem.macOS, false)]
    [InlineData("Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36", DeviceType.Desktop, Teslametrics.App.Web.Services.UserDevice.OperatingSystem.Linux, false)]
    public void UserDeviceService_ShouldCorrectlyAnalyzeUserAgent(string userAgent, DeviceType expectedDeviceType, Teslametrics.App.Web.Services.UserDevice.OperatingSystem expectedOS, bool expectedIsMobile)
    {
        // Arrange
        var mockLogger = new Mock<ILogger<UserDeviceService>>();
        var service = new UserDeviceService(mockLogger.Object);
        var mockHttpContext = CreateMockHttpContext(userAgent);

        // Act
        service.InitializeFromHttpContext(mockHttpContext);

        // Assert
        Assert.Equal(expectedDeviceType, service.DeviceType);
        Assert.Equal(expectedOS, service.OperatingSystem);
        Assert.Equal(expectedIsMobile, service.IsMobile);
        Assert.Equal(userAgent, service.UserAgent);
    }

    [Fact]
    public void UserDeviceService_WithEmptyUserAgent_ShouldReturnUnknown()
    {
        // Arrange
        var mockLogger = new Mock<ILogger<UserDeviceService>>();
        var service = new UserDeviceService(mockLogger.Object);
        var mockHttpContext = CreateMockHttpContext("");

        // Act
        service.InitializeFromHttpContext(mockHttpContext);

        // Assert
        Assert.Equal(DeviceType.Unknown, service.DeviceType);
        Assert.Equal(Teslametrics.App.Web.Services.UserDevice.OperatingSystem.Unknown, service.OperatingSystem);
        Assert.False(service.IsMobile);
    }

    [Fact]
    public void UserDeviceService_InitializeFromHttpContext_ShouldStoreDeviceInfo()
    {
        // Arrange
        var mockLogger = new Mock<ILogger<UserDeviceService>>();
        var service = new UserDeviceService(mockLogger.Object);

        var userAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15";
        var mockHttpContext = CreateMockHttpContext(userAgent);

        // Act
        service.InitializeFromHttpContext(mockHttpContext);

        // Assert
        Assert.Equal(DeviceType.Mobile, service.DeviceType);
        Assert.Equal(Teslametrics.App.Web.Services.UserDevice.OperatingSystem.iOS, service.OperatingSystem);
        Assert.True(service.IsMobile);
        Assert.Equal(userAgent, service.UserAgent);
    }

    [Fact]
    public void UserDeviceService_PreventDoubleInitialization()
    {
        // Arrange
        var mockLogger = new Mock<ILogger<UserDeviceService>>();
        var service = new UserDeviceService(mockLogger.Object);

        var userAgent1 = "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15";
        var userAgent2 = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
        var mockHttpContext1 = CreateMockHttpContext(userAgent1);
        var mockHttpContext2 = CreateMockHttpContext(userAgent2);

        // Act
        service.InitializeFromHttpContext(mockHttpContext1);
        service.InitializeFromHttpContext(mockHttpContext2); // Должна быть проигнорирована

        // Assert
        Assert.Equal(DeviceType.Mobile, service.DeviceType); // Остается первое значение
        Assert.Equal(Teslametrics.App.Web.Services.UserDevice.OperatingSystem.iOS, service.OperatingSystem);
        Assert.True(service.IsMobile);
        Assert.Equal(userAgent1, service.UserAgent); // Остается первый User-Agent
    }

    [Fact]
    public void UserDeviceService_SetUserAgent_ShouldUpdateData()
    {
        // Arrange
        var mockLogger = new Mock<ILogger<UserDeviceService>>();
        var service = new UserDeviceService(mockLogger.Object);

        var userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";

        // Act
        service.SetUserAgent(userAgent);

        // Assert
        Assert.Equal(DeviceType.Desktop, service.DeviceType);
        Assert.Equal(Teslametrics.App.Web.Services.UserDevice.OperatingSystem.Windows, service.OperatingSystem);
        Assert.False(service.IsMobile);
        Assert.Equal(userAgent, service.UserAgent);
    }

    private static HttpContext CreateMockHttpContext(string userAgent)
    {
        var mockHttpContext = new Mock<HttpContext>();
        var mockRequest = new Mock<HttpRequest>();
        var mockHeaders = new Mock<IHeaderDictionary>();

        // Настраиваем заголовок User-Agent
        mockHeaders.Setup(h => h.UserAgent).Returns(new StringValues(userAgent));
        mockRequest.Setup(r => r.Headers).Returns(mockHeaders.Object);
        mockHttpContext.Setup(c => c.Request).Returns(mockRequest.Object);

        return mockHttpContext.Object;
    }
}
