using Orleans.Providers.Streams.Common;
using Orleans.Runtime;
using Orleans.Serialization;
using Orleans.Streams;

namespace Orleans.Providers
{
    [Serializable]
    [GenerateSerializer]
    [SerializationCallbacks(typeof(OnDeserializedCallbacks))]
    internal sealed class LiveBatchContainer<TSerializer> : IBatchContainer, IOnDeserialized
        where TSerializer : class, ILiveMessageBodySerializer
    {
        [NonSerialized]
        private TSerializer serializer;
        [Id(0)]
        private readonly EventSequenceToken realToken;

        public StreamId StreamId => MessageData.StreamId;
        public StreamSequenceToken SequenceToken => realToken;

        [Id(1)]
        public LiveMessageData MessageData { get; set; }
        public long SequenceNumber => realToken.SequenceNumber;

        // Payload is local cache of deserialized payloadBytes.  Should never be serialized as part of batch container.  During batch container serialization raw payloadBytes will always be used.
        [NonSerialized] private LiveMessageBody? payload;

        private LiveMessageBody Payload()
        {
            return payload ?? (payload = serializer.Deserialize(MessageData.Payload));
        }

        public LiveBatchContainer(LiveMessageData messageData, TSerializer serializer)
        {
            this.serializer = serializer;
            MessageData = messageData;
            realToken = new EventSequenceToken(messageData.SequenceNumber);
        }

        public IEnumerable<Tuple<T, StreamSequenceToken>> GetEvents<T>()
        {
            return Payload().Events.Cast<T>().Select((e, i) => Tuple.Create<T, StreamSequenceToken>(e, realToken.CreateSequenceTokenForEvent(i)));
        }

        public bool ImportRequestContext()
        {
            var context = Payload().RequestContext;
            if (context != null)
            {
                RequestContextExtensions.Import(context);
                return true;
            }
            return false;
        }

        void IOnDeserialized.OnDeserialized(DeserializationContext context)
        {
            this.serializer = LiveMessageBodySerializerFactory<TSerializer>.GetOrCreateSerializer(context.ServiceProvider);
        }
    }
}
