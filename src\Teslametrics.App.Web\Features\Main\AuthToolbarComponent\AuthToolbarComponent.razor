﻿@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms

@inherits InteractiveBaseComponent

@if (_user is not null)
{
	<div class="d_contents">
		<MudMenu Icon="@TeslaIcons.Profile.UserOutline"
				 Label="@_user.Username"
				 Class="menu_icon"
				 Variant="Variant.Filled"
				 Size="Size.Medium"
				 AriaLabel="@_user.Username">
			<MudMenuItem Href="/profile"
						 Icon="@Icons.Material.Filled.Person">Профиль</MudMenuItem>
			<MudMenuItem OnClick="ChangePasswordAsync"
						 Icon="@Icons.Material.Filled.Key">Изменить пароль</MudMenuItem>
			<MudDivider Class="my-2" />
			<MudMenuItem OnClick="SignOut"
						 Icon="@Icons.Material.Filled.Logout"
						 IconColor="Color.Warning">Выход</MudMenuItem>
		</MudMenu>
	</div>
}

<Teslametrics.App.Web.Features.Main.AuthToolbarComponent.ChangeCurrentPasswordDialog.ChangeCurrentPasswordDialog />