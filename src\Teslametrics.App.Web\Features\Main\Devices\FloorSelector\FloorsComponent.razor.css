﻿::deep {
    background: rgba(255, 255, 255, 1);
    display: flex;
    align-items: center;
    width: fit-content;
    left: 24px;
    top: 24px;
    position: absolute;
    border-radius: 64px;
    padding: 8px 4px 8px 12px;
    gap: 10px;
    z-index: 64;
    color: rgba(92, 97, 102, 1);
    font-size: 12px;
}

::deep .button {
    background: rgba(248, 252, 255, 1);
    width: 24px;
    height: 24px;
}

::deep .button.selected {
    background: var(--mud-palette-primary);
    color: var(--mud-palette-primary-text) !important;
}

::deep .button.selected:hover {
    background: var(--mud-palette-primary-lighten);
    color: var(--mud-palette-primary);
}