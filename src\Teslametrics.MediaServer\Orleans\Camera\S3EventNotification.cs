using System.Text.Json.Serialization;

namespace Teslametrics.MediaServer.Orleans.Camera;

public class S3EventNotification
{
    [JsonPropertyName("eventName")]
    public string EventName { get; set; } = default!;

    [JsonPropertyName("key")]
    public string Key { get; set; } = default!;

    [JsonPropertyName("records")]
    public List<S3EventRecord> Records { get; set; } = new();
}

public class S3EventRecord
{
    [JsonPropertyName("eventVersion")]
    public string EventVersion { get; set; } = default!;

    [JsonPropertyName("eventSource")]
    public string EventSource { get; set; } = default!;

    [JsonPropertyName("awsRegion")]
    public string AwsRegion { get; set; } = default!;

    [JsonPropertyName("eventTime")]
    public DateTime EventTime { get; set; }

    [JsonPropertyName("eventName")]
    public string EventName { get; set; } = default!;

    [JsonPropertyName("userIdentity")]
    public UserIdentity UserIdentity { get; set; } = default!;

    [JsonPropertyName("requestParameters")]
    public RequestParameters RequestParameters { get; set; } = default!;

    [JsonPropertyName("responseElements")]
    public ResponseElements ResponseElements { get; set; } = default!;

    [JsonPropertyName("s3")]
    public S3Details S3 { get; set; } = default!;

    [JsonPropertyName("source")]
    public SourceInfo Source { get; set; } = default!;
}

public class UserIdentity
{
    [JsonPropertyName("principalId")]
    public string PrincipalId { get; set; } = default!;
}

public class RequestParameters
{
    [JsonPropertyName("principalId")]
    public string PrincipalId { get; set; } = default!;

    [JsonPropertyName("region")]
    public string Region { get; set; } = default!;

    [JsonPropertyName("sourceIPAddress")]
    public string SourceIPAddress { get; set; } = default!;
}

public class ResponseElements
{
    [JsonPropertyName("x-amz-id-2")]
    public string XAmzId2 { get; set; } = default!;

    [JsonPropertyName("x-amz-request-id")]
    public string XAmzRequestId { get; set; } = default!;

    [JsonPropertyName("x-minio-deployment-id")]
    public string XMinioDeploymentId { get; set; } = default!;

    [JsonPropertyName("x-minio-origin-endpoint")]
    public string XMinioOriginEndpoint { get; set; } = default!;
}

public class S3Details
{
    [JsonPropertyName("s3SchemaVersion")]
    public string S3SchemaVersion { get; set; } = default!;

    [JsonPropertyName("configurationId")]
    public string ConfigurationId { get; set; } = default!;

    [JsonPropertyName("bucket")]
    public S3Bucket Bucket { get; set; } = default!;

    [JsonPropertyName("object")]
    public S3Object Object { get; set; } = default!;
}

public class S3Bucket
{
    [JsonPropertyName("name")]
    public string Name { get; set; } = default!;

    [JsonPropertyName("ownerIdentity")]
    public UserIdentity OwnerIdentity { get; set; } = default!;

    [JsonPropertyName("arn")]
    public string Arn { get; set; } = default!;
}

public class S3Object
{
    [JsonPropertyName("key")]
    public string Key { get; set; } = default!;

    [JsonPropertyName("size")]
    public long Size { get; set; }

    [JsonPropertyName("eTag")]
    public string ETag { get; set; } = default!;

    [JsonPropertyName("contentType")]
    public string ContentType { get; set; } = default!;

    [JsonPropertyName("userMetadata")]
    public Dictionary<string, string> UserMetadata { get; set; } = new();

    [JsonPropertyName("sequencer")]
    public string Sequencer { get; set; } = default!;
}

public class SourceInfo
{
    [JsonPropertyName("host")]
    public string Host { get; set; } = default!;

    [JsonPropertyName("port")]
    public string Port { get; set; } = default!;

    [JsonPropertyName("userAgent")]
    public string UserAgent { get; set; } = default!;
}
