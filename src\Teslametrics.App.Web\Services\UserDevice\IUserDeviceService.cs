namespace Teslametrics.App.Web.Services.UserDevice;

public interface IUserDeviceService
{
    public string UserAgent { get; }

    public bool IsMobile { get; }

    public DeviceType DeviceType { get; }

    public OperatingSystem OperatingSystem { get; }

    public void SetUserAgent(string userAgent);

    public void SetMobile(bool mobile);

    public void InitializeFromHttpContext(HttpContext httpContext);

    public DeviceType GetDeviceType();

    public OperatingSystem GetOperatingSystem();
}
