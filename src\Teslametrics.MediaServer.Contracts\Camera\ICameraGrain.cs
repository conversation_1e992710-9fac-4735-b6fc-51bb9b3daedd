using Orleans.Concurrency;

namespace Teslametrics.MediaServer.Orleans.Camera;

public interface ICameraGrain : IGrainWithGuidKey
{
    [ReadOnly]
    public Task<CameraStatus> GetStatusAsync();

    [OneWay]
    public Task ConnectRtspAsync(ConnectRtspRequest request);

    [OneWay]
    public Task ConnectOnvifAsync(ConnectOnvifRequest request);

    [OneWay]
    public Task DisconnectAsync();

    [ReadOnly]
    public Task<GetCameraStreamIdResponse> GetCameraStreamIdAsync(GetCameraStreamIdRequest request);

    [GenerateSerializer]
    public record ConnectRtspRequest(string ArchiveUri, string ViewUri, string PublicUri);

    [GenerateSerializer]
    public record ConnectOnvifRequest(string Host, int Port, string Username, string Password);

    [GenerateSerializer]
    public record GetCameraStreamIdRequest(StreamType StreamType);

    [GenerateSerializer]
    public record GetCameraStreamIdResponse(Guid? CameraStreamId);
}