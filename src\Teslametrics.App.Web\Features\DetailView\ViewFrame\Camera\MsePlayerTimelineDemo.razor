@using Teslametrics.App.Web.Components.MSEPlayer
@using Teslametrics.MediaServer.Orleans.Camera
@inherits InteractiveBaseComponent
@attribute [Route("/demo/mse-timeline")]

<PageTitle>Демонстрация временной шкалы MSE плеера</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large"
              Class="mt-4">
    <MudText Typo="Typo.h4"
             Class="mb-4">Демонстрация временной шкалы MSE плеера</MudText>

    <MudPaper Class="pa-4 mb-4">
        <MudText Typo="Typo.h6"
                 Class="mb-2">Параметры камеры</MudText>

        <MudGrid>
            <MudItem xs="12"
                     sm="6">
                <MudTextField @bind-Value="CameraId"
                              Label="ID камеры"
                              Variant="Variant.Outlined"
                              HelperText="Введите GUID камеры"
                              Class="mb-2" />
            </MudItem>

            <MudItem xs="12"
                     sm="6">
                <MudSelect @bind-Value="StreamType"
                           Label="Тип потока"
                           Variant="Variant.Outlined"
                           Class="mb-2">
                    <MudSelectItem Value="1">Archive</MudSelectItem>
                    <MudSelectItem Value="2">View</MudSelectItem>
                    <MudSelectItem Value="4">Public</MudSelectItem>
                </MudSelect>
            </MudItem>
        </MudGrid>

        <MudButton Variant="Variant.Filled"
                   Color="Color.Primary"
                   OnClick="ApplySettings"
                   Class="mt-2">
            Применить настройки
        </MudButton>
    </MudPaper>

    <MudPaper Class="pa-4">
        <MudText Typo="Typo.h6"
                 Class="mb-2">MSE плеер с временной шкалой</MudText>

        @if (string.IsNullOrEmpty(AppliedCameraId))
        {
            <MudText Typo="Typo.body2"
                     Class="mb-4">
                Введите ID камеры и нажмите "Применить настройки" для просмотра потока с камерой.
            </MudText>
        }
        else
        {
            <MudText Typo="Typo.body2"
                     Class="mb-4">
                Просмотр потока с камеры ID: @AppliedCameraId, тип потока: @Teslametrics.MediaServer.Orleans.Camera.StreamType.View.GetName()
            </MudText>

            <!-- MSE плеер -->
            <div class="mb-4">
                <MsePlayer @ref="msePlayer"
                           CameraId="@Guid.Parse(AppliedCameraId)"
                           Type="@Teslametrics.MediaServer.Orleans.Camera.StreamType.View">
                    <ToolbarContent>
                        <PlayToggleButton Player="context" />
                        <VolumeComponent Player="context"
                                         ShowForced="true" />
                        <SeekToLiveButton Player="context" />
                        <MudSpacer />
                        <FullscreenToggleButton Player="context" />
                    </ToolbarContent>
                </MsePlayer>
            </div>

            <!-- Временная шкала -->
            @if (msePlayer != null)
            {
                <VideoTimeline @ref="videoTimeline"
                               Player="msePlayer"
                               UpdateInterval="250"
                               WindowSizeHours="3" />
            }

            <!-- Информация о текущем времени -->
            <MudPaper Class="pa-3 mt-4"
                      Elevation="0"
                      Outlined="true">
                <MudText Typo="Typo.subtitle2"
                         Class="mb-2">Информация о времени воспроизведения</MudText>

                <MudGrid>
                    <MudItem xs="12"
                             sm="6">
                        <MudText Typo="Typo.body2">
                            <strong>Текущее время сегмента:</strong><br />
                            @(CurrentSegmentTime ?? "Не определено")
                        </MudText>
                    </MudItem>
                    <MudItem xs="12"
                             sm="6">
                        <MudText Typo="Typo.body2">
                            <strong>Абсолютное время:</strong><br />
                            @(CurrentAbsoluteTime ?? "Не определено")
                        </MudText>
                    </MudItem>
                </MudGrid>

                <MudButton Variant="Variant.Text"
                           Color="Color.Primary"
                           OnClick="RefreshTimeInfo"
                           Class="mt-2">
                    Обновить информацию о времени
                </MudButton>
            </MudPaper>

            <!-- Диагностическая информация -->
            @if (Diagnostics != null)
            {
                <MudPaper Class="pa-3 mt-4"
                          Elevation="0"
                          Outlined="true">
                    <MudText Typo="Typo.subtitle2"
                             Class="mb-2">Диагностика синхронизации времени</MudText>

                    <MudGrid>
                        <MudItem xs="12" sm="6">
                            <MudText Typo="Typo.body2">
                                <strong>Системное время:</strong><br/>
                                @Diagnostics.SystemTime.ToString("dd.MM.yyyy HH:mm:ss.fff")
                            </MudText>
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <MudText Typo="Typo.body2">
                                <strong>Время плеера:</strong><br/>
                                @(Diagnostics.PlayerTime?.ToString("dd.MM.yyyy HH:mm:ss.fff") ?? "Не определено")
                            </MudText>
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <MudText Typo="Typo.body2">
                                <strong>Отображаемое время:</strong><br/>
                                @(Diagnostics.DisplayedTime?.ToString("dd.MM.yyyy HH:mm:ss.fff") ?? "Не определено")
                            </MudText>
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <MudText Typo="Typo.body2">
                                <strong>Погрешность (drift):</strong><br/>
                                <span class="@(Math.Abs(Diagnostics.TimeDriftSeconds ?? 0) > 5 ? "text-danger" : "text-success")">
                                    @(Diagnostics.TimeDriftSeconds?.ToString("F2") ?? "N/A") сек
                                </span>
                            </MudText>
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <MudText Typo="Typo.body2">
                                <strong>Интервал обновления:</strong><br/>
                                @Diagnostics.UpdateInterval мс
                            </MudText>
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <MudText Typo="Typo.body2">
                                <strong>Последнее обновление:</strong><br/>
                                @Diagnostics.LastUpdateTime.ToString("HH:mm:ss.fff")
                            </MudText>
                        </MudItem>
                    </MudGrid>

                    @if (Math.Abs(Diagnostics.TimeDriftSeconds ?? 0) > 5)
                    {
                        <MudAlert Severity="Severity.Warning" Class="mt-2">
                            Обнаружена значительная погрешность времени! Возможны проблемы с синхронизацией.
                        </MudAlert>
                    }
                </MudPaper>
            }
        }
    </MudPaper>
</MudContainer>
