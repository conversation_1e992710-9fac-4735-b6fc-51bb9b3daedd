﻿namespace Teslametrics.Core.Services.Outbox;

public interface IOutboxStorage
{
    public Task<List<OutboxMessage>> GetListAsync();

    public OutboxMessage Add(OutboxMessage outboxMessage);

    public Task<OutboxMessage> AddAsync(OutboxMessage outboxMessage);

    public void AddRange(IEnumerable<OutboxMessage> outboxMessages);

    public Task AddRangeAsync(IEnumerable<OutboxMessage> outboxMessages);

    public void RemoveRange(IEnumerable<OutboxMessage> outboxMessages);

    public Task SaveChangesAsync();

    public Task<OutboxMessage?> GetNextAsync();

    public void Remove(OutboxMessage outboxMessage);
}