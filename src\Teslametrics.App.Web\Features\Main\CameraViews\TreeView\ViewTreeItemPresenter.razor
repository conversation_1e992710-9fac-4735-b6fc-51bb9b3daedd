﻿@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<MudTreeViewItem Value="@Presenter.Value"
                 CanExpand="@Presenter.Expandable"
                 Selected="@Presenter.Selected"
                 T="Guid"
                 OnClick="SelectAsync"
                 Class="class"
                 TextClass="TextClass">
    <Content>
        <div class="d-flex flex-row align-center mud-width-full">
            <div class="py-2 px-1 d-flex flex-row align-center overflow-auto">
                <MudIcon Icon="@Presenter.Icon"
                         Class="ml-0 mr-2 icon"
                         Color="@Color.Default"
                         Size="Size.Small" />
                <MudText Class="text">@Presenter.Text</MudText>
            </div>
            <MudSpacer />
            <MudMenu Icon="@Icons.Material.Filled.MoreVert"
                     AriaLabel="Действия с видом"
                     Color="Color.Primary"
                     Size="Size.Small">
                <MudMenuItem OnClick="ShowView"
                             Icon="@TeslaIcons.Actions.StartView">Запустить просмотр
                </MudMenuItem>

                <AuthorizeView Policy="@AppPermissions.Main.CameraViews.Update.GetEnumPermissionString()"
                               Resource="@(new PolicyRequirementResource(Presenter.OrganizationId, Presenter.Id))"
                               Context="editContext">
                    <MudMenuItem OnClick="EditView"
                                 IconColor="Color.Warning"
                                 Icon="@Icons.Material.Outlined.Edit">Настройки вида
                    </MudMenuItem>
                </AuthorizeView>
                <AuthorizeView Policy="@AppPermissions.Main.CameraViews.Delete.GetEnumPermissionString()"
                               Resource="@(new PolicyRequirementResource(Presenter.OrganizationId, Presenter.Id))"
                               Context="deleteContext">
                    <MudDivider />
                    <MudMenuItem OnClick="DeleteView"
                                 IconColor="Color.Error"
                                 Icon="@Icons.Material.Outlined.Delete">Удалить вид
                    </MudMenuItem>
                </AuthorizeView>
            </MudMenu>
        </div>
    </Content>
</MudTreeViewItem>