using Microsoft.EntityFrameworkCore;
using Teslametrics.Core.Abstractions;
using Teslametrics.Core.Domain.AccessControl.Organizations;

namespace Teslametrics.Core.Services.Persistence;

public class OrganizationRepository : BaseRepository<OrganizationAggregate>, IOrganizationRepository
{
    public OrganizationRepository(CommandAppDbContext dbContext)
        : base(dbContext)
    {
    }

    public Task<List<OrganizationAggregate>> FindByOwnerId(Guid ownerId,
                                                           CancellationToken cancellationToken = default) =>
        DbContext.Set<OrganizationAggregate>()
            .AsTracking()
            .Where(o => o.OwnerId == ownerId)
            .OrderBy(entity => entity.OwnerId)
            .ToListAsync(cancellationToken);

    public Task<List<OrganizationAggregate>> GetOrganizationsByOwnerIdAsync(Guid ownerId,
                                                                            CancellationToken cancellationToken = default) =>
        DbContext.Set<OrganizationAggregate>()
            .AsTracking()
            .Where(o => o.OwnerId == ownerId)
            .OrderBy(entity => entity.OwnerId)
            .ToListAsync(cancellationToken);

    public Task<bool> IsOrganizationNameExistsAsync(string name,
                                                    CancellationToken cancellationToken = default) =>
        DbContext.Set<OrganizationAggregate>()
            .AsNoTracking()
            .AnyAsync(entity => entity.Name == name, cancellationToken);

    public Task<bool> IsOrganizationExistsAsync(Guid organizationId,
                                                CancellationToken cancellationToken = default) =>
        DbContext.Set<OrganizationAggregate>()
            .AsNoTracking()
            .AnyAsync(entity => entity.Id == organizationId, cancellationToken: cancellationToken);

    public Task<bool> IsPresetUsedInQuotasAsync(Guid presetId,
                                                CancellationToken cancellationToken = default) =>
        DbContext.Set<OrganizationAggregate>()
            .AsNoTracking()
            .AnyAsync(entity => entity.CameraQuotas.Any(quota => quota.PresetId == presetId), cancellationToken);
}