﻿@inherits InteractiveBaseComponent

<MudStack Class="mb-8"
          Spacing="8">
    <MudPaper Outlined="true"
              Class="pa-4"
              Style="width:fit-content">
        @if (_response is not null && _response.IsSuccess)
        {
            <MudImage Src="@_response!.QRCode"
                      alt="QR для 2FA"
                      style="width: 200px;" />
        }
        else
        {
            <MudProgressCircular />
        }
    </MudPaper>
    <MudText Typo="Typo.h2">Необходимо подключить двухфакторную аутентификацию</MudText>
</MudStack>

<MudStack Spacing="6">
    <MudTextField Variant="Variant.Outlined"
                  Margin="Margin.Dense"
                  @bind-Value="_code"
                  Placeholder="Введите код"
                  Immediate="true"
                  ShrinkLabel="false"
                  @ref="_codeFieldRef"
                  MaxLength="6" />
    <MudButton OnClick="SubmitAsync"
               Variant="Variant.Filled"
               Color="Color.Primary"
               Disabled="_code.Length != 6">Продолжить</MudButton>
</MudStack>