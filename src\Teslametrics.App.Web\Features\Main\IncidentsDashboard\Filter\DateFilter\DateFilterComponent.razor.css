::deep .toggle_group {
    gap: 4px;
}

::deep .range-btn,
::deep .toggle_group >button {
    border-radius: 6px;
    height: 32px;
}

::deep .range-btn.active,
::deep .toggle_group>button[aria-checked="true"] {
    background: var(--color-primary-800);
    color: var(--mud-palette-primary-text);
}

/* Quick date range panel */
::deep .quick-range {
    border-radius: 8px;
    width: fit-content;
    border: 1px solid var(--color-stroke-stroke);
}

.mud_theme_dark div ::deep .quick-range {
    border: 1px solid var(--mud-palette-lines-inputs);
}

.mud_theme_dark div ::deep .range-btn.active,
.mud_theme_dark div ::deep .toggle_group>button[aria-checked="true"] {
    background: var(--color-primary-800);
    color: var(--mud-palette-primary-text);
}