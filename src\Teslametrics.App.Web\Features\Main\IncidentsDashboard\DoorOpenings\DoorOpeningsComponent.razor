﻿@inherits InteractiveBaseComponent
<MudPaper Elevation="0"
          Outlined="true"
          Class="pa-4">
    <div class="d-flex align-center gap-3">
        <MudText Typo="Typo.subtitle1"
                 Class="mb-4">Открытие дверей</MudText>
        <MudSpacer />
        <MudText Class="legend door_opened">Количество открытий</MudText>
        <MudText Class="legend average_time">Среднее время открытия за период, мин</MudText>
    </div>
    <div id="door-openings-chart"
         style="height: 300px;"></div>
</MudPaper>
<template id="doorTooltipTemplate">
    <div class="door-custom-tooltip">
        <div class="tooltip-date"></div>
        <div class="tooltip-value door-opened">
            <div class="tooltip-indicator"></div>
            <span class="tooltip-text">Открытий&nbsp;<b class="tooltip-value-text"></b></span>
        </div>
        <div class="tooltip-value average-time">
            <div class="tooltip-indicator"></div>
            <span class="tooltip-text">Ср. время&nbsp;<b class="tooltip-value-text"></b></span>
        </div>
    </div>
</template>