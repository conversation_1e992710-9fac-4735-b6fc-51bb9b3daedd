using MudBlazor;
using Teslametrics.App.Web.Abstractions;

namespace Teslametrics.App.Web.Components.Drawer;

public class DrawerOptions
{
	public string Width { get; set; } = StyleVariables.ActionDrawerWidth;
	public Anchor Anchor { get; set; } = Anchor.Right;

	public DrawerVariant Variant { get; set; } = DrawerVariant.Temporary;
	public DrawerClipMode ClipMode { get; set; } = DrawerClipMode.Never;

	public bool OverlayAutoClose { get; set; } = true;
	public bool Overlay { get; set; } = true;

	public bool ShowCloseButton { get; set; } = true;

	public int XSColumnsWidth { get; set; } = 12;
	public int SMColumnsWidth { get; set; } = 9;
	public int MDColumnsWidth { get; set; } = 6;
	public int LGColumnsWidth { get; set; } = 5;
	public int XLColumnsWidth { get; set; } = 4;
	public int XXLColumnsWidth { get; set; } = 3;
}
