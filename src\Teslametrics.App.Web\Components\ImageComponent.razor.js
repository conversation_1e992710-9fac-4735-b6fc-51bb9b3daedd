export async function loadImageWithRetry(imgElement, url) {
  //dotNetRef.invokeMethodAsync("OnImageLoading", version);
  try {
    await loadOnce(url); // проверяем во временном Image()
    imgElement.src = url; // подменяем только после удачной проверки
  } catch (error) {
    console.error("Error loading image:", error);
  }
}

function loadOnce(url) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = img.onabort = () => reject();
    img.src = addCacheBuster(url);
  });
}

function addCacheBuster(url) {
  const sep = url.includes("?") ? "&" : "?";
  return `${url}${sep}cb=${Date.now()}`;
}
