using System.Reactive;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Teslametrics.App.Web.Extensions;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.CameraViews.TreeView;

public partial class TreeViewComponent
{
	#region [Fields]
	private bool _disposedValue;

	private string _orderBy = "Name";
	private OrderDirection _orderDirection = OrderDirection.Ascending;

	private bool _subscribing;
	private DateTime _lastRefreshTime = DateTime.Now;
	private SubscribeTreeUseCase.Response? _subscriptionResult;
	private GetTreeUseCase.Response? _response;
	private List<MudBlazor.TreeItemData<Guid>> _items = [];
	#endregion

	#region [Parameters]
	[Parameter]
	public EventCallback<(Guid? OrganizationId, Guid? ViewId)> OnParametersChanged { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid? OrganizationId { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid? ViewId { get; set; }
	#endregion

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();
		await FetchAsync();

		AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
	}

	protected override void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{
				AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
			}

			_disposedValue = true;
		}

		base.Dispose(disposing);
	}

	private async Task FetchAsync()
	{
		if (IsLoading) return;

		await SetLoadingAsync(true);
		try
		{
			var userId = await GetCurrentUserIdAsync() ?? throw new UnauthorizedAccessException();
			_response = await ScopeFactory.MediatorSend(new GetTreeUseCase.Query(userId, _orderBy, _orderDirection));
		}
		catch (Exception ex)
		{
			_response = null;
			Snackbar.Add("Не удалось получить список видов камер из-за ошибки сервера. Повторите попытку и обратитесь к администратору", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetLoadingAsync(false);
		if (_response is null) return;

		switch (_response.Result)
		{
			case GetTreeUseCase.Result.Success:
				_lastRefreshTime = DateTime.Now;
				var buffer = ConvertResponseToTreeItemPresenters(_response);
				TransferState(_items, buffer);
				_items.Clear();
				_items.AddRange(buffer);
				buffer.Clear();
				buffer = null;
				await SubscribeAsync();
				break;
			case GetTreeUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось получить список видов камер. Повторите попытку", MudBlazor.Severity.Error);
				break;
			case GetTreeUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(TreeViewComponent), nameof(GetTreeUseCase));
				Snackbar.Add($"Не удалось получить список видов камер из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(TreeViewComponent), nameof(GetTreeUseCase), _response.Result);
				Snackbar.Add($"Не удалось получить список видов камер из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private List<MudBlazor.TreeItemData<Guid>> ConvertResponseToTreeItemPresenters(GetTreeUseCase.Response response)
	{
		var result = new List<MudBlazor.TreeItemData<Guid>>();

		if (response?.Items == null) return result;

		// Добавляем организации и их виды
		foreach (var organization in response.Items)
		{
			// Создаем узел организации
			var orgPresenter = new OrganizationTreeItemPresenter.OrganizationItemPresenter(organization.Id, organization.Name)
			{
				Expanded = true,
				CameraCount = organization.CameraCount,
				ViewCount = organization.ViewCount,
				Children = []
			};
			result.Add(orgPresenter);

			// Добавляем виды для этой организации
			foreach (var view in organization.Views)
			{
				var viewPresenter = new ViewTreeItemPresenter.ViewItemPresenter(view.Id, organization.Id, view.Name, view.GridType)
				{
					CameraCount = view.CameraCount,
					OrganizationId = organization.Id
				};
				orgPresenter.Children?.Add(viewPresenter);
			}
		}

		return result;
	}

	private void TransferState(List<MudBlazor.TreeItemData<Guid>> oldItems, List<MudBlazor.TreeItemData<Guid>> newItems)
	{
		// Создаем словарь для быстрого поиска старых элементов по Id
		var oldItemsMap = oldItems.ToDictionary(item => item.Value, item => item);

		// Для каждого нового элемента проверяем, был ли соответствующий старый элемент развернут
		foreach (var newItem in newItems)
		{
			if (oldItemsMap.TryGetValue(newItem.Value, out var oldItem))
			{
				newItem.Expanded = oldItem.Expanded;
			}
		}
	}

	private async Task SubscribeAsync()
	{
		Unsubscribe();

		await SetSubscribingAsync(true);
		var orgIds = _items.Select(x => x.Value).ToList();
		if (orgIds.Count == 0)
		{
			Snackbar.Add("Нет доступных для просмотра организаций. Невозможно подписаться на обновления.", MudBlazor.Severity.Error);
			return;
		}
		try
		{
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeTreeUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), orgIds));
		}
		catch (Exception ex)
		{
			_subscriptionResult = null;
			Snackbar.Add("Не удалось получить подписку на события дерева из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetSubscribingAsync(false);
		if (_subscriptionResult is null) return;

		switch (_subscriptionResult.Result)
		{
			case SubscribeTreeUseCase.Result.Success:
				CompositeDisposable.Add(_subscriptionResult.Subscription!);
				break;
			case SubscribeTreeUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
				break;
			case SubscribeTreeUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(TreeViewComponent), nameof(SubscribeTreeUseCase));
				Snackbar.Add($"Не удалось получить подписку на события дерева из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(TreeViewComponent), nameof(SubscribeTreeUseCase), _subscriptionResult.Result);
				Snackbar.Add($"Не удалось получить подписку на события дерева из-за ошибки: {_subscriptionResult.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	private Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private async Task SelectViewAsync(ViewTreeItemPresenter.ViewItemPresenter view)
	{
		OrganizationId = view.OrganizationId;
		ViewId = view.Id;

		if (OnParametersChanged.HasDelegate)
			await OnParametersChanged.InvokeAsync((OrganizationId, ViewId));
	}


	private Task RefreshAsync() => FetchAsync();
	#endregion

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		await RefreshAsync();
		await UpdateViewAsync();
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}

	private async void OnAuthenticationStateChanged(Task<AuthenticationState> authState)
	{
		await FetchAsync();
	}
	#endregion
}
