using Blazor.Diagrams.Core.Controls;
using Blazor.Diagrams.Core.Models.Base;
using Blazor.Diagrams.Core;
using Blazor.Diagrams.Core.Geometry;
using Blazor.Diagrams.Core.Events;
using Blazor.Diagrams.Core.Models;
using Teslametrics.App.Web.Components.Diagrams.Models;
using Teslametrics.App.Web.Components.Diagrams.Behaivor;

namespace Teslametrics.App.Web.Components.Diagrams.Controls;

public class ResizeControl(ResizerControlPosition position, uint offset) : ExecutableControl
{
    protected readonly uint Offset = offset;

    public readonly ResizerControlPosition Position = position;

    public override Point? GetPosition(Model model)
    {
        if (model is not NodeModel nodeModel || nodeModel.Size is null || nodeModel.Locked)
            return null;

        return Position switch
        {
            ResizerControlPosition.TopLeft => new Point(nodeModel.Position.X - Offset, nodeModel.Position.Y - Offset),
            ResizerControlPosition.TopRight => new Point(nodeModel.Position.X + nodeModel.Size.Width + Offset, nodeModel.Position.Y - Offset),
            ResizerControlPosition.BottomLeft => new Point(nodeModel.Position.X - Offset, nodeModel.Position.Y + nodeModel.Size.Height + Offset),
            ResizerControlPosition.BottomRight => new Point(nodeModel.Position.X + nodeModel.Size.Width + Offset, nodeModel.Position.Y + nodeModel.Size.Height + Offset),
            _ => null,
        };
    }

    public override ValueTask OnPointerDown(Diagram diagram, Model model, PointerEventArgs e)
    {
        if (model is not NodeModel node) throw new Exception($"{model} is not NodeModel");
        if (model.Locked) return ValueTask.CompletedTask;

        var behavior = diagram.GetBehavior<ResizeBehaivor>() ?? throw new DiagramsException($"NewResizeBehaivor was not found");

        behavior.OnResizeStart(node, e, Position);
        return ValueTask.CompletedTask;
    }
}