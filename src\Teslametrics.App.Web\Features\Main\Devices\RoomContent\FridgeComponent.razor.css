::deep .list_item.error {
    border: 1px solid rgba(255, 214, 219, 1);
}

::deep .list_item:hover .header {
	background-color: var(--color-bg-2);
}

::deep .list_item:not(:first-child) {
    margin-top: 8px;
}

::deep .list_item .content {
    display: flex;
    flex-direction: row;
    align-content: center;
    align-items: center;
    gap: 10px;
}

::deep .item {
    background: var(--color-system-err-bg);
    border-radius: 8px;
    padding: 16px;
}

::deep .item .link {
    color: var(--mud-palette-error);
}

.tile_grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

::deep .tile:last-child:nth-child(odd) {
    grid-column: span 2;
}

::deep .tile {
    border-radius: 12px;
}
