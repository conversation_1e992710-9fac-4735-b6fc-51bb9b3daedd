using MediatR;
using Microsoft.Extensions.Configuration;
using System.Security.Claims;
using Teslametrics.Core.Domain.AccessControl.Organizations;
using Teslametrics.Core.Domain.AccessControl.Users;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Shared;

namespace Teslametrics.Core.Domain.AccessControl;

public class LogInDomainService
{
    private readonly IUserRepository _userRepository;
    private readonly IOrganizationRepository _organizationRepository;
    private readonly IPermissionRepository _permissionRepository;
    private readonly IPublisher _publisher;
    private readonly IOutbox _outbox;

    public LogInDomainService(IUserRepository userRepository,
                              IOrganizationRepository organizationRepository,
                              IPermissionRepository permissionRepository,
                              IPublisher publisher,
                              IOutbox outbox)
    {
        _userRepository = userRepository;
        _organizationRepository = organizationRepository;
        _permissionRepository = permissionRepository;
        _publisher = publisher;
        _outbox = outbox;
    }

    public async Task<List<(string, string)>> LogInAsync(UserAggregate user, CancellationToken cancellationToken)
    {
        var response = new List<(string, string)>
        {
            (ClaimTypes.NameIdentifier, user.Id.ToString())
        };

        var appPermissions = AppPermissions.GetAll();

        if (user.IsSystem)
        {
            response.AddRange(appPermissions.Select(p => ("Permission", $"*/{p}/*")));
        }
        else
        {
            var organizations = await _organizationRepository.GetOrganizationsByOwnerIdAsync(user.Id, cancellationToken);

            string[] exceptPermission =
            [
                Fqdn<AppPermissions>.GetName(AppPermissions.Main.AccessControl.Organizations.Create),
                Fqdn<AppPermissions>.GetName(AppPermissions.Main.AccessControl.Organizations.Delete),
                Fqdn<AppPermissions>.GetName(AppPermissions.Main.CameraPresets.Read),
                Fqdn<AppPermissions>.GetName(AppPermissions.Main.CameraPresets.Create),
                Fqdn<AppPermissions>.GetName(AppPermissions.Main.CameraPresets.Update),
                Fqdn<AppPermissions>.GetName(AppPermissions.Main.CameraPresets.Delete),
                Fqdn<AppPermissions>.GetName(AppPermissions.Main.CameraPublicAccess.Create),
                Fqdn<AppPermissions>.GetName(AppPermissions.Main.CameraPublicAccess.Update),
                Fqdn<AppPermissions>.GetName(AppPermissions.Main.CameraPublicAccess.Delete),
            ];

            foreach (var organization in organizations)
            {
                response.AddRange(appPermissions.Where(p => !exceptPermission.Contains(p)).Select(p => ("Permission", $"{organization.Id}/{p}/*")));
            }

            var permissions = await _permissionRepository.GetPermissions(user.Roles.Select(r => r.RoleId), cancellationToken);

            response.AddRange(permissions.Select(p =>
            {
                var organization = p.OrganizationId == SystemConsts.RootOrganizationId
                                   ? "*"
                                   : p.OrganizationId.ToString();

                var resource = p.ResourceId == SystemConsts.ResourceWildcardId
                               ? "*"
                               : p.ResourceId.ToString();

                return ("Permission", $"{organization}/{p.Permission}/{resource}");
            }));
        }

        var events = user.LogIn(DateTimeOffset.UtcNow);

        await _userRepository.SaveChangesAsync(cancellationToken);

        foreach (var @event in events)
        {
            await _publisher.Publish(@event, cancellationToken);
        }

        await _outbox.AddRangeAsync(events);

        return response;
    }
}