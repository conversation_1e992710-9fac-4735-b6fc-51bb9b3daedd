using Microsoft.AspNetCore.Components;

namespace Teslametrics.App.Web.Features.Main.Incidents.Filter.TimeRangePicker;

public partial class TimeRangePickerComponent
{
    private int _fromHours;
    private int _fromMinutes;
    private int _toHours;
    private int _toMinutes;

    [Parameter]
    public TimeSpan TimeFrom { get; set; }

    [Parameter]
    public EventCallback<TimeSpan?> TimeFromChanged { get; set; }

    [Parameter]
    public TimeSpan TimeTo { get; set; }

    [Parameter]
    public EventCallback<TimeSpan?> TimeToChanged { get; set; }



    protected override void OnParametersSet()
    {
        _fromHours = TimeFrom.Hours;
        _fromMinutes = TimeFrom.Minutes;
        _toHours = TimeTo.Hours;
        _toMinutes = TimeTo.Minutes;

        base.OnParametersSet();
    }

    private async Task OnFromHoursChanged(int hours)
    {
        var clampedHours = Math.Clamp(hours, 0, 23);
        if (_fromHours != clampedHours)
        {
            _fromHours = clampedHours;
            var newTimeFrom = new TimeSpan(_fromHours, _fromMinutes, 0);

            if (TimeFromChanged.HasDelegate)
            {
                await TimeFromChanged.InvokeAsync(newTimeFrom);
            }

            StateHasChanged();
        }
    }

    private async Task OnFromMinutesChanged(int minutes)
    {
        var clampedMinutes = Math.Clamp(minutes, 0, 59);
        if (_fromMinutes != clampedMinutes)
        {
            _fromMinutes = clampedMinutes;
            var newTimeFrom = new TimeSpan(_fromHours, _fromMinutes, 0);

            if (TimeFromChanged.HasDelegate)
            {
                await TimeFromChanged.InvokeAsync(newTimeFrom);
            }

            StateHasChanged();
        }
    }

    private async Task OnToHoursChanged(int hours)
    {
        var clampedHours = Math.Clamp(hours, 0, 23);
        if (_toHours != clampedHours)
        {
            _toHours = clampedHours;
            var newTimeTo = new TimeSpan(_toHours, _toMinutes, 0);

            if (TimeToChanged.HasDelegate)
            {
                await TimeToChanged.InvokeAsync(newTimeTo);
            }

            StateHasChanged();
        }
    }

    private async Task OnToMinutesChanged(int minutes)
    {
        var clampedMinutes = Math.Clamp(minutes, 0, 59);
        if (_toMinutes != clampedMinutes)
        {
            _toMinutes = clampedMinutes;
            var newTimeTo = new TimeSpan(_toHours, _toMinutes, 0);

            if (TimeToChanged.HasDelegate)
            {
                await TimeToChanged.InvokeAsync(newTimeTo);
            }

            StateHasChanged();
        }
    }
}
