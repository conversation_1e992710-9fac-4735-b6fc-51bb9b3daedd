﻿@using Teslametrics.MediaServer.Orleans.Camera
@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<div class="d-flex gap-2 align-center justify-space-between mud-width-full">
	@if (IsBlocked)
	{
		<MudChip T="string"
				 Color="Color.Error">Заблокирована</MudChip>
	}
	else
	{
		@switch (CameraStatus)
		{
			case CameraStatus.Running:
				<MudChip T="string"
						 Icon="@TeslaIcons.Devices.Camera"
						 Variant="Variant.Text"
						 Color="Color.Success">Подключена</MudChip>
				break;
			case CameraStatus.Stopped:
				<MudChip T="string"
						 Icon="@TeslaIcons.Devices.Camera"
						 Variant="Variant.Text"
						 Color="Color.Warning">Отключена</MudChip>
				break;
			case CameraStatus.Connecting:
				<MudChip T="string"
						 Icon="@TeslaIcons.Devices.Camera"
						 Variant="Variant.Text"
						 Color="Color.Info">Подключается</MudChip>
				break;
			case CameraStatus.Problem:
				<MudChip T="string"
						 Variant="Variant.Text"
						 Icon="@TeslaIcons.Devices.Camera"
						 Color="Color.Info">Ошибка</MudChip>
				break;
			default:
				break;
		}
		<div class="d-flex align-center">
			@if (IsLoading)
			{
				<MudProgressCircular Color="Color.Info"
									 Indeterminate="true" />
			}
			<MudSwitch T="bool"
					   Value="@(CameraStatus != CameraStatus.Stopped)"
					   ValueChanged="OnSwitchChanged"
					   Disabled="@(!_isAccessGranted || IsLoading)" />
		</div>
	}
</div>