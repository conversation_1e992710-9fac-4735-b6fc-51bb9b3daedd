using System.Reactive;
using FluentValidation;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Events.Presets;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Presets.Drawer.Edit;

public partial class PresetEditComponent
{
	private class Model(Guid id, string name, PresetFormComponent.Config archiveStreamConfig, PresetFormComponent.Config viewStreamConfig, PresetFormComponent.Config publicStreamConfig)
	{
		public Guid Id { get; set; } = id;
		public string Name { get; set; } = name;

		public PresetFormComponent.Config ArchiveStreamConfig { get; set; } = archiveStreamConfig;
		public PresetFormComponent.Config ViewStreamConfig { get; set; } = viewStreamConfig;
		public PresetFormComponent.Config PublicStreamConfig { get; set; } = publicStreamConfig;
	}

	private class Validator : BaseFluentValidator<Model>
	{
		public Validator()
		{
			RuleFor(model => model.Name)
				.Length(3, 60).WithMessage("наименование должно быть длиной от 3 до 60 символов");
		}
	}

	private MudTextField<string>? _nameFieldRef;
	private DateTime _lastUpdateTime = DateTime.UtcNow;
	private bool _subscribing;
	private bool _isValid;
	private Model? _model;
	private GetCameraPresetUseCase.Response? _response;
	private SubscribeCameraPresetUseCase.Response? _subscriptionResult;
	private Validator _validator = new();

	#region Parameters
	[Parameter]
	[EditorRequired]
	public Guid PresetId { get; set; }

	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;
	#endregion

	protected override async Task OnParametersSetAsync()
	{
		await base.OnParametersSetAsync();

		if (_response is null || PresetId != _response.Id)
		{
			_model = null;
			await FetchAsync();
			await SubscribeAsync();
		}
	}

	private async Task FetchAsync()
	{
		if (IsLoading) return;
		try
		{
			await SetLoadingAsync(true);
			_response = await ScopeFactory.MediatorSend(new GetCameraPresetUseCase.Query(PresetId));
		}
		catch (Exception ex)
		{
			Logger.LogError(ex, ex.Message);
			Snackbar.Add("Не удалось получить камеру из-за непредвиденной ошибки.");
		}
		finally
		{
			await SetLoadingAsync(false);
		}

		if (_response is null) return;

		_lastUpdateTime = DateTime.UtcNow;
		switch (_response.Result)
		{
			case GetCameraPresetUseCase.Result.Success:
				PresetFormComponent.Config archiveStreamConfig = new();
				PresetFormComponent.Config viewStreamConfig = new();
				PresetFormComponent.Config publicStreamConfig = new();

				if (_response.ArchiveStreamConfig is not null)
				{
					archiveStreamConfig = new(_response.ArchiveStreamConfig.Resolution, _response.ArchiveStreamConfig.VideoCodec, _response.ArchiveStreamConfig.FrameRate, _response.ArchiveStreamConfig.SceneDynamic, _response.ArchiveStreamConfig.AudioCodec);
				}
				if (_response.ViewStreamConfig is not null)
				{
					viewStreamConfig = new(_response.ViewStreamConfig.Resolution, _response.ViewStreamConfig.VideoCodec, _response.ViewStreamConfig.FrameRate, _response.ViewStreamConfig.SceneDynamic, _response.ViewStreamConfig.AudioCodec);
				}
				if (_response.PublicStreamConfig is not null)
				{
					publicStreamConfig = new(_response.PublicStreamConfig.Resolution, _response.PublicStreamConfig.VideoCodec, _response.PublicStreamConfig.FrameRate, _response.PublicStreamConfig.SceneDynamic, _response.PublicStreamConfig.AudioCodec);
				}
				_model ??= new Model(_response.Id, _response.Name, archiveStreamConfig, viewStreamConfig, publicStreamConfig);
				break;
			case GetCameraPresetUseCase.Result.CameraPresetNotFound:
				Snackbar.Add("Не удалось получить пресет. Возможно уже удалён.", MudBlazor.Severity.Warning);
				await CancelAsync();
				break;
			case GetCameraPresetUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось получить пресет из-за ошибки валидации.", MudBlazor.Severity.Error);
				await CancelAsync();
				break;
			case GetCameraPresetUseCase.Result.Unknown:
			default:
				throw new Exception($"Unexpected result in {nameof(GetCameraPresetUseCase)}: {_response.Result}");
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			Unsubscribe();

			await SetSubscribingAsync(true);
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraPresetUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), PresetId));
			await SetSubscribingAsync(false);
			switch (_subscriptionResult.Result)
			{
				case SubscribeCameraPresetUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;
				case SubscribeCameraPresetUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
					break;
				case SubscribeCameraPresetUseCase.Result.PresetNotFound:
					Snackbar.Add("Ошибка подписки на события пресета", MudBlazor.Severity.Error);
					break;
				case SubscribeCameraPresetUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeCameraPresetUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception ex)
		{
			await SetSubscribingAsync(false);
			Snackbar.Add("Не удалось получить подписку на события камеры из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}
	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});


	#region [Actions]
	private async Task SubmitAsync()
	{
		try
		{
			await SetLoadingAsync(true);
			if (_model is null)
			{
				Snackbar.Add("Пресет не найден. Невозможно применить изменения!", MudBlazor.Severity.Error);
				return;
			}
			var archiveStreamConfig = new UpdateCameraPresetUseCase.Command.StreamConfig(
				_model.ArchiveStreamConfig.Resolution,
				_model.ArchiveStreamConfig.VideoCodec,
				_model.ArchiveStreamConfig.FrameRate,
				_model.ArchiveStreamConfig.SceneDynamic,
				_model.ArchiveStreamConfig.AudioCodec
			);
			var viewStreamConfig = new UpdateCameraPresetUseCase.Command.StreamConfig(
				_model.ViewStreamConfig.Resolution,
				_model.ViewStreamConfig.VideoCodec,
				_model.ViewStreamConfig.FrameRate,
				_model.ViewStreamConfig.SceneDynamic,
				_model.ViewStreamConfig.AudioCodec
			);
			var publicStreamConfig = new UpdateCameraPresetUseCase.Command.StreamConfig(
				_model.PublicStreamConfig.Resolution,
				_model.PublicStreamConfig.VideoCodec,
				_model.PublicStreamConfig.FrameRate,
				_model.PublicStreamConfig.SceneDynamic,
				_model.PublicStreamConfig.AudioCodec
			);
			var response = await ScopeFactory.MediatorSend(new UpdateCameraPresetUseCase.Command(_model.Id, _model.Name, archiveStreamConfig, viewStreamConfig, publicStreamConfig));
			switch (response.Result)
			{
				case UpdateCameraPresetUseCase.Result.Success:
					Snackbar.Add("Пресет успешно сохранён", MudBlazor.Severity.Success);
					EventSystem.Publish(new PresetSelectEto(_model.Id));
					break;
				case UpdateCameraPresetUseCase.Result.CameraPresetNameAlreadyExists:
					Snackbar.Add("Данное имя пресета уже существует", MudBlazor.Severity.Warning);
					if (_nameFieldRef is not null)
					{
						await _nameFieldRef.SetErrorAsync("Данное имя пресета уже существует", true);
					}
					break;
				case UpdateCameraPresetUseCase.Result.ValidationError:
					Snackbar.Add("Не удалось сохранить изменения из-за ошибки валидации. Проверьте правильность заполнения полей", MudBlazor.Severity.Error);
					break;
				case UpdateCameraPresetUseCase.Result.CameraPresetNotFound:
					Snackbar.Add("Не удалось сохранить изменения, так как камера не найдена. Возможно камера уже удалена.", MudBlazor.Severity.Warning);
					await CancelAsync();
					break;
				case UpdateCameraPresetUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(UpdateCameraPresetUseCase)}: {response.Result}");
			}
		}
		catch (Exception ex)
		{
			Logger.LogError(ex, ex.Message);
			Snackbar.Add("Не удалось сохранить изменения из-за непредвиденной ошибки.");
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	private Task RefreshAsync() => FetchAsync();
	private Task CancelAsync() => Drawer.HideAsync();
	#endregion

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		switch (appEvent)
		{
			case SubscribeCameraPresetUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeCameraPresetUseCase.DeletedEvent deletedEto:
				Snackbar.Add("Просматриваемый вами пресет камеры был удалён", MudBlazor.Severity.Warning);
				Drawer?.HideAsync();
				break;

			default:
				Snackbar.Add("Было получено непредвиденное событие.", MudBlazor.Severity.Warning);
				await FetchAsync();
				await UpdateViewAsync();
				Logger.LogWarning("Unexpected event in {UseCase}: {Event}", nameof(SubscribeCameraPresetUseCase), nameof(appEvent));
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]
}
