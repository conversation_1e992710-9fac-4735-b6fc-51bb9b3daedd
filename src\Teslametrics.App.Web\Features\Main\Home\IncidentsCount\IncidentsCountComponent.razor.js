let incidentsChartInitialized = false;
let customTooltip = null;

const rootStyles = getComputedStyle(document.documentElement);
const paperClr =
	rootStyles.getPropertyValue("--mud-palette-surface").trim() || "#e0e0e0";

const layout = {
	// 1) сам «лист» (вся область SVG / Canvas), подсказки
	paper_bgcolor: paperClr,
	// 2) внутренняя область построения (ось‑ось‑сетка)
	plot_bgcolor: "transparent",
	margin: { t: 20, r: 10, b: 40, l: 40 },
	bargap: 0.6,
	xaxis: {
		type: "date",
		tickformat: "%d.%m", // ось: 21.07
		showline: false,
		ticks: "",
		gridcolor: "rgba(0,0,0,0.1)",
		ticktext: [],
		tickvals: [],
	},
	yaxis: {
		range: [0, 10],
		nticks: 5,
		tickformat: ",d",
		gridcolor: "rgba(0,0,0,0.1)",
	},

	hovermode: "x unified",
	showlegend: false,
};

const chartId = "incidents-chart";

/**
 * Создает кастомную HTML подсказку
 */
function createCustomTooltip() {
	if (customTooltip) {
		return customTooltip;
	}

	const tooltip = document.createElement("div");
	tooltip.className = "incidents-custom-tooltip";

	tooltip.style.cssText = `
		opacity: 0;
		transition: opacity 0.2s ease-in-out;
	`;

	// Создаем структуру подсказки
	const dateElement = document.createElement("div");
	dateElement.className = "tooltip-date";
	const valueElement = document.createElement("div");
	valueElement.className = "tooltip-value";

	// Добавляем цветной индикатор
	const indicator = document.createElement("div");
	indicator.className = "tooltip-indicator";
	indicator.style.cssText = `
		width: 8px;
		height: 8px;
		background-color: #004854;
		border-radius: 50%;
		flex-shrink: 0;
	`;

	const valueText = document.createElement("span");
	valueText.className = "tooltip-value-text";

	valueElement.appendChild(indicator);
	valueElement.appendChild(valueText);
	tooltip.appendChild(dateElement);
	tooltip.appendChild(valueElement);

	document.body.appendChild(tooltip);
	customTooltip = tooltip;
	return tooltip;
}

/**
 */

/**
 * Показывает кастомную подсказку
 * @param {{left:number, top:number}} position  // ISO‑дату Blazor отдаёт строкой
 * @param {{x: string, y: number}} data // x хранит в себе дату события y - количество происшествий
 */
function showCustomTooltip(position, data) {
	const tooltip = createCustomTooltip();
	const dateElement = tooltip.querySelector(".tooltip-date");
	const valueTextElement = tooltip.querySelector(".tooltip-value-text");

	// Форматируем дату
	const date = new Date(data.x);
	const formated = Object.fromEntries(
		new Intl.DateTimeFormat("ru-RU", {
			day: "2-digit",
			month: "long",
			year: "numeric",
			weekday: "long",
		})
			.formatToParts(date)
			.map((p) => [p.type, p.value])
	);

	const formattedDate = `${formated.day} ${formated.month} ${formated.year} ${formated.weekday}`;

	// Заполняем данные
	dateElement.textContent = formattedDate;
	valueTextElement.innerHTML = `Происшествий <b>${data.y}</b>`;

	tooltip.style.left = `${position.left}px`;
	tooltip.style.top = `${position.top}px`;
	tooltip.style.visibility = "visible";
	tooltip.style.opacity = "1";
}

/**
 * Скрывает кастомную подсказку
 */
function hideCustomTooltip() {
	if (customTooltip) {
		customTooltip.style.visibility = "hidden";
		customTooltip.style.opacity = "0";
	}
}

/**
 * Строит/обновляет график из DTO [{count, data}, ...]
 * @param {Array<{count:number, date:string}>} dto  // ISO‑дату Blazor отдаёт строкой
 * @param {string} dateFormat                       // 'dd.MM', 'MMM', ...
 */
export function initIncidentsChart(dto) {
	if (!dto || dto.length === 0) {
		const emptyLayout = {
			paper_bgcolor: "transparent",
			plot_bgcolor: "transparent",
			xaxis: { visible: false },
			yaxis: { visible: false },
			annotations: [
				{
					text: "Нет данных для отображения",
					xref: "paper",
					yref: "paper",
					x: 0.5,
					y: 0.5,
					showarrow: false,
					font: { size: 16, color: "#888" },
				},
			],
			margin: { t: 40, r: 10, b: 40, l: 48 },
		};
		const emptyConfig = { displayModeBar: false };
		draw(chartId, null, emptyLayout, emptyConfig, null);
		return;
	}

	/* ---------- 1. данные ---------- */
	const x = dto.map((d) => d.date); // оставляем «сырые» ISO‑даты
	const y = dto.map((d) => d.count);

	/* ---------- 2. трасса ---------- */
	const trace = {
		type: "bar",
		x,
		y,
		name: "Происшествий",
		marker: { color: "#004854" },
		// отключаем стандартные подсказки
		hoverinfo: "none",
	};

	/* ---------- 3. конфиг ---------- */
	const config = {
		responsive: true,
		displayModeBar: false, // убираем тулбар Plotly
		locale: "ru", // формат dd.MM HH:mm будет русским
	};

	const maxY = Math.ceil(Math.max(...y) * 1.2);

	layout.yaxis.range[0] = 0;
	layout.yaxis.range[1] = maxY > 0 ? maxY : 10;
	layout.xaxis.tickvals = dto.map((d) => d.date);
	layout.xaxis.ticktext = dto.map((d) =>
		new Date(d.date).toLocaleDateString("ru-RU", {
			day: "2-digit",
			month: "2-digit",
		})
	);
	layout.xaxis.type = "category"; // или оставить "date", но тогда шаги будут сложнее контролировать

	draw(chartId, trace, layout, config, dto);
}

/**
 * @param {string} id
 * @param {object} trace
 * @param {object} layout
 * @param {object} config
 * @param {Array} dto - исходные данные для подсказок
 */
function draw(id, trace, layout, config, dto = null) {
	var traces = trace ? [trace] : null;
	if (incidentsChartInitialized) {
		Plotly.react(id, traces, layout, config);
	} else {
		Plotly.newPlot(id, traces, layout, config);
		incidentsChartInitialized = true;
	}

	// Добавляем обработчики событий для кастомных подсказок
	if (dto && dto.length > 0) {
		const plotDiv = document.getElementById(id);

		// Обработчик наведения
		plotDiv.on("plotly_hover", function (eventData) {
			if (eventData.points && eventData.points.length > 0) {
				const point = eventData.points[0];
				const dataIndex = point.pointIndex;

				if (dataIndex >= 0 && dataIndex < dto.length) {
					const data = {
						x: dto[dataIndex].date,
						y: dto[dataIndex].count,
					};
					let left =
						eventData.points[0].bbox.x0 +
						(eventData.points[0].bbox.x1 - eventData.points[0].bbox.x0) / 2;

					let top = eventData.points[0].bbox.y0 + 10;

					showCustomTooltip({ left, top }, data);
				}
			}
		});

		// Обработчик ухода мыши
		plotDiv.on("plotly_unhover", function () {
			hideCustomTooltip();
		});

		// Дополнительный обработчик для скрытия при движении мыши вне графика
		plotDiv.addEventListener("mouseleave", function () {
			hideCustomTooltip();
		});
	}
}

/**
 * Очищает ресурсы компонента
 */
export function cleanupIncidentsChart() {
	// Скрываем и удаляем кастомную подсказку
	if (customTooltip) {
		hideCustomTooltip();
		if (customTooltip.parentNode) {
			customTooltip.parentNode.removeChild(customTooltip);
		}
		customTooltip = null;
	}

	// Сбрасываем флаг инициализации
	incidentsChartInitialized = false;
}
