using System.Reactive;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.UserDevice;

namespace Teslametrics.App.Web.Features.Main.Incidents.IncidentDialog;

public partial class IncidentDialog
{
    // фиксированный пояс UTC+3
    private DateTimeOffset _firedAt => (_sensorModel is GetIncidentUseCase.Response.DoorModel door ? _response?.Incident?.FiredAt - TimeSpan.FromSeconds(30) - TimeSpan.FromSeconds(door.AvailableOpeningTime) : _response?.Incident?.FiredAt - TimeSpan.FromSeconds(30)) ?? DateTimeOffset.MinValue;
    private static readonly TimeSpan Gmt3Offset = TimeSpan.FromHours(3);

    #region [Dialog]
    private DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Medium, NoHeader = true, BackdropClick = false };
    private bool _isVisible => IncidentId.HasValue;

    [Inject]
    private IUserDeviceService _userDeviceService { get; set; } = null!;
    #endregion

    #region [Data]
    private bool _subscribing;

    private IEnumerable<GetIncidentUseCase.Response.DoorModel>? _door => _response?.Fridge?.Sensors.OfType<GetIncidentUseCase.Response.DoorModel>();
    private IEnumerable<GetIncidentUseCase.Response.LeakModel>? _leak => _response?.Fridge?.Sensors.OfType<GetIncidentUseCase.Response.LeakModel>();
    private IEnumerable<GetIncidentUseCase.Response.PowerModel>? _power => _response?.Fridge?.Sensors.OfType<GetIncidentUseCase.Response.PowerModel>();
    private IEnumerable<GetIncidentUseCase.Response.TemperatureModel>? _temperature => _response?.Fridge?.Sensors.OfType<GetIncidentUseCase.Response.TemperatureModel>();
    private IEnumerable<GetIncidentUseCase.Response.HumidityModel>? _humidity => _response?.Fridge?.Sensors.OfType<GetIncidentUseCase.Response.HumidityModel>();

    private GetIncidentUseCase.Response.ISensorModel? _sensorModel => _response?.Fridge?.Sensors.FirstOrDefault(x => x.Id == _response?.Incident?.SensorId);

    private GetIncidentUseCase.Response? _response;
    private SubscribeIncidentUseCase.Response? _subscriptionResult;
    #endregion

    [Parameter]
    public Guid? IncidentId { get; set; }

    [Parameter]
    public EventCallback<Guid?> IncidentIdChanged { get; set; }

    protected override void OnInitialized()
    {
        if (_userDeviceService.IsMobile)
        {
            _dialogOptions = new()
            {
                CloseOnEscapeKey = true,
                FullWidth = true,
                FullScreen = true,
                NoHeader = true
            };
        }

        base.OnInitialized();
    }

    protected override async Task OnParametersSetAsync()
    {
        await FetchDataAsync();
        await SubscribeAsync();
        await MarkIncidentAsReadedAsync();
        await base.OnParametersSetAsync();
    }

    private async Task FetchDataAsync()
    {
        if (IsLoading || !IncidentId.HasValue) return;
        try
        {
            await SetLoadingAsync(true);
            _response = await ScopeFactory.MediatorSend(new GetIncidentUseCase.Query(IncidentId.Value));
        }
        catch (Exception ex)
        {
            _response = null;
            Logger.LogError(ex, "Failed to fetch incident data");
            Snackbar.Add($"Не удалось получить данные происшествия из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
        }

        await SetLoadingAsync(false);
        if (_response is null) return;

        switch (_response.Result)
        {
            case GetIncidentUseCase.Result.Success:
                break;
            case GetIncidentUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(IncidentDialog), nameof(GetIncidentUseCase));
                Snackbar.Add($"Не удалось получить данные происшествия из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(IncidentDialog), nameof(GetIncidentUseCase), _response.Result);
                Snackbar.Add($"Не удалось получить данные происшествия из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task SubscribeAsync()
    {
        if (IncidentId is null) return;
        Unsubscribe();
        await SetSubscribingAsync(true);
        try
        {
            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeIncidentUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), IncidentId.Value));
        }
        catch (Exception ex)
        {
            _subscriptionResult = null;
            Snackbar.Add($"Не удалось подписаться на события происшествия из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetSubscribingAsync(false);

        if (_subscriptionResult is null) return;

        switch (_subscriptionResult.Result)
        {
            case SubscribeIncidentUseCase.Result.Success:
                CompositeDisposable.Add(_subscriptionResult.Subscription!);
                break;
            case SubscribeIncidentUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
                break;
            case SubscribeIncidentUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(IncidentDialog), nameof(SubscribeIncidentUseCase));
                Snackbar.Add($"Не удалось подписаться на события происшествия из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(IncidentDialog), nameof(SubscribeIncidentUseCase), _subscriptionResult.Result);
                Snackbar.Add($"Не удалось подписаться на события происшествия из-за ошибки: {_subscriptionResult.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }
    private Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [Sensor Value Processors]
    private string TempValueProcessor(object? value) => value switch
    {
        null => "Нет связи с датчиком",
        double doubleValue => Math.Round(doubleValue, 2) + "°C",
        _ => value?.ToString() ?? "-",
    };

    private string HumidityValueProcessor(object? value) => value switch
    {
        null => "Нет связи с датчиком",
        _ => (value ?? 0) + "%",
    };

    private string DoorValueProcessor(object? value) => value switch
    {
        null => "Нет связи с датчиком",
        bool boolValue => boolValue ? "Открыта" : "Закрыта",
        _ => value?.ToString() ?? "-",
    };

    private string PowerValueProcessor(object? value) => value switch
    {
        null => "Нет связи с датчиком",
        bool boolValue => boolValue ? "В норме" : "Отсутствует",
        _ => value?.ToString() ?? "-",
    };

    private string LeakValueProcessor(object value) => value switch
    {
        null => "Нет связи с датчиком",
        bool boolValue => boolValue ? "Обнаружена" : "Не обнаружена",
        _ => value?.ToString() ?? "-",
    };
    #endregion

    #region [Sensor Error Funcs]
    private static bool DoorErrorFunc(object? value) => value is bool boolValue && boolValue;

    private static bool PowerErrorFunc(object? value) => value is bool boolValue && !boolValue;

    private static bool LeakErrorFunc(object? value) => value is bool boolValue && boolValue;

    private static bool HumidityErrorFunc(GetIncidentUseCase.Response.HumidityModel humidity, object? value) => value is double doubleValue && (doubleValue < humidity.MinHumidity || doubleValue > humidity.MaxHumidity);

    private static bool TemperatureErrorFunc(GetIncidentUseCase.Response.TemperatureModel temperature, object? value) => value is double doubleValue && (doubleValue < temperature.MinTemp || doubleValue > temperature.MaxTemp);
    #endregion

    #region [Actions]
    private async Task CancelAsync()
    {
        Unsubscribe();

        _response = null;
        IncidentId = null;
        if (IncidentIdChanged.HasDelegate)
            await IncidentIdChanged.InvokeAsync(IncidentId);
    }

    private async Task MarkIncidentAsReadedAsync()
    {
        if (IncidentId is null) return;

        Guid userId;
        try
        {
            userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get current user id");
            Snackbar.Add($"Не удалось получить данные происшествия из-за ошибки авторизации.", MudBlazor.Severity.Error);
            await CancelAsync();
            return;
        }


        MarkIncidentAsReadedUseCase.Response? response;
        try
        {
            response = await ScopeFactory.MediatorSend(new MarkIncidentAsReadedUseCase.Command(IncidentId.Value, userId));
        }
        catch (Exception ex)
        {
            response = null;
            Logger.LogError(ex, "Failed to mark incident as read");
            Snackbar.Add($"Не удалось получить данные происшествия из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case MarkIncidentAsReadedUseCase.Result.Success:
                break;
            case MarkIncidentAsReadedUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(IncidentDialog), nameof(MarkIncidentAsReadedUseCase));
                Snackbar.Add($"Не удалось получить данные происшествия из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(IncidentDialog), nameof(MarkIncidentAsReadedUseCase), response.Result);
                Snackbar.Add($"Не удалось получить данные происшествия из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
    #endregion

    #region [Events]
    private async void OnAppEventHandler(object appEvent)
    {
        switch (appEvent)
        {
            case SubscribeIncidentUseCase.UpdatedEvent updatedEto:
                await FetchDataAsync();
                await UpdateViewAsync();
                break;

            default:
                Snackbar.Add("Было получено непредвиденное событие.", MudBlazor.Severity.Warning);
                Logger.LogWarning("Unexpected event in {UseCase}: {Event}", nameof(SubscribeIncidentUseCase), nameof(appEvent));
                break;
        }
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
        Logger.LogError(exc, exc.Message);
    }
    #endregion

    private static DateTimeOffset FormatGmt3(DateTimeOffset dto)
    {
        return dto.ToOffset(Gmt3Offset);
    }
}
