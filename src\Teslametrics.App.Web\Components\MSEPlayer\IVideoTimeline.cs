namespace Teslametrics.App.Web.Components.MSEPlayer;

/// <summary>
/// Интерфейс для компонента временной шкалы видеоплеера.
/// Предоставляет функциональность отображения текущего времени воспроизведения
/// в пределах 3-часового окна для архивных записей.
/// </summary>
public interface IVideoTimeline
{
    /// <summary>
    /// Текущее абсолютное время воспроизведения видео.
    /// </summary>
    DateTime? CurrentTime { get; }

    /// <summary>
    /// Начало временного окна (3 часа).
    /// </summary>
    DateTime? WindowStart { get; }

    /// <summary>
    /// Конец временного окна (3 часа).
    /// </summary>
    DateTime? WindowEnd { get; }

    /// <summary>
    /// Позиция текущего времени в процентах от начала окна (0-100).
    /// </summary>
    double CurrentPositionPercent { get; }

    /// <summary>
    /// Событие, срабатывающее при изменении текущего времени воспроизведения.
    /// </summary>
    event EventHandler<DateTime?>? CurrentTimeChanged;

    /// <summary>
    /// Обновляет текущее время воспроизведения.
    /// </summary>
    /// <param name="currentTime">Новое текущее время</param>
    Task UpdateCurrentTimeAsync(DateTime? currentTime);

    /// <summary>
    /// Устанавливает временное окно для отображения.
    /// </summary>
    /// <param name="start">Начало окна</param>
    /// <param name="end">Конец окна</param>
    Task SetTimeWindowAsync(DateTime start, DateTime end);
}
