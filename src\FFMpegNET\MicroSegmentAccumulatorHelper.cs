using System.Threading.Channels;

namespace FFMpegNET;

internal static class MicroSegmentAccumulatorHelper
{
    public static async Task ProcessOutputStream(ChannelReader<(MemoryOutputBuffer Buffer, DateTimeOffset StartTime, double Duration)> channel,
                                                 Func<Segment, Task> onNextSegment,
                                                 CancellationToken cancellationToken)
    {
        bool readAvailable;
        do
        {
            readAvailable = await channel.WaitToReadAsync(cancellationToken);
            if (readAvailable && channel.TryRead(out var seg))
            {
                try
                {
                    // Создаем копию потока для передачи в сегмент
                    var segmentStream = new MemoryStream();
                    seg.Buffer.Stream.Position = 0;
                    await seg.Buffer.Stream.CopyToAsync(segmentStream, cancellationToken);
                    segmentStream.Position = 0;

                    await onNextSegment(new Segment(segmentStream, seg.StartTime, seg.Duration));
                }
                finally
                {
                    seg.Buffer.Dispose();
                }

            }
        }
        while (!cancellationToken.IsCancellationRequested && readAvailable);
    }

    public static async Task ProcessInputMicroSegments(
        ChannelReader<MicroSegment> inputChannel,
        ChannelWriter<(MemoryOutputBuffer, DateTimeOffset, double)> outputChannel,
        Action createOutputContext,
        Action<MicroSegment> processSingleMicroSegment,
        Action closeCurrentSegment,
        Action cleanupOutputContext,
        CancellationToken cancellationToken)
    {
        try
        {
            createOutputContext();
            await ProcessMicroSegmentFrames(inputChannel, processSingleMicroSegment, cancellationToken);
            closeCurrentSegment();
            outputChannel.Complete();
        }
        finally
        {
            cleanupOutputContext();
        }
    }

    private static async Task ProcessMicroSegmentFrames(
        ChannelReader<MicroSegment> inputChannel,
        Action<MicroSegment> processSingleMicroSegment,
        CancellationToken cancellationToken)
    {
        bool readAvailable;
        do
        {
            readAvailable = await inputChannel.WaitToReadAsync(cancellationToken);
            if (readAvailable && inputChannel.TryRead(out var microSegment))
            {
                processSingleMicroSegment(microSegment);
            }
        }
        while (!cancellationToken.IsCancellationRequested && readAvailable);
    }
}
