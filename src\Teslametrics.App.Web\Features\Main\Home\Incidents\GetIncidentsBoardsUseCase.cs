using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Home.Incidents;

public static class GetIncidentsBoardsUseCase
{
    public record Query(IEnumerable<Guid> Buildings) : BaseRequest<Response>; // Пользователь передаёт список зданий, за которыми он смотрит

    public record Response : BaseResponse
    {
        public Fridge? FridgeIncidents { get; init; } // Сколько происшествий
        public Camera? CameraIncidents { get; init; } // Состояние камер. Сколько работает/сколько всего
        public Incident? TotalIncidents { get; init; } // Происшествия за 24 часа

        public IEnumerable<Guid> CameraIds { get; init; } // Камеры, которые прикреплены к Buildings

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Fridge fridge, Camera camera, Incident incident, List<Guid> cameraIds)
        {
            Result = Result.Success;

            FridgeIncidents = fridge;
            CameraIncidents = camera;
            TotalIncidents = incident;
            CameraIds = cameraIds;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;
            FridgeIncidents = null;
            CameraIncidents = null;
            TotalIncidents = null;
            CameraIds = [];
        }

        public record Incident(int Current, int Total); // Активные инциденты, всего за 24 часа
        public record Fridge(int Incidents, int Total); // Сколько холодильников бьют тревогу, сколько всего холодильников
        public record Camera(int Incidents, int Total); // Сколько камер НЕ на связи, сколько всего камер
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        PlanNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            //RuleFor(q => q.Buildings).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;
        private readonly IGrainFactory _grainFactory;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection,
                       IGrainFactory grainFactory)
        {
            _validator = validator;
            _dbConnection = dbConnection;
            _grainFactory = grainFactory;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            if (!await CheckTableExistsAsync())
            {
                return new Response(Result.PlanNotFound);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Plans.Props.Page)
                .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

            var pageJson = await _dbConnection.ExecuteScalarAsync<string?>(template.RawSql);

            if (string.IsNullOrEmpty(pageJson))
            {
                return new Response(Result.PlanNotFound);
            }

            var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

            var requestBuildingsList = request.Buildings.ToList();
            var buildings = page.Cities.SelectMany(c => c.Buildings).Where(b => requestBuildingsList.Count == 0 || requestBuildingsList.Contains(b.Id)).ToList();

            var buildingIds = buildings.Select(b => b.Id).ToList();

            template = SqlQueryBuilder.Create()
                .Select(Db.Incidents.Props.DeviceId)
                .Select(Db.Incidents.Props.ResolvedAt)
                .Where($"({Db.Incidents.Props.BuildingId} = ANY(:BuildingIds) OR {Db.Incidents.Props.IncidentType} = :IncidentType)", new { BuildingIds = buildingIds, IncidentType = IncidentType.WirenboardDisconnected.ToString() })
                .Where($"({Db.Incidents.Props.ResolvedAt} IS NULL OR {Db.Incidents.Props.CreatedAt} > :StartAt)", new { StartAt = DateTime.UtcNow.AddDays(-1) })
                .Build(QueryType.Standard, Db.Incidents.Table, RowSelection.AllRows);

            var incidents = await _dbConnection.QueryAsync<IncidentModel>(template.RawSql, template.Parameters);

            var cameras = buildings.SelectMany(b => b.Floors.SelectMany(f => f.Rooms).SelectMany(r => r.Cameras))
                .Select(c => c.Id)
                .ToList();

            var cameraIncindets = 0;

            var mediaServerGrain = _grainFactory.GetGrain<IMediaServerGrain>(Guid.Empty);
            var statusResponse = await mediaServerGrain.GetStatusesAsync(new IMediaServerGrain.GetCameraStatusesRequest(cameras));

            foreach (var cameraId in cameras)
            {
                var status = statusResponse.Statuses[cameraId];

                if (status == CameraStatus.Problem || status == CameraStatus.Stopped)
                {
                    cameraIncindets++;
                }
            }

            var fridges = buildings.SelectMany(b => b.Floors.SelectMany(f => f.Rooms).Select(r => r.Fridges).SelectMany(f => f)).ToList();

            var fridgeIncidents = 0;

            foreach (var fridge in fridges)
            {
                if (incidents.Any(i => i.DeviceId == fridge.Id && i.ResolvedAt is null))
                {
                    fridgeIncidents++;
                }
            }

            return new Response(new Response.Fridge(fridgeIncidents, fridges.Count),
                                new Response.Camera(cameraIncindets, cameras.Count),
                                new Response.Incident(incidents.Count(i => i.ResolvedAt is null), incidents.Count()),
                                cameras);
        }

        private async Task<bool> CheckTableExistsAsync()
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = Db.Plans.Table });

            return tableExists > 0;
        }
    }

    public record IncidentModel(Guid DeviceId, DateTimeOffset? ResolvedAt);
}