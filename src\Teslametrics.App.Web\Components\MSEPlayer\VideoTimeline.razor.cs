using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Globalization;

namespace Teslametrics.App.Web.Components.MSEPlayer;

public partial class VideoTimeline : IVideoTimeline, IAsyncDisposable
{
    private DateTime? _currentTime;
    private DateTime? _windowStart;
    private DateTime? _windowEnd;
    private Timer? _updateTimer;
    private bool _disposed = false;
    private DateTime _lastUpdateTime = DateTime.UtcNow;
    private readonly object _timeLock = new object();
    private DotNetObjectReference<VideoTimeline>? _dotNetRef;
    private IJSObjectReference? _jsModule;

    #region [Parameters]

    /// <summary>
    /// Ссылка на MSE плеер для получения текущего времени воспроизведения.
    /// </summary>
    [Parameter]
    [EditorRequired]
    public IMsePlayer Player { get; set; } = null!;

    /// <summary>
    /// Интервал обновления временной шкалы в миллисекундах (по умолчанию 250мс).
    /// </summary>
    [Parameter]
    public int UpdateInterval { get; set; } = 250;

    /// <summary>
    /// Размер временного окна в часах (по умолчанию 3 часа).
    /// </summary>
    [Parameter]
    public int WindowSizeHours { get; set; } = 3;

    #endregion

    #region [Injected Services]

    [Inject]
    private IJSRuntime JSRuntime { get; set; } = null!;

    #endregion

    #region [IVideoTimeline Implementation]

    public DateTime? CurrentTime => _currentTime;
    public DateTime? WindowStart => _windowStart;
    public DateTime? WindowEnd => _windowEnd;

    public double CurrentPositionPercent
    {
        get
        {
            if (!_currentTime.HasValue || !_windowStart.HasValue || !_windowEnd.HasValue)
                return 0;

            var totalDuration = _windowEnd.Value - _windowStart.Value;
            var currentPosition = _currentTime.Value - _windowStart.Value;

            if (totalDuration.TotalMilliseconds <= 0)
                return 0;

            var percent = currentPosition.TotalMilliseconds / totalDuration.TotalMilliseconds * 100;
            return Math.Max(0, Math.Min(100, percent));
        }
    }

    public event EventHandler<DateTime?>? CurrentTimeChanged;

    public async Task UpdateCurrentTimeAsync(DateTime? currentTime)
    {
        var previousTime = _currentTime;

        if (_currentTime != currentTime)
        {
            // Проверяем на разумные изменения времени (не больше 5 секунд за раз)
            if (previousTime.HasValue && currentTime.HasValue)
            {
                var timeDelta = Math.Abs((currentTime.Value - previousTime.Value).TotalSeconds);
                if (timeDelta > 5 && timeDelta < 3600) // Больше 5 сек, но меньше часа
                {
                    Logger.LogDebug("Большой скачок времени: {PrevTime} -> {CurrentTime}, дельта: {Delta} сек",
                        previousTime.Value, currentTime.Value, timeDelta);
                }
            }

            _currentTime = currentTime;
            CurrentTimeChanged?.Invoke(this, currentTime);

            // Автоматически обновляем окно, если текущее время выходит за его пределы
            if (currentTime.HasValue && ShouldUpdateWindow(currentTime.Value))
            {
                await SetTimeWindowFromCurrentTimeAsync(currentTime.Value);
            }

            await InvokeAsync(StateHasChanged);
        }
    }

    public async Task SetTimeWindowAsync(DateTime start, DateTime end)
    {
        _windowStart = start;
        _windowEnd = end;
        await InvokeAsync(StateHasChanged);
    }

    #endregion

    #region [Lifecycle Methods]

    protected override void OnInitialized()
    {
        base.OnInitialized();

        // Подписываемся на изменения статуса плеера
        Player.StreamStatusChanged += OnPlayerStatusChanged;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            // Создаем ссылку на этот компонент для JavaScript
            _dotNetRef = DotNetObjectReference.Create(this);

            // Инициализируем JavaScript модуль
            await InitializeJavaScriptModuleAsync();

            // Запускаем таймер обновления времени
            StartUpdateTimer();

            // Получаем начальное время
            await UpdateCurrentTimeFromPlayerAsync();
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (_disposed) return;
        _disposed = true;

        // Останавливаем таймер
        _updateTimer?.Dispose();

        // Освобождаем JavaScript модуль
        try
        {
            if (_jsModule != null)
            {
                await _jsModule.DisposeAsync();
            }
        }
        catch (JSDisconnectedException)
        {
            // Если соединение с JS уже разорвано, ничего делать не нужно
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при освобождении JavaScript модуля");
        }

        // Освобождаем ссылку на компонент
        _dotNetRef?.Dispose();

        // Отписываемся от событий
        Player.StreamStatusChanged -= OnPlayerStatusChanged;

        GC.SuppressFinalize(this);
    }

    #endregion

    #region [Private Methods]

    /// <summary>
    /// Инициализирует JavaScript модуль для работы с плагином TimelineTracker
    /// </summary>
    private async Task InitializeJavaScriptModuleAsync()
    {
        try
        {
            // Загружаем JavaScript модуль
            _jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import",
                "./Components/MSEPlayer/VideoTimeline.razor.js");

            // Инициализируем плагин TimelineTracker для данного плеера
            await _jsModule.InvokeVoidAsync("initializeTimelineTracker",
                Player.CameraId.ToString(), _dotNetRef, new
                {
                    updateInterval = UpdateInterval,
                    highFrequency = false,
                    adaptive = true
                });

            Logger.LogDebug("TimelineTracker плагин инициализирован для камеры {CameraId}", Player.CameraId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при инициализации TimelineTracker плагина");
        }
    }

    /// <summary>
    /// Запускает таймер для периодического обновления времени
    /// </summary>
    private void StartUpdateTimer()
    {
        // Запускаем таймер с интервалом UpdateInterval
        _updateTimer = new Timer(OnTimerCallback, null,
            TimeSpan.Zero, TimeSpan.FromMilliseconds(UpdateInterval));
    }

    /// <summary>
    /// Обработчик таймера для обновления времени
    /// </summary>
    private async void OnTimerCallback(object? state)
    {
        if (_disposed) return;

        try
        {
            await UpdateCurrentTimeFromPlayerAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при обновлении времени по таймеру");
        }
    }

    private async Task UpdateCurrentTimeFromPlayerAsync()
    {
        if (_disposed) return;

        try
        {
            var currentTimeString = await Player.GetCurrentAbsoluteTimeAsync();

            if (!string.IsNullOrEmpty(currentTimeString))
            {
                DateTime currentTime;

                // Используем более точный парсинг с учетом UTC и культуры
                if (DateTime.TryParseExact(currentTimeString, "yyyy-MM-ddTHH:mm:ss.fffZ",
                    CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal | DateTimeStyles.AdjustToUniversal, out currentTime) ||
                    DateTime.TryParseExact(currentTimeString, "yyyy-MM-ddTHH:mm:ssZ",
                    CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal | DateTimeStyles.AdjustToUniversal, out currentTime) ||
                    DateTime.TryParse(currentTimeString, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal | DateTimeStyles.AdjustToUniversal, out currentTime))
                {
                    // Проверяем на разумность времени (не должно сильно отличаться от системного времени)
                    var systemTime = DateTime.UtcNow;
                    var timeDiff = Math.Abs((currentTime - systemTime).TotalHours);

                    // Если разница больше 24 часов, возможно проблема с часовыми поясами
                    if (timeDiff > 24)
                    {
                        Logger.LogWarning("Большая разница между временем плеера ({PlayerTime}) и системным временем ({SystemTime}). Разница: {Diff} часов",
                            currentTime, systemTime, timeDiff);
                    }

                    await UpdateCurrentTimeAsync(currentTime);
                }
                else
                {
                    Logger.LogWarning("Не удалось распарсить время от плеера: {TimeString}", currentTimeString);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при получении текущего времени от плеера: {Message}", ex.Message);
        }
    }

    private void OnPlayerStatusChanged(object? sender, IMsePlayer.StreamStatus status)
    {
        // При изменении статуса плеера можем обновить отображение
        InvokeAsync(StateHasChanged);
    }

    private bool ShouldUpdateWindow(DateTime currentTime)
    {
        if (!_windowStart.HasValue || !_windowEnd.HasValue)
            return true;

        // Обновляем окно, если текущее время выходит за пределы текущего окна
        return currentTime < _windowStart.Value || currentTime > _windowEnd.Value;
    }

    private async Task SetTimeWindowFromCurrentTimeAsync(DateTime currentTime)
    {
        // Устанавливаем окно так, чтобы текущее время было в центре
        var halfWindow = TimeSpan.FromHours(WindowSizeHours / 2.0);
        var start = currentTime - halfWindow;
        var end = currentTime + halfWindow;

        await SetTimeWindowAsync(start, end);
    }

    /// <summary>
    /// Обработчик обновлений времени от JavaScript плагина TimelineTracker
    /// </summary>
    [JSInvokable]
    public async Task OnTimelineUpdate(string timeDataJson)
    {
        if (_disposed) return;

        try
        {
            var timeData = System.Text.Json.JsonSerializer.Deserialize<TimelineUpdateData>(timeDataJson);

            if (timeData != null && !string.IsNullOrEmpty(timeData.AbsoluteTime))
            {
                if (DateTime.TryParseExact(timeData.AbsoluteTime, "yyyy-MM-ddTHH:mm:ss.fffZ",
                    CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal | DateTimeStyles.AdjustToUniversal, out var currentTime))
                {
                    await UpdateCurrentTimeAsync(currentTime);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при обработке обновления времени от TimelineTracker: {TimeData}", timeDataJson);
        }
    }

    /// <summary>
    /// Диагностический метод для проверки точности синхронизации времени
    /// </summary>
    public async Task<TimelineDiagnostics> GetDiagnosticsAsync()
    {
        try
        {
            var playerTimeString = await Player.GetCurrentAbsoluteTimeAsync();
            var systemTime = DateTime.UtcNow;

            DateTime? playerTime = null;
            if (!string.IsNullOrEmpty(playerTimeString))
            {
                DateTime.TryParseExact(playerTimeString, "yyyy-MM-ddTHH:mm:ss.fffZ",
                    CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal | DateTimeStyles.AdjustToUniversal, out var parsed);
                playerTime = parsed;
            }

            return new TimelineDiagnostics
            {
                SystemTime = systemTime,
                PlayerTime = playerTime,
                DisplayedTime = _currentTime,
                TimeDriftSeconds = playerTime.HasValue ? (playerTime.Value - systemTime).TotalSeconds : null,
                LastUpdateTime = _lastUpdateTime,
                UpdateInterval = UpdateInterval
            };
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при получении диагностики временной шкалы");
            return new TimelineDiagnostics { SystemTime = DateTime.UtcNow };
        }
    }

    #endregion
}

/// <summary>
/// Данные обновления времени от JavaScript
/// </summary>
public class TimelineUpdateData
{
    public string AbsoluteTime { get; set; } = string.Empty;
    public double MediaTime { get; set; }
    public double Timestamp { get; set; }
    public double Latency { get; set; }
    public double BufferStart { get; set; }
    public double BufferEnd { get; set; }
}

/// <summary>
/// Диагностическая информация о состоянии временной шкалы
/// </summary>
public class TimelineDiagnostics
{
    public DateTime SystemTime { get; set; }
    public DateTime? PlayerTime { get; set; }
    public DateTime? DisplayedTime { get; set; }
    public double? TimeDriftSeconds { get; set; }
    public DateTime LastUpdateTime { get; set; }
    public int UpdateInterval { get; set; }
}
