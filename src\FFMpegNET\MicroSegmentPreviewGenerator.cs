using System.Runtime.InteropServices;
using FFmpeg.AutoGen;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace FFMpegNET;

/// <summary>
/// Класс для генерации превью из микросегментов по первому кадру (который всегда I-кадр).
/// Используется в CameraStreamPreviewGrain для создания превью.
/// </summary>
public unsafe sealed class MicroSegmentPreviewGenerator : IDisposable
{
    public record Options
    {
        /// <summary>
        /// Интервал между генерацией превью в количестве микросегментов
        /// </summary>
        public int PreviewSegmentInterval { get; set; } = 10;

        /// <summary>
        /// Ширина превью (0 - использовать оригинальную ширину)
        /// </summary>
        public int PreviewWidth { get; set; } = 320;

        /// <summary>
        /// Высота превью (0 - использовать оригинальную высоту)
        /// </summary>
        public int PreviewHeight { get; set; } = 240;

        /// <summary>
        /// Качество JPEG для превью (1-100)
        /// </summary>
        public int PreviewQuality { get; set; } = 100;

        public static Options Default => new Options();
    }

    #region Настройки и зависимости
    private readonly ILogger<MicroSegmentPreviewGenerator> _logger;
    private readonly Options _options;
    #endregion

    #region Декодирование для превью
    // Все контексты создаются локально для каждого микросегмента
    #endregion

    #region Счетчики
    private int _segmentCounter;
    #endregion

    /// <summary>
    /// Инициализирует новый экземпляр генератора превью микросегментов
    /// </summary>
    public MicroSegmentPreviewGenerator(ILogger<MicroSegmentPreviewGenerator> logger, IOptions<Options>? options = null)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? Options.Default;
    }

    private void ResetFields()
    {
        _segmentCounter = 0;
    }

    /// <summary>
    /// Генерирует превью из микросегмента, если достигнут интервал
    /// </summary>
    public byte[]? GeneratePreview(MicroSegment microSegment)
    {
        if (microSegment.Payload == null)
        {
            _logger.LogWarning("Received micro segment with null payload");
            return null;
        }

        // Проверяем, нужно ли генерировать превью
        if (_segmentCounter == 0 || _segmentCounter >= _options.PreviewSegmentInterval)
        {
            try
            {
                var preview = GeneratePreviewFromMicroSegment(microSegment);
                _segmentCounter = 1; // Сбрасываем счетчик на 1 после создания превью
                return preview;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating preview from micro segment");
                _segmentCounter++; // Увеличиваем счетчик даже при ошибке
                return null;
            }
        }
        else
        {
            // Увеличиваем счетчик только если не создавали превью
            _segmentCounter++;
            return null;
        }
    }

    /// <summary>
    /// Генерирует превью из микросегмента
    /// </summary>
    private byte[]? GeneratePreviewFromMicroSegment(MicroSegment microSegment)
    {
        if (microSegment.Payload == null)
            return null;

        // Создаем временный файл для декодирования микросегмента
        var tempFile = Path.GetTempFileName();
        try
        {
            File.WriteAllBytes(tempFile, microSegment.Payload);

            // Для каждого микросегмента создаем полный контекст декодирования
            return ExtractFirstFrameWithFullContext(tempFile);
        }
        finally
        {
            if (File.Exists(tempFile))
            {
                File.Delete(tempFile);
            }
        }
    }

    ~MicroSegmentPreviewGenerator()
    {
        Dispose(false);
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    private void Dispose(bool disposing)
    {
        // Все ресурсы освобождаются локально в методах
    }

    /// <summary>
    /// Устанавливает значение в словаре с проверкой результата
    /// </summary>
    private static void SetDictionaryValue(AVDictionary** dict, string key, string value)
    {
        int result = ffmpeg.av_dict_set(dict, key, value, 0);
        if (result < 0)
        {
            throw new ApplicationException($"Failed to set {key}: {result}");
        }
    }

    /// <summary>
    /// Извлекает первый кадр из файла с созданием полного контекста декодирования
    /// </summary>
    private byte[]? ExtractFirstFrameWithFullContext(string filePath)
    {
        AVFormatContext* formatContext = null;
        AVCodecContext* decoderContext = null;
        AVCodecContext* encoderContext = null;
        SwsContext* swsContext = null;

        try
        {
            // Открываем файл
            formatContext = ffmpeg.avformat_alloc_context();
            if (formatContext == null)
            {
                throw new ApplicationException("Failed to allocate format context");
            }

            if (ffmpeg.avformat_open_input(&formatContext, filePath, null, null) < 0)
            {
                throw new ApplicationException($"Failed to open file: {filePath}");
            }

            if (ffmpeg.avformat_find_stream_info(formatContext, null) < 0)
            {
                throw new ApplicationException("Failed to retrieve stream information");
            }

            // Находим видеопоток
            int videoStreamIndex = -1;
            for (int i = 0; i < formatContext->nb_streams; i++)
            {
                if (formatContext->streams[i]->codecpar->codec_type == AVMediaType.AVMEDIA_TYPE_VIDEO)
                {
                    videoStreamIndex = i;
                    break;
                }
            }

            if (videoStreamIndex == -1)
            {
                throw new ApplicationException("Video stream not found");
            }

            var videoStream = formatContext->streams[videoStreamIndex];
            var decoder = ffmpeg.avcodec_find_decoder(videoStream->codecpar->codec_id);

            if (decoder == null)
            {
                throw new ApplicationException("Failed to find decoder for video stream");
            }

            // Создаем декодер
            decoderContext = ffmpeg.avcodec_alloc_context3(decoder);
            if (decoderContext == null)
            {
                throw new ApplicationException("Failed to allocate memory for decoder context");
            }

            if (ffmpeg.avcodec_parameters_to_context(decoderContext, videoStream->codecpar) < 0)
            {
                throw new ApplicationException("Failed to copy codec parameters");
            }

            if (ffmpeg.avcodec_open2(decoderContext, decoder, null) < 0)
            {
                throw new ApplicationException("Failed to open decoder");
            }

            // Определяем размеры превью
            int targetWidth = _options.PreviewWidth > 0 ? _options.PreviewWidth : decoderContext->width;
            int targetHeight = _options.PreviewHeight > 0 ? _options.PreviewHeight : decoderContext->height;

            // Создаем JPEG энкодер
            encoderContext = CreateJpegEncoder(targetWidth, targetHeight);

            // Контекст масштабирования создадим динамически при обработке первого кадра
            // так как размеры декодера могут быть неизвестны до декодирования первого кадра

            // Декодируем первый кадр
            return DecodeFirstFrame(formatContext, videoStreamIndex, decoderContext, encoderContext, targetWidth, targetHeight);
        }
        finally
        {
            // Освобождаем ресурсы
            if (swsContext != null)
            {
                ffmpeg.sws_freeContext(swsContext);
            }

            if (decoderContext != null)
            {
                ffmpeg.avcodec_free_context(&decoderContext);
            }

            if (encoderContext != null)
            {
                ffmpeg.avcodec_free_context(&encoderContext);
            }

            if (formatContext != null)
            {
                ffmpeg.avformat_close_input(&formatContext);
            }
        }
    }

    /// <summary>
    /// Создает JPEG энкодер
    /// </summary>
    private AVCodecContext* CreateJpegEncoder(int width, int height)
    {
        var encoder = ffmpeg.avcodec_find_encoder(AVCodecID.AV_CODEC_ID_MJPEG);
        if (encoder == null)
        {
            throw new ApplicationException("Failed to find JPEG encoder");
        }

        var encoderContext = ffmpeg.avcodec_alloc_context3(encoder);
        if (encoderContext == null)
        {
            throw new ApplicationException("Failed to allocate memory for encoder context");
        }

        encoderContext->width = width;
        encoderContext->height = height;
        encoderContext->time_base = new AVRational { num = 1, den = 25 };
        encoderContext->pix_fmt = AVPixelFormat.AV_PIX_FMT_YUVJ420P;
        encoderContext->codec_type = AVMediaType.AVMEDIA_TYPE_VIDEO;
        encoderContext->color_range = AVColorRange.AVCOL_RANGE_JPEG;
        encoderContext->strict_std_compliance = ffmpeg.FF_COMPLIANCE_NORMAL;
        encoderContext->flags = ffmpeg.AV_CODEC_FLAG_QSCALE;
        encoderContext->global_quality = 10 * ffmpeg.FF_QP2LAMBDA;

        AVDictionary* dict = null;
        try
        {
            SetDictionaryValue(&dict, "quality", _options.PreviewQuality.ToString());

            if (ffmpeg.avcodec_open2(encoderContext, encoder, &dict) < 0)
            {
                ffmpeg.avcodec_free_context(&encoderContext);
                throw new ApplicationException("Failed to open JPEG encoder");
            }
        }
        finally
        {
            if (dict != null)
            {
                ffmpeg.av_dict_free(&dict);
            }
        }

        return encoderContext;
    }

    /// <summary>
    /// Создает контекст масштабирования
    /// </summary>
    private SwsContext* CreateScalingContext(AVCodecContext* decoderContext, int targetWidth, int targetHeight)
    {
        var srcFormat = decoderContext->pix_fmt;
        var dstFormat = AVPixelFormat.AV_PIX_FMT_YUVJ420P; // Используем YUVJ420P для JPEG энкодера

        // Если исходный формат неизвестен, используем fallback
        if (srcFormat == AVPixelFormat.AV_PIX_FMT_NONE)
        {
            _logger.LogWarning("Source pixel format is AV_PIX_FMT_NONE, using AV_PIX_FMT_YUV420P as fallback");
            srcFormat = AVPixelFormat.AV_PIX_FMT_YUV420P;
        }

        // Если размеры одинаковые и форматы одинаковые, scaling context не нужен
        if (decoderContext->width == targetWidth &&
            decoderContext->height == targetHeight &&
            srcFormat == dstFormat)
        {
            _logger.LogInformation("No scaling needed. Source and target dimensions and formats are the same: {Width}x{Height}, format: {Format}",
                decoderContext->width, decoderContext->height, srcFormat);
            return null; // Scaling не нужен
        }

        var swsContext = ffmpeg.sws_getContext(
            decoderContext->width, decoderContext->height, srcFormat,
            targetWidth, targetHeight, dstFormat,
            ffmpeg.SWS_BILINEAR | ffmpeg.SWS_ACCURATE_RND, null, null, null);

        if (swsContext == null)
        {
            throw new ApplicationException($"Failed to create scaling context. Source format: {srcFormat}, Target format: {dstFormat}, Dimensions: {decoderContext->width}x{decoderContext->height} -> {targetWidth}x{targetHeight}");
        }

        _logger.LogInformation("Successfully created scaling context. Source format: {SourceFormat}, Target format: {TargetFormat}, Dimensions: {Width}x{Height} -> {TargetWidth}x{TargetHeight}",
            srcFormat, dstFormat, decoderContext->width, decoderContext->height, targetWidth, targetHeight);

        return swsContext;
    }

    /// <summary>
    /// Декодирует первый кадр и создает JPEG превью
    /// </summary>
    private byte[]? DecodeFirstFrame(AVFormatContext* formatContext, int videoStreamIndex,
        AVCodecContext* decoderContext, AVCodecContext* encoderContext, int targetWidth, int targetHeight)
    {
        var packet = ffmpeg.av_packet_alloc();
        var frame = ffmpeg.av_frame_alloc();
        var scaledFrame = ffmpeg.av_frame_alloc();
        var jpegPacket = ffmpeg.av_packet_alloc();
        SwsContext* swsContext = null;

        try
        {
            // Ищем первый видеопакет
            while (ffmpeg.av_read_frame(formatContext, packet) >= 0)
            {
                if (packet->stream_index == videoStreamIndex)
                {
                    // Декодируем пакет
                    int ret = ffmpeg.avcodec_send_packet(decoderContext, packet);
                    if (ret < 0)
                    {
                        _logger.LogError("Error sending packet to decoder: {Error}", ret);
                        break;
                    }

                    ret = ffmpeg.avcodec_receive_frame(decoderContext, frame);
                    if (ret < 0)
                    {
                        _logger.LogError("Error receiving frame from decoder: {Error}", ret);
                        break;
                    }

                    // Теперь, когда у нас есть декодированный кадр, создаем scaling context
                    bool needsScaling = (frame->width != targetWidth ||
                                       frame->height != targetHeight ||
                                       (AVPixelFormat)frame->format != encoderContext->pix_fmt);

                    AVFrame* frameToEncode;

                    if (needsScaling)
                    {
                        // Создаем scaling context на основе реальных размеров кадра
                        swsContext = CreateScalingContextFromFrame(frame, targetWidth, targetHeight, encoderContext->pix_fmt);

                        // Подготавливаем кадр для масштабирования
                        scaledFrame->width = targetWidth;
                        scaledFrame->height = targetHeight;
                        scaledFrame->format = (int)encoderContext->pix_fmt;
                        scaledFrame->color_range = encoderContext->color_range;

                        ret = ffmpeg.av_frame_get_buffer(scaledFrame, 32);
                        if (ret < 0)
                        {
                            _logger.LogError("Error allocating buffer for frame: {Error}", ret);
                            break;
                        }

                        // Масштабируем кадр
                        ret = ffmpeg.sws_scale(swsContext, frame->data, frame->linesize, 0, frame->height,
                            scaledFrame->data, scaledFrame->linesize);

                        if (ret <= 0)
                        {
                            _logger.LogError("Error scaling frame: {Error}", ret);
                            break;
                        }

                        frameToEncode = scaledFrame;
                    }
                    else
                    {
                        // Scaling не нужен, используем исходный кадр
                        frameToEncode = frame;
                    }

                    // Кодируем в JPEG
                    ret = ffmpeg.avcodec_send_frame(encoderContext, frameToEncode);
                    if (ret < 0)
                    {
                        _logger.LogError("Error sending frame to encoder: {Error}", ret);
                        break;
                    }

                    ret = ffmpeg.avcodec_receive_packet(encoderContext, jpegPacket);
                    if (ret < 0)
                    {
                        _logger.LogError("Error receiving JPEG packet: {Error}", ret);
                        break;
                    }

                    // Копируем данные JPEG в управляемый буфер
                    byte[] jpegData = new byte[jpegPacket->size];
                    Marshal.Copy((IntPtr)jpegPacket->data, jpegData, 0, jpegPacket->size);

                    return jpegData;
                }

                ffmpeg.av_packet_unref(packet);
            }
        }
        finally
        {
            if (swsContext != null)
            {
                ffmpeg.sws_freeContext(swsContext);
            }
            ffmpeg.av_frame_free(&frame);
            ffmpeg.av_frame_free(&scaledFrame);
            ffmpeg.av_packet_free(&packet);
            ffmpeg.av_packet_free(&jpegPacket);
        }

        return null;
    }

    /// <summary>
    /// Создает контекст масштабирования на основе реального кадра
    /// </summary>
    private SwsContext* CreateScalingContextFromFrame(AVFrame* frame, int targetWidth, int targetHeight, AVPixelFormat targetFormat)
    {
        var srcFormat = (AVPixelFormat)frame->format;

        _logger.LogInformation("Creating scaling context from frame: Source format={SourceFormat}, Target format={TargetFormat}, Dimensions: {Width}x{Height} -> {TargetWidth}x{TargetHeight}",
            srcFormat, targetFormat, frame->width, frame->height, targetWidth, targetHeight);

        var swsContext = ffmpeg.sws_getContext(
            frame->width, frame->height, srcFormat,
            targetWidth, targetHeight, targetFormat,
            ffmpeg.SWS_BILINEAR | ffmpeg.SWS_ACCURATE_RND, null, null, null);

        if (swsContext == null)
        {
            throw new ApplicationException($"Failed to create scaling context from frame. Source format: {srcFormat}, Target format: {targetFormat}, Dimensions: {frame->width}x{frame->height} -> {targetWidth}x{targetHeight}");
        }

        return swsContext;
    }




}
