using Orleans.Concurrency;

namespace Teslametrics.MediaServer.Orleans.Camera;

public interface ICameraStreamPreviewGrain : IGrainWithGuidKey
{
    [ReadOnly]
    public Task<byte[]?> GetPreviewAsync();

    public Task SubscribeAsync(SubscribeRequest request);

    public Task UnsubscribeAsync(UnsubscribeRequest request);

    [GenerateSerializer]
    public record SubscribeRequest(IPreviewObserver Observer);

    [GenerateSerializer]
    public record UnsubscribeRequest(IPreviewObserver Observer);
}