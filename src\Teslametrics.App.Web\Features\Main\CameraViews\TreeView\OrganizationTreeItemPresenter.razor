﻿@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<MudTreeViewItem @bind-Expanded="@Presenter.Expanded"
                 Items="@Presenter.Children"
                 Value="@Presenter.Value"
                 CanExpand="@Presenter.HasChildren"
                 Selected="@false"
                 ReadOnly="true"
                 T="Guid">
    <Content>
        <MudTreeViewItemToggleButton @bind-Expanded="@Presenter.Expanded"
                                     Visible="@Presenter.HasChildren" />
        <div class="d-flex flex-row align-center mud-width-full">
            <MudText Class="text">@Presenter.Text</MudText>
            <MudSpacer />
            <MudStack Row="true"
                      AlignItems="AlignItems.Center">
                <MudTooltip Arrow="true"
                            Placement="Placement.Start"
                            Text="Количество видов в организации">
                    <MudChip T="int"
                             Class="chip"
                             Color="Color.Primary"
                             Size="Size.Small"
                             Icon="@TeslaIcons.Grid.Custom">@Presenter.ViewCount</MudChip>
                </MudTooltip>
                <MudTooltip Arrow="true"
                            Placement="Placement.Start"
                            Text="Количество камер в видах организации">
                    <MudChip T="int"
                             Class="chip"
                             Color="Color.Info"
                             Size="Size.Small"
                             Icon="@Icons.Material.Filled.Camera">@Presenter.CameraCount</MudChip>
                </MudTooltip>
                <AuthorizeView Policy="@AppPermissions.Main.CameraViews.Create.GetEnumPermissionString()"
                               Context="innerContext">
                    <MudTooltip Arrow="true"
                                Placement="Placement.Start"
                                Text="Создать вид">
                        <MudIconButton OnClick="CreateView"
                                       Icon="@TeslaIcons.Actions.Add"
                                       Size="Size.Small" />
                    </MudTooltip>
                </AuthorizeView>
            </MudStack>
        </div>
    </Content>
</MudTreeViewItem>