// VideoTimeline.razor.js - JavaScript интеграция для компонента VideoTimeline
import { TimelineTracker } from "./Plugins/TimelineTracker.js";

/**
 * Менеджер TimelineTracker плагинов для VideoTimeline компонентов
 * Обеспечивает изоляцию между множественными экземплярами плееров
 */
class TimelineTrackerManager {
  constructor() {
    this.trackerInstances = new Map(); // Map<cameraId, TimelineTracker>
    this.dotNetRefs = new Map(); // Map<cameraId, DotNetObjectReference>
  }

  /**
   * Инициализирует TimelineTracker плагин для камеры
   * @param {string} cameraId - ID камеры
   * @param {DotNetObjectReference} dotNetRef - ссылка на C# компонент
   * @param {Object} options - настройки плагина
   */
  async initializeTracker(cameraId, dotNetRef, options = {}) {
    try {
      console.log(
        `[TimelineTracker] Инициализация для камеры ${cameraId}`,
        options
      );

      // Получаем MediaPipeline для данной камеры
      const mediaPipeline = this.getMediaPipelineForCamera(cameraId);
      if (!mediaPipeline) {
        console.warn(
          `[TimelineTracker] MediaPipeline не найден для камеры ${cameraId}`
        );
        return;
      }

      // Создаем экземпляр TimelineTracker плагина
      const timelineTracker = new TimelineTracker(mediaPipeline, options);

      // Подписываемся на события обновления времени
      timelineTracker.addEventListener("time-update", async (event) => {
        await this.handleTimeUpdate(cameraId, event.detail);
      });

      // Подписываемся на метрики производительности
      timelineTracker.addEventListener("metrics-update", (event) => {
        this.handleMetricsUpdate(cameraId, event.detail);
      });

      // Добавляем плагин к MediaPipeline
      mediaPipeline.addPlugin("timelineTracker", timelineTracker);

      // Сохраняем экземпляры
      this.trackerInstances.set(cameraId, timelineTracker);
      this.dotNetRefs.set(cameraId, dotNetRef);

      console.log(
        `[TimelineTracker] Плагин инициализирован для камеры ${cameraId}`
      );
    } catch (error) {
      console.error(
        `[TimelineTracker] Ошибка инициализации для камеры ${cameraId}:`,
        error
      );
    }
  }

  /**
   * Останавливает TimelineTracker плагин для камеры
   * @param {string} cameraId - ID камеры
   */
  stopTracker(cameraId) {
    try {
      const timelineTracker = this.trackerInstances.get(cameraId);
      if (timelineTracker) {
        // Удаляем плагин из MediaPipeline
        const mediaPipeline = this.getMediaPipelineForCamera(cameraId);
        if (mediaPipeline) {
          mediaPipeline.removePlugin("timelineTracker");
        }

        this.trackerInstances.delete(cameraId);
      }

      this.dotNetRefs.delete(cameraId);

      console.log(`[TimelineTracker] Плагин остановлен для камеры ${cameraId}`);
    } catch (error) {
      console.error(
        `[TimelineTracker] Ошибка при остановке плагина для камеры ${cameraId}:`,
        error
      );
    }
  }

  /**
   * Получает MediaPipeline для указанной камеры
   * @param {string} cameraId - ID камеры
   * @returns {MediaPipeline|null} - экземпляр MediaPipeline или null
   */
  getMediaPipelineForCamera(cameraId) {
    // Ищем плеер по ID камеры в глобальном реестре (из MsePlayer.razor.js)
    if (window.players && typeof window.players.get === "function") {
      const player = window.players.get(cameraId);
      if (player && player.pipe) {
        console.log(
          `[TimelineTracker] MediaPipeline найден через глобальный реестр для камеры ${cameraId}`
        );
        return player.pipe;
      }
    }

    // Альтернативный поиск через DOM элементы
    const videoElements = document.querySelectorAll("video");
    for (const video of videoElements) {
      // Проверяем, есть ли у видео элемента связанный MediaPipeline
      if (video._mediaPipeline || video.mediaPipeline) {
        const pipeline = video._mediaPipeline || video.mediaPipeline;
        console.log(
          `[TimelineTracker] MediaPipeline найден через DOM для камеры ${cameraId}`
        );
        return pipeline;
      }
    }

    // Попробуем найти через глобальные переменные
    if (window.currentPlayer && window.currentPlayer.pipe) {
      console.log(
        `[TimelineTracker] MediaPipeline найден через currentPlayer для камеры ${cameraId}`
      );
      return window.currentPlayer.pipe;
    }

    console.warn(
      `[TimelineTracker] MediaPipeline не найден для камеры ${cameraId}. Доступные объекты:`,
      {
        windowPlayers: !!window.players,
        playersType: typeof window.players,
        currentPlayer: !!window.currentPlayer,
        videoElements: document.querySelectorAll("video").length,
      }
    );

    return null;
  }

  /**
   * Обрабатывает обновление времени от TimelineTracker
   * @param {string} cameraId - ID камеры
   * @param {Object} timeData - данные времени
   */
  async handleTimeUpdate(cameraId, timeData) {
    try {
      const dotNetRef = this.dotNetRefs.get(cameraId);
      if (!dotNetRef) {
        console.warn(
          `[TimelineTracker] DotNetRef не найден для камеры ${cameraId}`
        );
        return;
      }

      // Сериализуем данные для передачи в C#
      const timeDataJson = JSON.stringify({
        absoluteTime: timeData.absoluteTime.toISOString(),
        mediaTime: timeData.mediaTime,
        timestamp: timeData.timestamp,
        latency: timeData.latency,
        bufferStart: timeData.bufferStart,
        bufferEnd: timeData.bufferEnd,
        isPlaying: timeData.isPlaying,
        playbackRate: timeData.playbackRate,
      });

      // Вызываем метод C# компонента
      await dotNetRef.invokeMethodAsync("OnTimelineUpdate", timeDataJson);
    } catch (error) {
      // Не логируем ошибки JSDisconnectedException, так как они нормальны при навигации
      if (!error.message?.includes("JSDisconnectedException")) {
        console.error(
          `[TimelineTracker] Ошибка при передаче обновления времени для камеры ${cameraId}:`,
          error
        );
      }
    }
  }

  /**
   * Обрабатывает обновление метрик производительности
   * @param {string} cameraId - ID камеры
   * @param {Object} metrics - метрики производительности
   */
  handleMetricsUpdate(cameraId, metrics) {
    console.debug(`[TimelineTracker] Метрики для камеры ${cameraId}:`, {
      fps: metrics.updatesPerSecond,
      latency: metrics.averageLatency.toFixed(2) + "мс",
      droppedFrames: metrics.droppedFrames,
    });
  }

  /**
   * Получает метрики производительности для камеры
   * @param {string} cameraId - ID камеры
   * @returns {Object|null} - метрики или null
   */
  getMetrics(cameraId) {
    const timelineTracker = this.trackerInstances.get(cameraId);
    return timelineTracker ? timelineTracker.getMetrics() : null;
  }

  /**
   * Освобождает все ресурсы
   */
  dispose() {
    for (const [cameraId, timelineTracker] of this.trackerInstances) {
      try {
        const mediaPipeline = this.getMediaPipelineForCamera(cameraId);
        if (mediaPipeline) {
          mediaPipeline.removePlugin("timelineTracker");
        }
      } catch (error) {
        console.error(
          `[TimelineTracker] Ошибка при освобождении ресурсов для камеры ${cameraId}:`,
          error
        );
      }
    }

    this.trackerInstances.clear();
    this.dotNetRefs.clear();

    console.log("[TimelineTracker] Все ресурсы освобождены");
  }
}

// Создаем глобальный экземпляр менеджера
const timelineTrackerManager = new TimelineTrackerManager();

// Экспортируем функции для использования из C#
export function initializeTimelineTracker(cameraId, dotNetRef, options) {
  return timelineTrackerManager.initializeTracker(cameraId, dotNetRef, options);
}

export function stopTimelineTracker(cameraId) {
  return timelineTrackerManager.stopTracker(cameraId);
}

export function getTimelineMetrics(cameraId) {
  return timelineTrackerManager.getMetrics(cameraId);
}

// Автоматическая очистка при выгрузке страницы
window.addEventListener("beforeunload", () => {
  timelineTrackerManager.dispose();
});

console.log("[TimelineTracker] JavaScript модуль загружен");
