using System.Threading.Channels;

namespace FFMpegNET;

internal static class RtspMicroSegmenterHelper
{
    public static async Task ProcessOutputStream(ChannelReader<(MemoryOutputBuffer Buffer, DateTimeOffset StartTime, double Duration)> channel,
                                                 Func<MicroSegment, Task> onNextMicroSegment,
                                                 CancellationToken cancellationToken)
    {
        bool readAvailable;
        do
        {
            readAvailable = await channel.WaitToReadAsync();
            if (readAvailable && channel.TryRead(out var seg))
            {
                try
                {
                    var microSegment = new MicroSegment(seg.Buffer.Stream.ToArray(), seg.StartTime, seg.Duration);
                    await onNextMicroSegment(microSegment);
                }
                finally
                {
                    seg.Buffer.Dispose();
                }

            }
        }
        while (!cancellationToken.IsCancellationRequested && readAvailable);
    }
}
