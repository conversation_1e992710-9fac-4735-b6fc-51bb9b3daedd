::deep .swipe_container:has(> .camera_tree) {
	display:grid;
	grid-template-columns: 0.5fr 1fr;   /* ширина столбцов */
    grid-template-rows: 1fr;
	grid-template-areas:
	  "tree   content"   /* первая строка */
	  ".      content";  /* вторая строка, только content */
	gap:16px;
}

::deep .swipe_container .camera_tree {
	grid-area: tree;
	border-radius: 16px;
    height: -webkit-fill-available;
}

::deep .swipe_container .content {
    height: 100%;
    padding: 0 8px;
    height: 100%;
    grid-area: content;
}
