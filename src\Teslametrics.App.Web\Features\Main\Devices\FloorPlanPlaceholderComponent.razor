﻿@if (CityId is not null && BuildingId is not null && FloorId is null)
{
    <MudStack AlignItems="AlignItems.Center" Justify="Justify.Center" Class="mud-height-full">
        <MudIcon Icon="@Icons.Material.Filled.Domain" Size="Size.Large" Color="Color.Primary" />
        <MudText Typo="Typo.subtitle1" Class="mt-2">Этаж не выбран</MudText>
        <MudText Typo="Typo.body2" Color="Color.Secondary">
            Чтобы отобразить план, выберите этаж из списка сверху слева.
        </MudText>
    </MudStack>
}
@code {
    [Parameter]
    public Guid? CityId { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }

    [Parameter]
    public Guid? FloorId { get; set; }
}
