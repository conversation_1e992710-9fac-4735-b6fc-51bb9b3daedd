﻿@if (CityId is not null && BuildingId is not null && FloorId is null)
{
    <MudPaper Class="d-flex flex-column align-center justify-center" Style="height: 100%; min-height: 300px; padding: 2rem; text-align: center;">
        <MudIcon Icon="@Icons.Material.Filled.Domain" Size="Size.Large" Color="Color.Primary" />
        <MudText Typo="Typo.h5" Class="mt-2">Выберите этаж</MudText>
        <MudText Typo="Typo.body1" Class="mt-1" Color="Color.Secondary">
            Чтобы продолжить, пожалуйста, выберите этаж здания из списка.
        </MudText>
    </MudPaper>
}
@code {
    [Parameter]
    public Guid? CityId { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }

    [Parameter]
    public Guid? FloorId { get; set; }
}
