using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Devices.RoomContent;

public static class GetRoomContentUseCase
{
    public record Query(Guid RoomId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public string Name { get; set; } // Название комнаты

        public List<Guid> Fridges { get; init; }

        public List<Guid> Cameras { get; init; }

        public int ActiveIncidentCount { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(string name, List<Guid> fridges, List<Guid> cameras, int activeIncidentCount)
        {
            Name = name;
            Fridges = fridges;
            Cameras = cameras;
            ActiveIncidentCount = activeIncidentCount;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Name = string.Empty;
            Fridges = [];
            Cameras = [];
            ActiveIncidentCount = 0;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        PlanNotFound,
        RoomNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(r => r.RoomId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!await CheckTableExistsAsync())
            {
                return new Response(Result.PlanNotFound);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Plans.Props.Page)
                .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

            var pageJson = await _dbConnection.ExecuteScalarAsync<string?>(template.RawSql);

            if (string.IsNullOrEmpty(pageJson))
            {
                return new Response(Result.PlanNotFound);
            }

            var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

            var room = page.GetRoom(request.RoomId);

            if (room is null)
            {
                return new Response(Result.RoomNotFound);
            }

            template = SqlQueryBuilder.Create()
                .Select($"COUNT({Db.Incidents.Props.Id})")
                .Where(Db.Incidents.Props.RoomId, ":RoomId", SqlOperator.Equals, new { request.RoomId })
                .Where(Db.Incidents.Props.ResolvedAt, SqlOperator.IsNull)
                .Build(QueryType.Standard, Db.Incidents.Table, RowSelection.AllRows);

            var incidentCount = await _dbConnection.ExecuteScalarAsync<int>(template.RawSql, template.Parameters);

            var fridges = room.Fridges.Select(f => f.Id).ToList();

            return new Response(room.Name, fridges, room.Cameras.Select(c => c.Id).ToList(), incidentCount);
        }

        private async Task<bool> CheckTableExistsAsync()
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = Db.Plans.Table });

            return tableExists > 0;
        }
    }

    public record IncidentModel(Guid RoomId, Guid DeviceId);
}