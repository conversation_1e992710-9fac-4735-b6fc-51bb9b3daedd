.grid {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    height: -webkit-fill-available;
    align-items: start;
}

.shadow {
    box-shadow: var(--mud-elevation-1);
    height: 1px;
    z-index: 1;
    margin-top: -1px;
}

::deep .item {
    height: -webkit-fill-available;
}

/* Chart container styles */
::deep .apexcharts-canvas {
    margin: 0 auto;
}

/* Filter styles */
::deep .mud-button-group {
    overflow: hidden;
    border-radius: 4px;
}

::deep .mud-button-group .mud-button {
    min-width: 80px;
}

/* Chart title styles */
::deep .mud-typography.mud-typography-subtitle1 {
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 960px) {
    ::deep .mud-grid-item {
        flex-basis: 100%;
        max-width: 100%;
        height: fit-content;
    }
    ::deep .incidents_dashboard {
        z-index: 0;
        height: -webkit-fill-available;
        padding: 12px 8px;
    }
}