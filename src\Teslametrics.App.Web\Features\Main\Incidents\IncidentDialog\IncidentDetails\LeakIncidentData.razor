﻿@attribute [StreamRendering(true)]
@inherits IncidentDataBaseComponent
<div class="d-flex gap-2 flex-column">
    <MudText><b>Обнаружена протечка</b></MudText>
    <MudText>Продолжительность события: @Duration.ToString(@"hh\:mm\:ss")</MudText>
    <MudText>Начало: @FormatGmt3(FiredAt)</MudText>
    <MudText>Окончание: @(ResolvedAd is null ? "не устранено" : FormatGmt3(ResolvedAd.Value))</MudText>
</div>