using FluentValidation;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.App.Web.Services.UserDevice;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.FolderCreateDialog;

public partial class FolderCreateDialog
{
    #region [Types]
    private class CreateFolderModel
    {
        public Guid OrganizationId { get; set; }
        public string Name { get; set; }

        public CreateFolderModel(Guid organizationId, string name)
        {
            OrganizationId = organizationId;
            Name = name;
        }
    }

    private class Validator : BaseFluentValidator<CreateFolderModel>
    {
        public Validator()
        {
            RuleFor(model => model.Name)
                .NotEmpty()
                .WithMessage("Название является обязательным")
                .Length(3, 120)
                .WithMessage("наименование должно быть длиной от 3 до 120 символов");
        }
    }
    #endregion

    #region [Dialog]
    private DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Small, NoHeader = true, BackdropClick = false };
    private bool _isVisible;

    [Inject]
    private IUserDeviceService _userDeviceService { get; set; } = null!;
    #endregion

    #region [Form]
    private static readonly Validator _validator = new();
    private bool _isValid;
    private CreateFolderModel? _model;
    #endregion

    protected override void OnInitialized()
    {
        CompositeDisposable.Add(EventSystem.Subscribe<CameraGroupCreateEto>(OnCreateHandler));

        if (_userDeviceService.IsMobile)
        {
            _dialogOptions = new DialogOptions
            {
                CloseOnEscapeKey = true,
                FullScreen = true,
                NoHeader = true,
                BackdropClick = false
            };
        }

        base.OnInitialized();
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            UnsubscribeFromAuthStateChanged();
        }

        base.Dispose(disposing);
    }

    #region [Actions]
    private async Task SubmitAsync()
    {
        if (_model is null) return;

        CreateFolderUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new CreateFolderUseCase.Command(_model.OrganizationId, _model.Name));
        }
        catch (Exception ex)
        {
            response = null;
            Snackbar.Add("не удалось создать директорию из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case CreateFolderUseCase.Result.Success:
                Snackbar.Add("Директория успешно добавлена", MudBlazor.Severity.Success);
                Cancel();
                break;
            case CreateFolderUseCase.Result.ValidationError:
                Snackbar.Add("не удалось создать Директорию из-за ошибки валдиации", MudBlazor.Severity.Error);
                break;
            case CreateFolderUseCase.Result.FolderNameAlreadyExists:
                Snackbar.Add("Директория с данным именем уже существует", MudBlazor.Severity.Error);
                break;
            case CreateFolderUseCase.Result.OrganizationNotFound:
                Snackbar.Add("Организация не найдена", MudBlazor.Severity.Error);
                break;
            case CreateFolderUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FolderCreateDialog), nameof(CreateFolderUseCase));
                Snackbar.Add($"Не удалось создать директорию из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(FolderCreateDialog), nameof(CreateFolderUseCase), response.Result);
                Snackbar.Add($"Не удалось создать директорию из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private void Cancel()
    {
        _isVisible = false;
        UnsubscribeFromAuthStateChanged();
    }

    private void SubscribeToAuthStateChanged()
    {
        AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
    }

    private void UnsubscribeFromAuthStateChanged()
    {
        AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
    }
    #endregion

    #region [Event Handlers]
    private async Task OnCreateHandler(CameraGroupCreateEto eto)
    {
        var userAuthState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (userAuthState is null) return;

        var policyRequirementResource = new PolicyRequirementResource(eto.OrganizationId, eto.OrganizationId);
        var authorizationResult = await AuthorizationService.AuthorizeAsync(
            userAuthState.User,  // Current user from AuthenticationStateProvider
            policyRequirementResource, // The resource being authorized
            AppPermissions.Main.Folders.Create.GetEnumPermissionString() // The policy name
        );

        if (!authorizationResult.Succeeded)
        {
            Snackbar.Add("Недостаточно прав для создания директории", MudBlazor.Severity.Warning);
            return;
        }

        SubscribeToAuthStateChanged();

        _isValid = true;
        _model = new(eto.OrganizationId, "Новая директория");
        _isVisible = true;
        await UpdateViewAsync();
    }

    private async void OnAuthenticationStateChanged(Task<AuthenticationState> authState)
    {
        if (_model is null)
        {
            Cancel();
            return;
        }

        var userAuthState = await authState;
        var policyRequirementResource = new PolicyRequirementResource(_model.OrganizationId, _model.OrganizationId);
        var authorizationResult = await AuthorizationService.AuthorizeAsync(
            userAuthState.User,  // Current user from AuthenticationStateProvider
            policyRequirementResource, // The resource being authorized
            AppPermissions.Main.Folders.Create.GetEnumPermissionString() // The policy name
        );

        if (!authorizationResult.Succeeded)
        {
            Snackbar.Add("Недостаточно прав для создания директории", MudBlazor.Severity.Warning);
            Cancel();
            return;
        }
    }
    #endregion
}
