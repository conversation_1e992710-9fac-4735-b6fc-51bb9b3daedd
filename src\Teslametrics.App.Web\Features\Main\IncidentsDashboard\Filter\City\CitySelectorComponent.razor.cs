using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.Filter.City;

public partial class CitySelectorComponent
{
    private GetCityListUseCase.Response.Item? _selectedCity;

    [Parameter]
    public Guid? City { get; set; }
    [Parameter]
    public EventCallback<Guid?> CityChanged { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        if (_selectedCity?.Id != City)
        {
            _selectedCity = null;
            await GetDataAsync();
        }
        await base.OnParametersSetAsync();
    }

    private async Task GetDataAsync()
    {
        if (City is null) return;

        GetCityUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetCityUseCase.Query(City.Value));
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching cities");
            Snackbar.Add("Не удалось получить город из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case GetCityUseCase.Result.Success:
                _selectedCity = new GetCityListUseCase.Response.Item(City.Value, response.Name);
                break;
            case GetCityUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении города", MudBlazor.Severity.Error);
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CitySelectorComponent), nameof(GetCityUseCase));
                break;
            case GetCityUseCase.Result.CityNotFound:
                Snackbar.Add("Город не найден", MudBlazor.Severity.Error);
                City = null;
                if (CityChanged.HasDelegate)
                    await CityChanged.InvokeAsync(null);
                break;
            case GetCityUseCase.Result.Unknown:
                Snackbar.Add("Не удалось получить город из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CitySelectorComponent), nameof(GetCityUseCase));
                break;
        }
    }

    private async Task CityValueChanged(GetCityListUseCase.Response.Item? city)
    {
        _selectedCity = city;
        await CityChanged.InvokeAsync(city?.Id);
    }

    private async Task<IEnumerable<GetCityListUseCase.Response.Item>> SearchCityAsync(string value, CancellationToken token)
    {
        GetCityListUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetCityListUseCase.Query(value), token);
        }
        catch (TaskCanceledException)
        {
            // Search was canceled, just return empty result
            return [];
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching cities");
            Snackbar.Add("Не удалось получить список городов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
            return [];
        }

        if (response.Result == GetCityListUseCase.Result.Success)
            return response.Items;

        if (response.Result == GetCityListUseCase.Result.ValidationError)
            Snackbar.Add("Ошибка валидации при получении списка городов", MudBlazor.Severity.Error);
        else
            Snackbar.Add("Не удалось получить список городов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);

        return [];
    }
}
