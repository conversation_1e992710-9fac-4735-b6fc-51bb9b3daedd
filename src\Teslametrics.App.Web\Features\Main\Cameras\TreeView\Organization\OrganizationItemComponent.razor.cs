using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Cameras.TreeView.Organization;

public partial class OrganizationItemComponent
{
    private DirectoryTreeViewComponent.TreeItemPresenter? _presenter => Item as DirectoryTreeViewComponent.TreeItemPresenter;
    private bool _selected => SelectedOrganizationId is not null && SelectedOrganizationId == _presenter?.Id;

    [Parameter]
    public Guid? SelectedOrganizationId { get; set; }

    [Parameter]
    [EditorRequired]
    public TreeItemData<Guid> Item { get; set; } = null!;

    [Parameter]
    [EditorRequired]
    public EventCallback<TreeItemData<Guid>> OnSelect { get; set; }

    protected override bool ShouldRender()
    {
        if (_presenter == null)
            return false;

        return true;
    }

    private async Task SelectHandler()
    {
        if (OnSelect.HasDelegate)
            await OnSelect.InvokeAsync(Item);
    }

    private void Create()
    {
        EventSystem.Publish(new CameraGroupCreateEto(Item.Value));
    }

    private async Task DisconnectAsync()
    {
        if (IsLoading) return;

        DisconnectCameraListUseCase.Response? response;
        try
        {
            await SetLoadingAsync(true);
            response = await ScopeFactory.MediatorSend(new DisconnectCameraListUseCase.Command(Item.Value));
        }
        catch (Exception ex)
        {
            response = null;
            Snackbar.Add($"Не удалось отключить камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetLoadingAsync(false);

        if (response is null) return;
        switch (response.Result)
        {
            case DisconnectCameraListUseCase.Result.Success:
                Snackbar.Add("Отправлен запрос на отключение камер", Severity.Success);
                break;
            case DisconnectCameraListUseCase.Result.ValidationError:
                Snackbar.Add("Не удалось отключить камеры из-за ошибки валидации", Severity.Error);
                break;
            case DisconnectCameraListUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(OrganizationItemComponent), nameof(DisconnectCameraListUseCase));
                Snackbar.Add($"Не удалось отключить камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(OrganizationItemComponent), nameof(DisconnectCameraListUseCase), response.Result);
                Snackbar.Add($"Не удалось отключить камеры из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task ConnectAsync()
    {
        if (IsLoading) return;

        ConnectCameraListUseCase.Response? response;
        try
        {
            await SetLoadingAsync(true);
            response = await ScopeFactory.MediatorSend(new ConnectCameraListUseCase.Command(Item.Value));
        }
        catch (Exception ex)
        {
            response = null;
            Snackbar.Add($"Не удалось подключить камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetLoadingAsync(false);

        if (response is null) return;
        switch (response.Result)
        {
            case ConnectCameraListUseCase.Result.Success:
                Snackbar.Add("Отправлен запрос на подключение камер", Severity.Success);
                break;
            case ConnectCameraListUseCase.Result.ValidationError:
                Snackbar.Add("Не удалось подключить камеры из-за ошибки валидации", Severity.Error);
                break;
            case ConnectCameraListUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(OrganizationItemComponent), nameof(ConnectCameraListUseCase));
                Snackbar.Add($"Не удалось подключить камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(OrganizationItemComponent), nameof(ConnectCameraListUseCase), response.Result);
                Snackbar.Add($"Не удалось подключить камеры из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
}
