using FluentValidation;
using MediatR;
using System.Reactive.Linq;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Features.Main.SystemSettings;
using Teslametrics.Core.Domain.Incidents.Events;
using Teslametrics.Core.Services.DomainEventBus;

namespace Teslametrics.App.Web.Features.Main.Devices.RoomContent;

public static class SubscribeRoomContentUseCase
{
    public record Request(IObserver<UpdatedEvent> Observer, Guid RoomId) : BaseRequest<Response>; // В теории могу ещё и ID датчиков прокинуть! Но их тогда нужно добавить в Get

    public record Response : BaseResponse
    {
        public IDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

    // События, на которые можно подписаться
    public record UpdatedEvent;

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        RoomNotFound
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IValidator<Request> _validator;
        private readonly IDomainEventBus _domainEventBus;

        public Handler(IValidator<Request> validator,
                       IDomainEventBus domainEventBus)
        {
            _validator = validator;
            _domainEventBus = domainEventBus;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var eventStream = await _domainEventBus.GetEventStreamAsync();


            // Хотелось бы узнавать:
            //  1. В комнату добавлен датчик
            //  2. В комнате удалён датчик
            //  3. В одном из датчиков холодильника случилось происшествие
            //  4. В одном из датчиков холодильника было решено происшествие
            //  5. В одном из датчиков комнаты случилось происшествие
            //  6. В одном из датчиков комнаты было решено происшествие
            //  5. Изминилась информация о комнате (название и т.д.)
            //  7. Холодильник был добавлен
            //  8. Холодильник был удалён
            //  9. Камера была добавлена
            // 10. Камера была удалёна
            // 11. Комната была удалена
            // Подписываемся на события, связанные с комнатой и её содержимым
            var subscription = eventStream
                .Where(e => e switch
                {
                    PlanUpdatedEvent => true,
                    IncidentCreatedEvent @event => @event.RoomId == request.RoomId,
                    IncidentResolvedEvent @event => @event.RoomId == request.RoomId,
                    _ => false
                })
                .Select(e => e switch
                {
                    PlanUpdatedEvent => new UpdatedEvent(),
                    IncidentCreatedEvent => new UpdatedEvent(),
                    IncidentResolvedEvent => new UpdatedEvent(),
                    _ => throw new AppException("Invalid event type")
                })
                .Subscribe(request.Observer);

            return new Response(subscription);
        }
    }
}