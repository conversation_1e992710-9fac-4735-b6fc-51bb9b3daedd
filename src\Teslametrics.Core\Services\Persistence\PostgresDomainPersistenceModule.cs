using System.Data;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;
using Teslametrics.Core.Domain.AccessControl;
using Teslametrics.Core.Domain.AccessControl.Organizations;
using Teslametrics.Core.Domain.AccessControl.Users;
using Teslametrics.Core.Domain.CameraPresets;
using Teslametrics.Core.Domain.Cameras;
using Teslametrics.Core.Domain.CameraViews;
using Teslametrics.Core.Domain.Folders;
using Teslametrics.Core.Domain.Incidents;
using Teslametrics.Core.Domain.Notifications;
using Teslametrics.Core.Domain.PublicLinks;

namespace Teslametrics.Core.Services.Persistence;

public static class PostgresDomainPersistenceModule
{
    public static void Install(IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("Default") ?? throw new InvalidOperationException("Connection string not found.");

        SqlMapper.AddTypeHandler(typeof(DateTimeOffset), new DateTimeOffsetTypeHandler());

        DefaultTypeMap.MatchNamesWithUnderscores = true;

        services.AddDbContextPool<CommandAppDbContext>(options =>
        {
            options.UseNpgsql(connectionString, b => b.MigrationsAssembly(typeof(PostgresDomainPersistenceModule).Assembly.FullName));
            options.UseSnakeCaseNamingConvention();
        });

        services.AddTransient<IDbConnection>(s => new NpgsqlConnection(connectionString));

        services.AddTransient<IOrganizationRepository, OrganizationRepository>();

        services.AddTransient<IUserRepository, UserRepository>();

        services.AddTransient<IPermissionRepository, PermissionRepository>();

        services.AddTransient<ICameraRepository, CameraRepository>();

        services.AddTransient<IFolderRepository, FolderRepository>();

        services.AddTransient<IPresetRepository, PresetRepository>();

        services.AddTransient<IPublicLinkRepository, PublicLinkRepository>();

        services.AddTransient<ICameraViewRepository, CameraViewRepository>();

        services.AddTransient<IIncidentRepository, IncidentRepository>();

        services.AddTransient<IIncidentNotificationRepository, IncidentNotificationRepository>();
    }
}