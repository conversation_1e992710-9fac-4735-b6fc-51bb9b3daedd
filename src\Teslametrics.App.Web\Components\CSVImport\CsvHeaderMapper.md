# CsvHeaderMapper Component

Blazor компонент для настройки маппинга между заголовками CSV файла и полями модели данных с использованием MudBlazor и drag-and-drop функциональности.

## Особенности

- ✅ Drag-and-drop интерфейс с использованием MudDropContainer
- ✅ Поддержка дублирования заголовков (один CSV заголовок на несколько полей)
- ✅ Визуальная индикация обязательных полей
- ✅ Валидация маппинга
- ✅ Паттерн MappedHeaders для упрощения управления состоянием
- ✅ Следование принципам SOLID
- ✅ Двусторонняя привязка данных
- ✅ Адаптивный дизайн
- ✅ **Простое решение без JavaScript** - точное удаление маппингов через виртуальные экземпляры
- ✅ **Высокая производительность** - минимальные накладные расходы
- ✅ **Надежность** - нет зависимостей от DOM-событий

## Структуры данных

```csharp
public record CsvHeader(string Name)
{
    /// <summary>
    /// Уникальный идентификатор экземпляра заголовка для отслеживания в разных зонах.
    /// </summary>
    public string InstanceId { get; init; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Идентификатор зоны, к которой принадлежит этот экземпляр заголовка.
    /// Пустая строка означает пул доступных заголовков.
    /// </summary>
    public string ZoneId { get; init; } = string.Empty;

    /// <summary>
    /// Создает новый экземпляр заголовка для указанной зоны.
    /// </summary>
    public CsvHeader ForZone(string zoneId) => this with { ZoneId = zoneId, InstanceId = Guid.NewGuid().ToString() };

    /// <summary>
    /// Проверяет логическое равенство заголовков по имени (игнорируя InstanceId и ZoneId).
    /// </summary>
    public virtual bool Equals(CsvHeader? other) => other != null && Name.Equals(other.Name, StringComparison.OrdinalIgnoreCase);

    public override int GetHashCode() => Name.GetHashCode(StringComparison.OrdinalIgnoreCase);
};

public record ModelField(string Label, string Field, bool Required);
public record HeaderMapping(CsvHeader CsvHeader, ModelField ModelField);
```

**Ключевые особенности модели CsvHeader:**
- `InstanceId` - уникальный идентификатор для отслеживания конкретного экземпляра
- `ZoneId` - идентификатор зоны, позволяющий точно определить источник drag-операции
- `ForZone()` - метод создания экземпляра для конкретной зоны
- `Equals()` - логическое равенство по имени, игнорирующее технические поля
- Сохраняется совместимость с существующим API компонента

## Параметры компонента

| Параметр | Тип | Обязательный | Описание |
|----------|-----|--------------|----------|
| `Fields` | `IEnumerable<ModelField>` | ✅ | Поля модели для импорта |
| `Headers` | `IEnumerable<CsvHeader>` | ✅ | Заголовки CSV файла |
| `Mapping` | `IEnumerable<HeaderMapping>` | ❌ | Текущий маппинг с двусторонней привязкой |
| `MappingChanged` | `EventCallback<IEnumerable<HeaderMapping>>` | ❌ | Событие изменения маппинга |
| `AllowDuplicateHeaders` | `bool` | ❌ | Разрешить дублирование заголовков (по умолчанию false) |
| `LocaleStrings` | `HeaderMapperLocales` | ❌ | Локализация |

## Свойства

| Свойство | Тип | Описание |
|----------|-----|----------|
| `MappedHeaders` | `IEnumerable<HeaderMapping>` | Паттерн MappedHeaders - текущий маппинг |
| `IsValid` | `bool` | Проверяет, что все обязательные поля назначены |

## Пример использования

```razor
<CsvHeaderMapper Fields="@modelFields"
                 Headers="@csvHeaders"
                 @bind-Mapping="@currentMapping"
                 AllowDuplicateHeaders="true"
                 LocaleStrings="@localeStrings" />

@code {
    private IEnumerable<CsvHeaderMapper.HeaderMapping> currentMapping = [];

    private readonly IEnumerable<CsvHeaderMapper.CsvHeader> csvHeaders = new[]
    {
        new CsvHeaderMapper.CsvHeader("Имя"),
        new CsvHeaderMapper.CsvHeader("Email"),
        new CsvHeaderMapper.CsvHeader("Телефон")
    };

    private readonly IEnumerable<CsvHeaderMapper.ModelField> modelFields = new[]
    {
        new CsvHeaderMapper.ModelField("Имя пользователя", "firstName", true),
        new CsvHeaderMapper.ModelField("Электронная почта", "email", true),
        new CsvHeaderMapper.ModelField("Номер телефона", "phone", false)
    };

    private readonly HeaderMapperLocales localeStrings = new()
    {
        EntityFieldsTitle = "Поля модели данных",
        FileHeadersTitle = "Доступные CSV заголовки",
        DropZoneText = "Перетащите заголовки сюда"
    };
}
```

## Функциональность

### Верхняя зона - пул CSV заголовков
- Отображает доступные для перетаскивания CSV заголовки
- При `AllowDuplicateHeaders = false` показывает только неназначенные заголовки
- При `AllowDuplicateHeaders = true` показывает все заголовки

### Нижняя область - поля модели
- Каждое поле модели представлено как зона для сброса заголовков
- Обязательные поля отмечены красным значком предупреждения
- Ограничение сброса на занятые поля (если `AllowDuplicateHeaders = false`)

### Drag-and-Drop логика
- Перетаскивание заголовка на поле создает маппинг
- Перетаскивание заголовка в пустую зону удаляет маппинг
- **Одно поле модели = один CSV заголовок максимум** (критическое правило)
- **🔄 Обмен заголовков местами** - при перетаскивании заголовка из одного поля на другое занятое поле происходит обмен
- При перетаскивании из пула на занятое поле происходит **замещение** существующего маппинга
- При дублировании (`AllowDuplicateHeaders = true`): один заголовок может быть назначен на несколько полей
- При запрете дублирования (`AllowDuplicateHeaders = false`): один заголовок только на одно поле

#### Сценарии обмена заголовков:
- **Условие**: Перетаскивание заголовка из поля A на поле B, где оба поля имеют назначенные заголовки
- **Результат**: Заголовки меняются местами между полями A и B
- **Исключения**: Обмен НЕ происходит при перетаскивании из пула заголовков или на свободное поле

### Исправленная логика инициализации
- `MudDropContainer.Items` содержит ВСЕ CSV заголовки
- `ItemsSelector` правильно определяет размещение каждого элемента в соответствующих зонах
- Элементы корректно отображаются только в назначенных им зонах при инициализации

### Точное удаление маппингов без JavaScript
- Каждый экземпляр заголовка содержит информацию о своей зоне (`ZoneId`)
- Виртуальные экземпляры создаются для каждой зоны (пул + поля модели)
- При drag-операции система точно знает, из какой зоны был перетащен заголовок
- Удаляется именно тот маппинг, который соответствует источнику drag-операции
- Решение работает полностью на уровне C#/Blazor без JavaScript-зависимостей

## Техническая реализация

### Проблема, которую решает компонент
При drag-and-drop операциях с дублированием заголовков система ранее удаляла неправильный маппинг (FIFO вместо конкретного). Новая реализация решает эту проблему через виртуальные экземпляры заголовков.

### Алгоритм работы

#### 1. Создание виртуальных экземпляров
```csharp
private void UpdateAllHeaderInstances()
{
    _allHeaderInstances.Clear();

    // Экземпляры для пула доступных заголовков (ZoneId = "")
    foreach (var header in Headers)
    {
        if (AllowDuplicateHeaders || !Mapping.Any(m => m.CsvHeader.Equals(header)))
        {
            _allHeaderInstances.Add(header.ForZone(string.Empty));
        }
    }

    // Экземпляры для каждого назначенного маппинга (ZoneId = field.Field)
    foreach (var mapping in Mapping)
    {
        var instanceForField = mapping.CsvHeader.ForZone(mapping.ModelField.Field);
        _allHeaderInstances.Add(instanceForField);
    }
}
```

#### 2. Упрощенный ItemSelector
```csharp
private bool ItemSelector(CsvHeader headerInstance, string zoneIdentifier)
{
    // Экземпляр отображается в той зоне, для которой он был создан
    return headerInstance.ZoneId == zoneIdentifier;
}
```

#### 3. Валидация drop-операций
```csharp
private bool CanDrop(CsvHeader header, string identifier)
{
    if (identifier == string.Empty)
        return true; // Всегда можно вернуть в пул

    var targetField = Fields.FirstOrDefault(f => f.Field == identifier);
    if (targetField is null) return false;

    // Проверяем, есть ли уже маппинг для этого поля
    var existingMapping = Mapping.FirstOrDefault(m => m.ModelField.Equals(targetField));

    if (existingMapping != null)
    {
        // Если заголовок уже назначен на это поле, разрешаем
        if (existingMapping.CsvHeader.Equals(header))
            return true;

        // Если это другой заголовок, разрешаем замещение
        return true;
    }

    // Если поле свободно, проверяем ограничения дублирования заголовков
    if (!AllowDuplicateHeaders)
    {
        return !Mapping.Any(m => m.CsvHeader.Equals(header));
    }

    return true;
}
```

#### 4. Обработка обмена заголовков местами
```csharp
private async Task HandleFieldDrop(MudItemDropInfo<CsvHeader> dropInfo)
{
    var headerInstance = dropInfo.Item!;
    var targetField = Fields.FirstOrDefault(f => f.Field == dropInfo.DropzoneIdentifier);
    var updatedMapping = Mapping.ToList();

    // Проверяем условия для обмена заголовков местами
    if (ShouldSwapHeaders(headerInstance, targetField, out var sourceField, out var targetMapping, out var sourceMapping))
    {
        await HandleHeaderSwap(sourceField!, targetField, sourceMapping!, targetMapping!, updatedMapping);
        return;
    }

    // Стандартная логика замещения/назначения
    await HandleStandardFieldDrop(headerInstance, targetField, updatedMapping);
}

private bool ShouldSwapHeaders(
    CsvHeader headerInstance,
    ModelField targetField,
    out ModelField? sourceField,
    out HeaderMapping? targetMapping,
    out HeaderMapping? sourceMapping)
{
    // Проверяем условия для обмена:
    // 1. Источник - поле модели (не пул)
    // 2. Целевое поле имеет назначенный заголовок
    // 3. Исходное поле имеет перетаскиваемый заголовок
    // 4. Заголовки разные

    sourceField = null;
    targetMapping = null;
    sourceMapping = null;

    if (string.IsNullOrEmpty(headerInstance.ZoneId))
        return false;

    sourceField = Fields.FirstOrDefault(f => f.Field == headerInstance.ZoneId);
    if (sourceField == null || sourceField.Equals(targetField))
        return false;

    targetMapping = Mapping.FirstOrDefault(m => m.ModelField.Equals(targetField));
    if (targetMapping == null)
        return false;

    var tempSourceField = sourceField;
    sourceMapping = Mapping.FirstOrDefault(m =>
        m.CsvHeader.Equals(headerInstance) && m.ModelField.Equals(tempSourceField));
    if (sourceMapping == null)
        return false;

    if (targetMapping.CsvHeader.Equals(headerInstance))
        return false;

    return true;
}

private async Task HandleHeaderSwap(
    ModelField sourceField,
    ModelField targetField,
    HeaderMapping sourceMapping,
    HeaderMapping targetMapping,
    List<HeaderMapping> updatedMapping)
{
    // Удаляем оба существующих маппинга
    updatedMapping.Remove(sourceMapping);
    updatedMapping.Remove(targetMapping);

    // Создаем новые маппинги с обменом заголовков
    var newSourceMapping = new HeaderMapping(targetMapping.CsvHeader, sourceField);
    var newTargetMapping = new HeaderMapping(sourceMapping.CsvHeader, targetField);

    updatedMapping.Add(newSourceMapping);
    updatedMapping.Add(newTargetMapping);

    await UpdateMappingAsync(updatedMapping);
}
```

#### 5. Обработка стандартного замещения
```csharp
private async Task HandleStandardFieldDrop(
    CsvHeader headerInstance,
    ModelField targetField,
    List<HeaderMapping> updatedMapping)
{
    // КРИТИЧЕСКИ ВАЖНО: Удаляем существующий маппинг для целевого поля
    var existingMappingForField = updatedMapping.FirstOrDefault(m => m.ModelField.Equals(targetField));
    if (existingMappingForField != null)
    {
        updatedMapping.Remove(existingMappingForField);
    }

    // Обрабатываем перемещение из источника
    if (!string.IsNullOrEmpty(headerInstance.ZoneId))
    {
        var sourceField = Fields.FirstOrDefault(f => f.Field == headerInstance.ZoneId);
        if (sourceField != null && !sourceField.Equals(targetField))
        {
            var oldMapping = updatedMapping.FirstOrDefault(m =>
                m.CsvHeader.Equals(headerInstance) && m.ModelField.Equals(sourceField));
            if (oldMapping != null)
                updatedMapping.Remove(oldMapping);
        }
    }

    // Создаем новый маппинг
    var baseHeader = new CsvHeader(headerInstance.Name);
    var newMapping = new HeaderMapping(baseHeader, targetField);
    updatedMapping.Add(newMapping);

    await UpdateMappingAsync(updatedMapping);
}
```

#### 6. Точное удаление маппингов
```csharp
private async Task HandleRemovalDrop(MudItemDropInfo<CsvHeader> dropInfo)
{
    var headerInstance = dropInfo.Item!;

    if (!string.IsNullOrEmpty(headerInstance.ZoneId))
    {
        // Удаляем конкретный маппинг для источника
        var sourceField = Fields.FirstOrDefault(f => f.Field == headerInstance.ZoneId);
        var mappingToRemove = Mapping.FirstOrDefault(m =>
            m.CsvHeader.Equals(headerInstance) && m.ModelField.Equals(sourceField));

        if (mappingToRemove != null)
        {
            var updatedMapping = Mapping.ToList();
            updatedMapping.Remove(mappingToRemove);
            await UpdateMappingAsync(updatedMapping);
        }
    }
}
```

### Преимущества технического решения

✅ **Простота** - Только C#/Blazor, никакого JavaScript
✅ **Точность** - Удаляется именно нужный маппинг
✅ **Валидация** - Корректная обработка замещения маппингов
✅ **🔄 UX-оптимизация** - Интуитивный обмен заголовков местами
✅ **Совместимость** - API компонента не изменился
✅ **Производительность** - Минимальные накладные расходы
✅ **Надежность** - Нет зависимостей от DOM-событий
✅ **SOLID** - Четкое разделение ответственности
✅ **Безопасность** - Невозможно создать конфликтующие маппинги
✅ **Гибкость** - Автоматическое определение типа операции (обмен/замещение)

### Ключевые концепции

- **Виртуальные экземпляры**: Каждая зона получает свои экземпляры заголовков
- **Логическое равенство**: Заголовки сравниваются только по имени (`Name`)
- **Отслеживание зон**: `ZoneId` точно указывает источник drag-операции
- **Автоматическое обновление**: Экземпляры пересоздаются при изменении маппинга

## Архитектура

Компонент следует принципам SOLID:
- **Single Responsibility**: Каждый метод отвечает за одну задачу
- **Open/Closed**: Легко расширяется через параметры и события
- **Liskov Substitution**: Использует абстракции MudBlazor
- **Interface Segregation**: Четкое разделение интерфейсов
- **Dependency Inversion**: Зависит от абстракций, а не от конкретных реализаций

## Стилизация

Компонент использует CSS Grid для адаптивной сетки:
- Desktop: 4 колонки
- Tablet: 2 колонки
- Mobile: 1 колонка

Поддерживает темы MudBlazor и кастомизацию через CSS переменные.

## Миграция и совместимость

### Адаптация существующих компонентов

Компонент полностью обратно совместим. Существующие компоненты (`CsvMapperComponent`, `ImportDialog`) были успешно адаптированы к новой модели данных:

#### Миграционный путь:
- `CsvExpectedFields` → `CsvHeaderMapper.ModelField`
- `CsvMapperHeader` → `CsvHeaderMapper.HeaderMapping`
- `ExpectedHeaders` параметр → `Fields` параметр
- Прямая работа с заголовками → Работа через маппинги

#### Пример адаптации ImportDialog:
```csharp
// Старая модель
private List<CsvExpectedFields> _expectedFields = [...];
private ImportMap GetImportMap(IEnumerable<CsvMapperHeader> csvFields) { ... }

// Новая модель
private readonly List<CsvHeaderMapper.ModelField> _expectedFields = [...];
private ImportMap GetImportMap(IEnumerable<CsvHeaderMapper.HeaderMapping> mappings) { ... }
```

### Обратная совместимость
- ✅ Внешний API компонентов остался неизменным
- ✅ Все функции импорта работают как прежде
- ✅ Минимальные изменения в пользовательском коде
- ✅ Улучшена точность удаления маппингов

## История изменений

### v2.2 - Улучшение пользовательского опыта (UX)
- 🔄 **НОВАЯ ФУНКЦИЯ**: Обмен заголовков местами при drag-and-drop
- ✅ Реализован метод `ShouldSwapHeaders()` для определения условий обмена
- ✅ Добавлен метод `HandleHeaderSwap()` для выполнения обмена
- ✅ Разделена логика на `HandleHeaderSwap()` и `HandleStandardFieldDrop()`
- ✅ Улучшен пользовательский опыт при работе с маппингами
- ✅ Сохранена обратная совместимость

### v2.1 - Исправление критической ошибки валидации
- 🔧 **КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ**: Запрет назначения нескольких заголовков на одно поле
- ✅ Добавлена корректная валидация в `CanDrop()`
- ✅ Реализовано замещение маппингов в `HandleFieldDrop()`
- ✅ Обновлена логика обработки конфликтующих маппингов
- ✅ Улучшена безопасность импорта данных

### v2.0 - Простое решение без JavaScript
- ✅ Убрана JavaScript-интеграция
- ✅ Реализованы виртуальные экземпляры заголовков
- ✅ Точное удаление маппингов через `ZoneId`
- ✅ Адаптированы существующие компоненты
- ✅ Улучшена производительность и надежность

### v1.0 - Первоначальная реализация
- ✅ Базовая функциональность drag-and-drop
- ✅ Поддержка дублирования заголовков
- ✅ JavaScript-интеграция для отслеживания источника drag-операций
