using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes;
using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.PlanNode;
using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.FridgeNode;
using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.CameraNode;
using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.RoomNode;

namespace Teslametrics.App.Web.Features.Main.Devices.Diagrams;

public partial class PlanComponent : IAsyncDisposable
{
    private Guid? _roomId;
    private Guid? _floorId;

    private GetFloorDiagramUseCase.Response? _response;
    private List<PlanElementBase> _elements = [];

    private readonly string _containerId = $"plan-container-{Guid.NewGuid()}";
    private readonly string _contentId = $"plan-content-{Guid.NewGuid()}";
    private IJSObjectReference? _jsModule;

    [Inject]
    private IJSRuntime JS { get; set; } = null!;

    #region [Parameters]
    [Parameter]
    public Guid? CityId { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }

    [Parameter]
    public Guid? FloorId { get; set; }

    [Parameter]
    public Guid? RoomId { get; set; }

    [Parameter]
    public EventCallback<Guid?> RoomIdChanged { get; set; }
    #endregion

    public async ValueTask DisposeAsync()
    {
        try
        {
            if (_jsModule != null)
            {
                await _jsModule.InvokeVoidAsync("dispose", _containerId);
                await _jsModule.DisposeAsync();
            }
        }
        catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
        {
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to dispose PlanComponent resources");
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (!CityId.HasValue || !BuildingId.HasValue || !FloorId.HasValue)
        {
            _elements = [];
            _response = null;
        }

        // Reload plan data if IDs have changed
        if (CityId.HasValue && BuildingId.HasValue && FloorId.HasValue && _floorId != FloorId)
        {
            _floorId = FloorId;
            await LoadPlanDataAsync();
            await ResetZoomAsync();
        }

        if (RoomId != _roomId)
        {
            _roomId = RoomId;
            if (RoomId is not null)
            {
                var room = _elements.OfType<RoomElement>().FirstOrDefault(r => r.Id == RoomId);
                await SelectRoomAsync(room);
            }
            else
            {
                await ResetZoomAsync();
            }
        }

        await base.OnParametersSetAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                _jsModule = await JS.InvokeAsync<IJSObjectReference>("import", "./Features/Main/Devices/Diagrams/PlanComponent.razor.js");
                if (_jsModule != null)
                {
                    await _jsModule.InvokeVoidAsync("init", _containerId, new { maxZoom = 5, minZoom = 0.2, padding = 30 });
                    if (RoomId is not null && RoomId != Guid.Empty)
                    {
                        var room = _elements.OfType<RoomElement>().FirstOrDefault(r => r.Id == RoomId);
                        await SelectRoomAsync(room);
                    }
                }
            }
            catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
            {
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Failed to initialize PlanComponent JavaScript module");
            }
        }
    }

    private async Task LoadPlanDataAsync()
    {
        if (!CityId.HasValue || !BuildingId.HasValue || !FloorId.HasValue) return;
        await SetLoadingAsync(true);
        try
        {
            _response = await ScopeFactory.MediatorSend(new GetFloorDiagramUseCase.Query(CityId!.Value, BuildingId!.Value, FloorId!.Value));
        }
        catch (Exception ex)
        {
            _response = null;
            Logger.LogError(ex, "Failed to load plan data for CityId: {CityId}, BuildingId: {BuildingId}, FloorId: {FloorId}", CityId, BuildingId, FloorId);
        }

        await SetLoadingAsync(false);

        switch (_response?.Result)
        {
            case GetFloorDiagramUseCase.Result.Success:
                _elements.Clear();

                if (_response.Floor is null) return;

                // Добавляем изображение плана, если оно есть
                if (_response.Floor.Plan != null)
                {
                    var imageElement = new ImageElement
                    {
                        X = _response.Floor.Plan.Position.X,
                        Y = _response.Floor.Plan.Position.Y,
                        Width = _response.Floor.Plan.Size.Width > 0 ? _response.Floor.Plan.Size.Width : 800,
                        Height = _response.Floor.Plan.Size.Height > 0 ? _response.Floor.Plan.Size.Height : 600,
                        Id = _response.Floor.Id,
                        ImageUrl = $"/devices/floor/{_response.Floor.Id}"
                    };
                    _elements.Add(imageElement);
                }

                // Добавляем комнаты как области
                foreach (var room in _response.Floor.Rooms)
                {
                    var areaPoints = room.ZonePoints.Select(p => new AreaPoint(p.X, p.Y)).ToList();

                    // Добавляем только комнаты с валидными точками зоны
                    if (areaPoints.Count >= 3)
                    {
                        var areaElement = new RoomElement
                        {
                            Id = room.Id,
                            X = room.Position.X,
                            Y = room.Position.Y,
                            Title = room.Name,
                            Points = areaPoints
                        };
                        _elements.Add(areaElement);
                    }

                    // Добавляем холодильники как специализированные объекты
                    foreach (var fridge in room.Fridges)
                    {
                        var fridgeElement = new FridgeElement(fridge.Name)
                        {
                            Id = fridge.Id,
                            X = fridge.Position.X,
                            Y = fridge.Position.Y,
                        };
                        _elements.Add(fridgeElement);
                    }

                    // Добавляем камеры как специализированные объекты
                    foreach (var camera in room.Cameras)
                    {
                        var cameraElement = new CameraElement(camera.Name)
                        {
                            Id = camera.Id,
                            X = camera.Position.X,
                            Y = camera.Position.Y
                        };
                        _elements.Add(cameraElement);
                    }
                }
                break;
            case GetFloorDiagramUseCase.Result.PlanNotFound:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(PlanComponent), nameof(GetFloorDiagramUseCase), _response.Result);
                Snackbar.Add($"Не удалось получить данные этажа из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
            case GetFloorDiagramUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(PlanComponent), nameof(GetFloorDiagramUseCase));
                Snackbar.Add($"Не удалось получить данные этажа из-за неизвестной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(PlanComponent), nameof(GetFloorDiagramUseCase), _response?.Result);
                Snackbar.Add($"Не удалось получить данные этажа из-за непредвиденной ошибки: {_response?.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task ResetZoomAsync()
    {
        try
        {
            // Reset zoom to initial state and unlock panning/zooming
            if (_jsModule != null)
            {
                await _jsModule.InvokeVoidAsync("reset", _containerId);
                await _jsModule.InvokeVoidAsync("unlock", _containerId);
            }
        }
        catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
        {
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to reset zoom for container: {ContainerId}", _containerId);
        }
    }

    private async Task SelectRoomAsync(RoomElement? room)
    {
        RoomId = room?.Id;
        if (RoomIdChanged.HasDelegate)
            await RoomIdChanged.InvokeAsync(RoomId);

        try
        {
            // Зумируем на выбранный элемент и блокируем перемещение/зум
            if (_jsModule != null)
            {
                await _jsModule.InvokeVoidAsync("zoomToElement", _containerId, RoomId);
                await _jsModule.InvokeVoidAsync("lock", _containerId);
            }
        }
        catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
        {
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to select room {RoomId} for container: {ContainerId}", RoomId, _containerId);
        }

    }

    /// <summary>
    /// Увеличивает масштаб плана
    /// </summary>
    public async Task ZoomInAsync()
    {
        try
        {
            if (_jsModule != null)
            {
                await _jsModule.InvokeVoidAsync("zoomIn", _containerId);
            }
        }
        catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
        {
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to zoom in for container: {ContainerId}", _containerId);
        }
    }

    /// <summary>
    /// Уменьшает масштаб плана
    /// </summary>
    public async Task ZoomOutAsync()
    {
        try
        {
            if (_jsModule != null)
            {
                await _jsModule.InvokeVoidAsync("zoomOut", _containerId);
            }
        }
        catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
        {
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to zoom out for container: {ContainerId}", _containerId);
        }
    }
}
