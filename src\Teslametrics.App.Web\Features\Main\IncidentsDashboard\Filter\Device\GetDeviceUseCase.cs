using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.Filter.Device;

public static class GetDeviceUseCase
{
    public record Query(Guid CityId, Guid BuildingId, Guid FloorId, Guid RoomId, Guid DeviceId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public string Name { get; set; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(string name)
        {
            Name = name;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Name = string.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        PlanNotFound,
        CityNotFound,
        BuildingNotFound,
        FloorNotFound,
        RoomNotFound,
        DeviceNotFound,
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.CityId).NotEmpty();
            RuleFor(q => q.BuildingId).NotEmpty();
            RuleFor(q => q.FloorId).NotEmpty();
            RuleFor(q => q.RoomId).NotEmpty();
            RuleFor(q => q.DeviceId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator, IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            if (!await CheckTableExistsAsync())
            {
                return new Response(Result.PlanNotFound);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Plans.Props.Page)
                .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

            var pageJson = await _dbConnection.ExecuteScalarAsync<string?>(template.RawSql);

            if (string.IsNullOrEmpty(pageJson))
            {
                return new Response(Result.PlanNotFound);
            }

            var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

            var city = page.Cities.FirstOrDefault(c => c.Id == request.CityId);

            if (city is null)
            {
                return new Response(Result.CityNotFound);
            }

            var building = city.Buildings.FirstOrDefault(b => b.Id == request.BuildingId);

            if (building is null)
            {
                return new Response(Result.BuildingNotFound);
            }

            var floor = building.Floors.FirstOrDefault(f => f.Id == request.FloorId);

            if (floor is null)
            {
                return new Response(Result.FloorNotFound);
            }

            var room = floor.Rooms.FirstOrDefault(r => r.Id == request.RoomId);

            if (room is null)
            {
                return new Response(Result.RoomNotFound);
            }

            var device = room.Fridges.FirstOrDefault(f => f.Id == request.DeviceId);

            if (device is null)
            {
                return new Response(Result.DeviceNotFound);
            }

            return new Response(device.Name);
        }

        private async Task<bool> CheckTableExistsAsync()
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = Db.Plans.Table });

            return tableExists > 0;
        }
    }
}