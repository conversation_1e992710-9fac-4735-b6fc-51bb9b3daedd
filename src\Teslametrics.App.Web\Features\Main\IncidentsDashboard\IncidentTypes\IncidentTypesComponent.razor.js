const CHART_DIV_ID = "incident-types-chart";
let currentChartData = null;

// ────────────────────────────────────────────────────────────────────────────
// 1. Загрузка Plotly (при необходимости)
// ────────────────────────────────────────────────────────────────────────────
if (typeof Plotly === "undefined") {
	const s = document.createElement("script");
	s.src = "/js/plotly-3.0.1.min.js";
	s.defer = true;
	document.head.appendChild(s);
}

// цвета
const rootStyles = getComputedStyle(document.documentElement);
const paperClr =
	rootStyles.getPropertyValue("--mud-palette-surface").trim() || "#e0e0e0";
const INCIDENT_COLORS = {
	0: "#2DB2B2",
	1: "#FE9725",
	2: "#FFD85C",
	3: "#33C7F8",
	4: "#B7F1FF",
	5: "#004854",
};
const DEFAULT_COLOR = "#CED3D3";

// ────────────────────────────────────────────────────────────────────────────
// 2. Публичная функция (adapter)
// ────────────────────────────────────────────────────────────────────────────
export async function renderIncidentTypes(rows) {
	const labels = rows.map((r) => r.name);
	const series = rows.map((r) => r.count);
	const colors = rows.map((r) => INCIDENT_COLORS[r.chartType] ?? DEFAULT_COLOR);

	await initIncidentTypesChart({ labels, series, colors });
}

// ────────────────────────────────────────────────────────────────────────────
// 3. Ожидание Plotly
// ────────────────────────────────────────────────────────────────────────────
function waitForPlotly() {
	return new Promise((resolve, reject) => {
		if (typeof Plotly !== "undefined") {
			resolve();
			return;
		}
		let tries = 0;
		(function check() {
			if (typeof Plotly !== "undefined") return resolve();
			if (++tries > 100) return reject(new Error("Plotly not loaded"));
			setTimeout(check, 100);
		})();
	});
}

// ────────────────────────────────────────────────────────────────────────────
// 4. Главное построение
// ────────────────────────────────────────────────────────────────────────────
export async function initIncidentTypesChart({ labels, series, colors }) {
	await waitForPlotly().catch((err) => {
		console.error(err);
		return;
	});

	/* ---------- 0. «Нет данных» ---------- */
	if (!series || series.length === 0) {
		Plotly.react(
			CHART_DIV_ID,
			[],
			{
				paper_bgcolor: "transparent",
				plot_bgcolor: "transparent",
				xaxis: { visible: false },
				yaxis: { visible: false },
				annotations: [
					{
						text: "Нет данных для отображения",
						xref: "paper",
						yref: "paper",
						x: 0.5,
						y: 0.5,
						showarrow: false,
						font: { size: 16, color: "#888" },
					},
				],
				margin: { t: 40, r: 10, b: 40, l: 48 },
			},
			{ displayModeBar: false }
		).then(applyCustomAnnotationStyles);
		return;
	}

	currentChartData = { labels, series, colors };
	const total = series.reduce((sum, v) => sum + v, 0);

	// 4.1 Domain / геометрия пончика (легенда слева → x‑range начинается с 0.3)
	const pieDomain = { x: [0.3, 0.95], y: [0.05, 0.95] };
	const domainW = pieDomain.x[1] - pieDomain.x[0];
	const domainH = pieDomain.y[1] - pieDomain.y[0];
	const cx = (pieDomain.x[0] + pieDomain.x[1]) / 2;
	const cy = (pieDomain.y[0] + pieDomain.y[1]) / 2;

	const trace = {
		type: "pie",
		labels,
		values: series,
		textinfo: "none",
		hole: 0.65,
		rotation: 90, // старт в 3 часа
		direction: "clockwise",
		sort: false, // ← сохраняем порядок элементов rows
		marker: { colors, line: { color: "#CED3D3", width: 1 } },
		domain: pieDomain,
		hovertemplate: "%{label}<br>%{value} (%{percent:.1%})<extra></extra>",
	};

	// 4.2 Средние углы с учётом rotation
	const startDeg = 90 - trace.rotation; // rotation CCW
	const angles = [];
	let curDeg = startDeg;
	for (let i = 0; i < series.length; i++) {
		const segDeg = (series[i] / total) * 360;
		angles.push(curDeg - segDeg / 2);
		curDeg -= segDeg; // clockwise
	}

	// 4.3 Аннотации — учитываем соотношение сторон контейнера
	const { width: pxW, height: pxH } = document
		.getElementById(CHART_DIV_ID)
		.getBoundingClientRect();
	const outerPx = Math.min(pxW * domainW, pxH * domainH) / 2;
	const innerPx = outerPx * trace.hole;
	const midPx = innerPx + (outerPx - innerPx) * 0.5; // середина кольца

	const radX = midPx / pxW; // в paper‑координатах радиусы по осям отличаются,
	const radY = midPx / pxH; // если контейнер не квадратный

	const annotations = series.map((val, idx) => {
		const pct = val / total;
		const ang = (angles[idx] * Math.PI) / 180;

		let x = cx + Math.cos(ang) * radX;
		if (isNaN(x)) x = 0;

		let y = cy + Math.sin(ang) * radY;
		if (isNaN(y)) y = 0;

		return {
			x: x,
			y: y,
			xref: "paper",
			yref: "paper",
			text: `${val}; ${(pct * 100).toFixed(0)}%`,
			showarrow: false,
			font: {
				family: "Inter, sans-serif",
				size: 11,
				color: "#474B4E",
				weight: 500,
			},
			bgcolor: "rgba(255,255,255,0.95)",
			bordercolor: "#E7E9EE",
			borderwidth: 1,
			borderpad: 6,
			xanchor: "center",
			yanchor: "middle",
		};
	});

	// 4.4 Layout
	const layout = {
		paper_bgcolor: paperClr,
		plot_bgcolor: "transparent",
		height: 300,
		margin: { t: 20, b: 20, l: 20, r: 20 },
		showlegend: true,
		font: { family: "Inter, sans-serif", size: 8 },
		legend: {
			orientation: "v",
			yanchor: "bottom",
			y: 0.05,
			xanchor: "left",
			x: 0,
			font: { family: "Inter, sans-serif", size: 12, color: "#474B4E" },
		},
		annotations,
	};

	// 4.5 Рендер
	Plotly.react(CHART_DIV_ID, [trace], layout, {
		displayModeBar: false,
		responsive: true,
	}).then(applyCustomAnnotationStyles);
}

// ────────────────────────────────────────────────────────────────────────────
// 5. Скруглённые углы + тень
// ────────────────────────────────────────────────────────────────────────────
function applyCustomAnnotationStyles() {
	const svg = document.getElementById(CHART_DIV_ID)?.querySelector(".main-svg");
	if (!svg) return;
	svg.querySelectorAll("g.annotation-text-g rect").forEach((r) => {
		r.setAttribute("rx", "8");
		r.setAttribute("ry", "8");
		r.style.filter = "drop-shadow(0 2px 4px rgba(0,0,0,0.1))";
	});
	svg.querySelectorAll("g.annotation-text-g text").forEach((t) => {
		t.style.fontWeight = "500";
		t.style.letterSpacing = "0.02em";
	});
}
