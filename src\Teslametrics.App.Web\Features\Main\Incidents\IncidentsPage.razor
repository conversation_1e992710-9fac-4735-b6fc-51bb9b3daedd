@using Teslametrics.App.Web.Features.Main.Incidents.IncidentsList
@using Teslametrics.App.Web.Features.Main.Incidents.Filter
@page "/incidents"
@attribute [Authorize]
<PageTitle>Multimonitor | Происшествия</PageTitle>
<div class="container">
	<FilterComponent @bind-IsResolved="IsResolved"
					 @bind-IncidentType="IncidentType"
					 @bind-CityId="@CityId"
					 @bind-BuildingId="BuildingId"
					 @bind-FloorId="FloorId"
					 @bind-RoomId="RoomId"
					 @bind-FridgeId="FridgeId"
					 @bind-DateFrom="DateFrom"
					 @bind-DateTo="DateTo" />

	<IncidentsListComponent IsResolved="IsResolved"
							IncidentType="IncidentType"
							DateFrom="DateFrom"
							DateTo="DateTo"
							CityId="@CityId"
							BuildingId="@BuildingId"
							FloorId="@FloorId"
							RoomId="@RoomId"
							FridgeId="@FridgeId"
							IncidentId="IncidentId"
							IncidentIdChanged="OnIncidendIdChanged" />
</div>
<Teslametrics.App.Web.Features.Main.Incidents.IncidentDialog.IncidentDialog IncidentId="IncidentId"
																			IncidentIdChanged="OnIncidendIdChanged" />