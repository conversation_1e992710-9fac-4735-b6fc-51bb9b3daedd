﻿@using Teslametrics.App.Web.Features.Main.Cameras.CameraSettingDialog.PublicAccessView
@using Teslametrics.App.Web.Features.Main.Cameras.CameraSettingDialog.Status
@using Teslametrics.App.Web.Features.Main.Cameras.CameraSettingDialog.Quota
@attribute [StreamRendering(true)]
@inherits InteractiveBaseComponent
<MudDialog @bind-Visible="_isVisible"
		   Class="CameraSettingsDialog"
		   ActionsClass="align-center justify-center"
		   ContentClass="content"
		   Options="_dialogOptions"
		   Style="height: 70vh;">
	<DialogContent>
		<div class="title d-flex">
			<MudStack Spacing="0">
				<MudText Typo="Typo.h6">Настройки камеры</MudText>
				@if (IsLoading)
				{
					<MudSkeleton Width="30%"
								 Height="42px" />
				}
				@if (!IsLoading && _camera is null)
				{
					<MudText Typo="Typo.body2">Камера не найдена</MudText>
				}
				@if (!IsLoading && _camera is not null)
				{
					<MudText Typo="Typo.body2">@_camera?.Name</MudText>
				}
			</MudStack>
			<MudSpacer />
			<div>
				@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
				{
					<MudTooltip Arrow="true"
								Placement="Placement.Start"
								Text="Ошибка подписки на события">
						<MudIconButton OnClick="SubscribeAsync"
									   Icon="@Icons.Material.Filled.ErrorOutline"
									   Color="Color.Error" />
					</MudTooltip>
					<MudIconButton OnClick="RefreshAsync"
								   Icon="@Icons.Material.Filled.Refresh"
								   Color="Color.Primary" />
				}
				<MudIconButton OnClick="CancelAsync"
							   Icon="@Icons.Material.Outlined.Close" />
			</div>
		</div>
		@if (IsLoading)
		{
			<MudSkeleton Width="30%" />
			<MudSkeleton Width="30%" />
			<MudSkeleton Width="30%" />
			<MudSkeleton Width="30%" />
			<MudSkeleton Width="30%" />
		}
		@if (!IsLoading && _camera is null)
		{
			<MudStack AlignItems="AlignItems.Center"
					  Justify="Justify.Center"
					  Spacing="0"
					  Class="mud-height-full">
				<MudIcon Icon="@Icons.Material.Outlined.WarningAmber"
						 Color="Color.Warning"
						 Style="font-size: 8rem;"
						 Class="mb-2" />
				<MudText Typo="Typo.subtitle1"
						 Color="Color.Warning">Камера не найдена</MudText>
				<MudText Typo="Typo.body2">Не удалось получить даные о камеры.</MudText>
				<MudButton OnClick="RefreshAsync">Повторить попытку</MudButton>
			</MudStack>
		}

		@if (_camera is not null && _camera.IsSuccess && _model is not null)
		{
			<MudForm Model="_model"
					 Validation="_validator.ValidateValue"
					 Class="form mx-n4 px-4"
					 Spacing="5"
					 OverrideFieldValidation="true"
					 UserAttributes="@(new Dictionary<string, object>() { { "autocomplete", "off" }, { "aria-autocomplete", "none" }, { "role", "presentation" } })">
				<MudStack Spacing="3">
					<MudText Typo="Typo.subtitle2">Описание камеры</MudText>

					<MudText Typo="Typo.caption">Имя камеры</MudText>
					<MudTextField @bind-Value="_model.Name"
								  For="() => _model.Name"
								  Clearable="true"
								  Immediate="true"
								  Placeholder="Наименование"
								  RequiredError="Данное поле обязательно"
								  Required="true"
								  Variant="Variant.Outlined"
								  Margin="Margin.Dense" />
				</MudStack>

				<MudStack Spacing="3">
					<MudText Typo="Typo.subtitle2">Параметры камеры</MudText>
					@if (!_env.IsProduction())
					{
						<MudText Typo="Typo.caption">ID камеры</MudText>
						<MudTextField Value="@_model.Id.ToString("N")"
									  ReadOnly="true"
									  Disabled="true"
									  Variant="Variant.Outlined"
									  Margin="Margin.Dense" />
					}

					<MudText Typo="Typo.caption">Наименование квоты</MudText>
					<QuotaFieldComponent @bind-Selected="@_model.Quota"
										 OrganizationId="@_organizationId!.Value"
										 Variant="Variant.Outlined"
										 Margin="Margin.Dense" />

					<MudText Typo="Typo.caption">Ссылка на архивный поток</MudText>
					<MudTextField T="string"
								  Value="_model.ArchiveUri"
								  ValueChanged="OnArchiveUriChanged"
								  For="() => _model.ArchiveUri"
								  InputType="InputType.Text"
								  Immediate="true"
								  Variant="Variant.Outlined"
								  Margin="Margin.Dense"
								  @ref="@_archiveUriRef" />

					<MudText Typo="Typo.caption">Ссылка на поток для видов</MudText>
					<MudTextField T="string"
								  Value="_model.ViewUri"
								  ValueChanged="OnViewUriChanged"
								  For="() => _model.ViewUri"
								  InputType="InputType.Text"
								  Immediate="true"
								  Variant="Variant.Outlined"
								  Margin="Margin.Dense"
								  @ref="@_viewUriRef" />

					<MudText Typo="Typo.caption">Ссылка на публичный поток</MudText>
					<MudTextField T="string"
								  Value="_model.PublicUri"
								  ValueChanged="OnPublicUriChanged"
								  For="() => _model.PublicUri"
								  InputType="InputType.Text"
								  Immediate="true"
								  Variant="Variant.Outlined"
								  Margin="Margin.Dense"
								  @ref="@_publicUriRef" />

					<MudCheckBox @bind-Value="_model.AutoStart"
								 Label="Автозапуск при перезапуске системы"
								 Color="Color.Primary"
								 Class="ml-n3" />

					<MudCheckBox T="bool"
								 @bind-Value="_model.OnvifEnabled"
								 Color="Color.Primary"
								 Class="ml-n3"
								 Label="Включить ONVIF" />
				</MudStack>

				@if (_model.OnvifEnabled)
				{
					<MudStack Spacing="3">
						<MudText Typo="Typo.subtitle2">Подключение к ONVIF</MudText>
						<MudText Typo="Typo.caption">Логин</MudText>
						<MudTextField T="string"
									  @bind-Value="_model.Onvif!.Username"
									  For="() => _model.Onvif!.Username"
									  Immediate="true"
									  Variant="Variant.Outlined"
									  Margin="Margin.Dense" />

						<MudText Typo="Typo.caption">Пароль</MudText>
						<PasswordFieldComponent @bind-Value="_model.Onvif!.Password"
												For="() => _model.Onvif!.Password"
												Immediate="true"
												Variant="Variant.Outlined"
												Margin="Margin.Dense" />

						<MudText Typo="Typo.caption">Адрес ONVIF</MudText>
						<MudTextField T="string"
									  @bind-Value="_model.Onvif!.Host"
									  For="() => _model.Onvif!.Host"
									  Immediate="true"
									  Variant="Variant.Outlined"
									  Margin="Margin.Dense" />

						<MudText Typo="Typo.caption">Порт</MudText>
						<MudNumericField T="int"
										 @bind-Value="_model.Onvif!.Port"
										 For="() => _model.Onvif!.Port"
										 Immediate="true"
										 Variant="Variant.Outlined"
										 Margin="Margin.Dense" />
					</MudStack>
				}

				<MudStack Spacing="3">
					<MudText Typo="Typo.subtitle2">Статус камеры</MudText>
					<MudStack Spacing="8"
							  Row="true">
						<CameraStatusComponent CameraId="@_id"
											   OrganizationId="@_organizationId!.Value"
											   CameraStatus="@_cameraStatus"
											   IsBlocked="@_camera!.IsBlocked" />
					</MudStack>
				</MudStack>

				<MudStack Spacing="3">
					<MudText Typo="Typo.subtitle2">Публичный доступ к камере</MudText>
					<CameraPublicAccessListComponent CameraId="@_id" />
				</MudStack>

				<MudStack Spacing="3">
					<MudText Typo="Typo.subtitle2">Местоположение камеры</MudText>
					<MudText Typo="Typo.caption">Часовой пояс</MudText>
					<TimeZoneSelector @bind-TimeZone="@_model.TimeZone"
									  Variant="Variant.Outlined"
									  Margin="Margin.Dense" />

					<YandexMaps @bind-Coordinates="@_model.Coordinates"
								Width="100%"
								ReadOnly="false"
								For="() => _model.Coordinates"
								Class="rounded-b overflow-hidden"
								Height="400px" />
				</MudStack>
			</MudForm>
		}
		<FormLoadingComponent IsLoading="IsLoading && _camera is null" />
	</DialogContent>
	<DialogActions>
		<MudButton OnClick="CancelAsync"
				   Variant="Variant.Outlined">Отмена</MudButton>
		@if (IsLoading)
		{
			<MudSkeleton Width="160px"
						 Height="52px" />
		}
		@if (!IsLoading && _camera is not null)
		{
			<MudButton OnClick="SubmitAsync"
					   Color="Color.Primary"
					   Disabled="@(!_isValid)"
					   Variant="Variant.Filled">Сохранить</MudButton>
		}
	</DialogActions>
</MudDialog>

<style>
	.CameraSettingsDialog .content {
		display: grid;
		grid-template-rows: auto 1fr;
		overflow: hidden;
	}

	.CameraSettingsDialog form {
		overflow: auto;
	}
</style>
