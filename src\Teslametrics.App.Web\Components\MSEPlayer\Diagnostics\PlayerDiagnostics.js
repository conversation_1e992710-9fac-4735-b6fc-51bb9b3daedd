/**
 * Диагностический модуль для MSE видеоплеера
 * Предоставляет инструменты для мониторинга и диагностики проблем
 */

export class PlayerDiagnostics {
    constructor(playerCore) {
        this.core = playerCore;
        this.isEnabled = false;
        this.diagnosticData = {
            startTime: new Date(),
            segmentHistory: [],
            errorHistory: [],
            stateHistory: [],
            performanceMetrics: {
                avgSegmentSize: 0,
                avgSegmentInterval: 0,
                totalSegments: 0,
                totalErrors: 0
            }
        };

        this.maxHistorySize = 100; // Максимальное количество записей в истории
        this.lastSegmentTime = null;
    }

    /**
     * Включает диагностику
     */
    enable() {
        if (this.isEnabled) return;
        
        this.isEnabled = true;
        console.log("[PlayerDiagnostics] Диагностика включена");

        // Подписываемся на события плеера
        this.core.bus.on("*", (eventType, data) => {
            this.handleEvent(eventType, data);
        });

        // Подписываемся на события MediaPipeline
        this.core.pipe.ev.on("*", (eventType, data) => {
            this.handlePipelineEvent(eventType, data);
        });

        // Подписываемся на события SignalR
        this.core.feed.ev.on("*", (eventType, data) => {
            this.handleFeedEvent(eventType, data);
        });

        // Запускаем периодический сбор метрик
        this.metricsInterval = setInterval(() => {
            this.collectMetrics();
        }, 5000);
    }

    /**
     * Отключает диагностику
     */
    disable() {
        if (!this.isEnabled) return;
        
        this.isEnabled = false;
        console.log("[PlayerDiagnostics] Диагностика отключена");

        if (this.metricsInterval) {
            clearInterval(this.metricsInterval);
            this.metricsInterval = null;
        }
    }

    /**
     * Обрабатывает события плеера
     */
    handleEvent(eventType, data) {
        if (!this.isEnabled) return;

        const timestamp = new Date();
        
        if (eventType === "error") {
            this.recordError(timestamp, "PlayerCore", data);
        }

        this.recordState(timestamp, "PlayerCore", eventType, data);
    }

    /**
     * Обрабатывает события MediaPipeline
     */
    handlePipelineEvent(eventType, data) {
        if (!this.isEnabled) return;

        const timestamp = new Date();

        if (eventType === "error") {
            this.recordError(timestamp, "MediaPipeline", data);
        } else if (eventType === "buffer") {
            this.recordSegment(timestamp, data);
        } else if (eventType === "health-warning") {
            this.recordError(timestamp, "MediaPipeline", data, "warning");
        }

        this.recordState(timestamp, "MediaPipeline", eventType, data);
    }

    /**
     * Обрабатывает события SignalR
     */
    handleFeedEvent(eventType, data) {
        if (!this.isEnabled) return;

        const timestamp = new Date();

        if (eventType === "error") {
            this.recordError(timestamp, "SignalRFeed", data);
        } else if (eventType === "chunk") {
            this.recordSegmentReceived(timestamp, data);
        }

        this.recordState(timestamp, "SignalRFeed", eventType, data);
    }

    /**
     * Записывает информацию о полученном сегменте
     */
    recordSegmentReceived(timestamp, data) {
        const segmentInfo = {
            timestamp,
            size: data?.data?.byteLength || 0,
            hasTimestamp: !!data?.ts,
            absoluteTime: data?.ts?.toISOString() || null
        };

        // Вычисляем интервал между сегментами
        if (this.lastSegmentTime) {
            segmentInfo.interval = timestamp - this.lastSegmentTime;
        }
        this.lastSegmentTime = timestamp;

        this.addToHistory(this.diagnosticData.segmentHistory, segmentInfo);
        this.diagnosticData.performanceMetrics.totalSegments++;
    }

    /**
     * Записывает информацию о буфере
     */
    recordSegment(timestamp, bufferData) {
        const segmentInfo = {
            timestamp,
            bufferStart: bufferData?.start || 0,
            bufferEnd: bufferData?.end || 0,
            bufferSize: (bufferData?.end || 0) - (bufferData?.start || 0)
        };

        this.addToHistory(this.diagnosticData.segmentHistory, segmentInfo);
    }

    /**
     * Записывает ошибку
     */
    recordError(timestamp, source, error, severity = "error") {
        const errorInfo = {
            timestamp,
            source,
            severity,
            message: error?.message || error?.toString() || "Unknown error",
            type: error?.name || error?.type || "Unknown",
            stack: error?.stack || null,
            data: error
        };

        this.addToHistory(this.diagnosticData.errorHistory, errorInfo);
        this.diagnosticData.performanceMetrics.totalErrors++;

        console.error(`[PlayerDiagnostics] ${severity.toUpperCase()} from ${source}:`, errorInfo);
    }

    /**
     * Записывает изменение состояния
     */
    recordState(timestamp, source, eventType, data) {
        const stateInfo = {
            timestamp,
            source,
            eventType,
            data: this.sanitizeData(data)
        };

        this.addToHistory(this.diagnosticData.stateHistory, stateInfo);
    }

    /**
     * Собирает метрики производительности
     */
    collectMetrics() {
        if (!this.isEnabled) return;

        const segments = this.diagnosticData.segmentHistory.slice(-20); // Последние 20 сегментов
        
        if (segments.length > 0) {
            // Средний размер сегмента
            const totalSize = segments.reduce((sum, seg) => sum + (seg.size || 0), 0);
            this.diagnosticData.performanceMetrics.avgSegmentSize = totalSize / segments.length;

            // Средний интервал между сегментами
            const intervals = segments.filter(seg => seg.interval).map(seg => seg.interval);
            if (intervals.length > 0) {
                const totalInterval = intervals.reduce((sum, interval) => sum + interval, 0);
                this.diagnosticData.performanceMetrics.avgSegmentInterval = totalInterval / intervals.length;
            }
        }

        console.log("[PlayerDiagnostics] Metrics:", this.diagnosticData.performanceMetrics);
    }

    /**
     * Добавляет запись в историю с ограничением размера
     */
    addToHistory(historyArray, item) {
        historyArray.push(item);
        if (historyArray.length > this.maxHistorySize) {
            historyArray.shift();
        }
    }

    /**
     * Очищает чувствительные данные для логирования
     */
    sanitizeData(data) {
        if (data && typeof data === 'object') {
            // Удаляем большие бинарные данные
            const sanitized = { ...data };
            if (sanitized.data instanceof Uint8Array) {
                sanitized.data = `[Uint8Array: ${sanitized.data.byteLength} bytes]`;
            }
            return sanitized;
        }
        return data;
    }

    /**
     * Возвращает полный отчет о диагностике
     */
    getReport() {
        return {
            ...this.diagnosticData,
            uptime: new Date() - this.diagnosticData.startTime,
            isEnabled: this.isEnabled
        };
    }

    /**
     * Возвращает краткий отчет о состоянии
     */
    getHealthSummary() {
        const now = new Date();
        const recentErrors = this.diagnosticData.errorHistory.filter(
            error => now - error.timestamp < 60000 // Последние 60 секунд
        );
        
        const recentSegments = this.diagnosticData.segmentHistory.filter(
            segment => now - segment.timestamp < 30000 // Последние 30 секунд
        );

        return {
            status: recentErrors.length === 0 ? "healthy" : "warning",
            recentErrorCount: recentErrors.length,
            recentSegmentCount: recentSegments.length,
            avgSegmentSize: this.diagnosticData.performanceMetrics.avgSegmentSize,
            avgSegmentInterval: this.diagnosticData.performanceMetrics.avgSegmentInterval,
            totalSegments: this.diagnosticData.performanceMetrics.totalSegments,
            totalErrors: this.diagnosticData.performanceMetrics.totalErrors,
            uptime: now - this.diagnosticData.startTime
        };
    }

    /**
     * Экспортирует диагностические данные в JSON
     */
    exportData() {
        return JSON.stringify(this.getReport(), null, 2);
    }
}

// Глобальная функция для включения диагностики на любом плеере
window.enablePlayerDiagnostics = function(cameraId) {
    const players = window.players || new Map();
    const player = players.get(cameraId);
    
    if (!player) {
        console.error("Player not found for camera:", cameraId);
        return null;
    }

    if (!player.diagnostics) {
        player.diagnostics = new PlayerDiagnostics(player);
    }
    
    player.diagnostics.enable();
    console.log("Diagnostics enabled for camera:", cameraId);
    return player.diagnostics;
};

window.getPlayerDiagnostics = function(cameraId) {
    const players = window.players || new Map();
    const player = players.get(cameraId);
    return player?.diagnostics || null;
};
