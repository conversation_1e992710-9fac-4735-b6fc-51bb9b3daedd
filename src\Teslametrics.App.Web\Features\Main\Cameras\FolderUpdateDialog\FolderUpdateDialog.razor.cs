using FluentValidation;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.App.Web.Services.UserDevice;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.FolderUpdateDialog;

public partial class FolderUpdateDialog
{
    #region [Types]
    private class FolderUpdateModel(Guid organizationId, Guid id, string name)
    {
        public Guid OrganizationId { get; init; } = organizationId;
        public Guid Id { get; init; } = id;
        public string Name { get; set; } = name;
    }
    private class Validator : BaseFluentValidator<FolderUpdateModel>
    {
        public Validator()
        {
            RuleFor(model => model.Name)
                .NotEmpty()
                .WithMessage("Название является обязательным")
                .Length(3, 120)
                .WithMessage("наименование должно быть длиной от 3 до 120 символов");
        }
    }
    #endregion

    #region [Dialog]
    private MudBlazor.DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MudBlazor.MaxWidth.Small, NoHeader = true, BackdropClick = false };
    private bool _isVisible;

    [Inject]
    private IUserDeviceService _userDeviceService { get; set; } = null!;
    #endregion

    #region [Form]
    private static readonly Validator _validator = new();
    private bool _isValid;
    private FolderUpdateModel? _model;
    #endregion

    protected override void OnInitialized()
    {
        CompositeDisposable.Add(EventSystem.Subscribe<FolderUpdateEto>(OnUpdateHandler));

        if (_userDeviceService.IsMobile)
        {
            _dialogOptions = new MudBlazor.DialogOptions
            {
                CloseOnEscapeKey = true,
                FullScreen = true,
                NoHeader = true,
                BackdropClick = false
            };
        }

        base.OnInitialized();
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            UnsubscribeFromAuthStateChanged();
        }

        base.Dispose(disposing);
    }

    private async Task SubmitAsync()
    {
        if (!_isValid || _model is null) return;

        await SetLoadingAsync(true);

        UpdateFolderUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new UpdateFolderUseCase.Command(_model.Id, _model.Name));
        }
        catch (Exception exc)
        {
            Snackbar.Add("Не удалось обновить наименование директории. Повторите попытку", MudBlazor.Severity.Error);
            Logger.LogError(exc, exc.Message);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case UpdateFolderUseCase.Result.Success:
                Snackbar.Add("Имя директории изменено", MudBlazor.Severity.Success);
                Cancel();
                break;
            case UpdateFolderUseCase.Result.FolderNameAlreadyExists:
                Snackbar.Add("Директория с данным именем уже существует.", MudBlazor.Severity.Error);
                // if (_inputRef is not null)
                // {
                //     var parameters = new Dictionary<string, object?>
                //     {
                //         { "ErrorText", "Директория с данным именем уже существует." },
                //         { "Error", true }
                //     };
                //     await _inputRef.SetParametersAsync(ParameterView.FromDictionary(parameters));
                // }
                break;
            case UpdateFolderUseCase.Result.FolderNotFound:
                Snackbar.Add("Директория, которой вы хотите изменить наименование не найдна.", MudBlazor.Severity.Error);
                // if (_inputRef is not null)
                // {
                //     var parameters = new Dictionary<string, object?>
                //     {
                //         { "ErrorText", "Директория с данным именем уже существует." },
                //         { "Error", true }
                //     };
                //     await _inputRef.SetParametersAsync(ParameterView.FromDictionary(parameters));
                // }
                break;
            case UpdateFolderUseCase.Result.ValidationError:
                Snackbar.Add("Не удалось изменить имя директории. Проверьте правильность заполнения.", MudBlazor.Severity.Error);
                // if (_inputRef is not null)
                // {
                //     var parameters = new Dictionary<string, object?>
                //     {
                //         { "ErrorText", "Директория с данным именем уже существует." },
                //         { "Error", true }
                //     };
                //     await _inputRef.SetParametersAsync(ParameterView.FromDictionary(parameters));
                // }
                break;
            case UpdateFolderUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FolderUpdateDialog), nameof(UpdateFolderUseCase));
                Snackbar.Add($"Не удалось изменить имя директории из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(FolderUpdateDialog), nameof(UpdateFolderUseCase), response.Result);
                Snackbar.Add($"Не удалось изменить имя директории из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private void Cancel()
    {
        _isVisible = false;
        _model = null;
        UnsubscribeFromAuthStateChanged();
    }

    #region [Event Handlers]
    private async Task OnUpdateHandler(FolderUpdateEto eto)
    {
        bool isAcessGranted = await CheckIfPermissionGranted(eto.OrganizationId, eto.DirectoryId);
        if (!isAcessGranted)
        {
            Snackbar.Add("Недостаточно прав для редактирования директории", MudBlazor.Severity.Warning);
            return;
        }

        GetFolderUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetFolderUseCase.Query(eto.DirectoryId));

        }
        catch (Exception ex)
        {
            Snackbar.Add("Не удалось получить директорию для редактирования", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
            return;
        }

        if (response == null) return;

        switch (response.Result)
        {
            case GetFolderUseCase.Result.Success:
                SubscribeToAuthStateChanged();
                _isValid = true;
                _model = new(eto.OrganizationId, eto.DirectoryId, response.Name);
                _isVisible = true;
                await UpdateViewAsync();
                break;
            case GetFolderUseCase.Result.ValidationError:
                Snackbar.Add("Не удалось получить директорию для редактирования из-за ошибки валидации", MudBlazor.Severity.Error);
                return;
            case GetFolderUseCase.Result.FolderNotFound:
                Snackbar.Add("Директория не найдена", MudBlazor.Severity.Error);
                return;
            case GetFolderUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FolderUpdateDialog), nameof(GetFolderUseCase));
                Snackbar.Add($"Не удалось получить директорию для редактирования из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                return;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(FolderUpdateDialog), nameof(GetFolderUseCase), response.Result);
                Snackbar.Add($"Не удалось получить директорию для редактирования из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                return;
        }
    }

    private async void OnAuthenticationStateChanged(Task<AuthenticationState> authState)
    {
        if (_model is null)
        {
            Cancel();
            return;
        }

        bool isAcessGranted = await CheckIfPermissionGranted(authState, _model.OrganizationId, _model.Id);
        if (!isAcessGranted)
        {
            Snackbar.Add("Недостаточно прав для редактирования директории", MudBlazor.Severity.Warning);
            Cancel();
            return;
        }
    }
    #endregion

    #region [Helpers]
    private Task<bool> CheckIfPermissionGranted(Guid organizationId, Guid folderId)
    {
        var userAuthState = AuthenticationStateProvider.GetAuthenticationStateAsync();

        return CheckIfPermissionGranted(userAuthState, organizationId, folderId);
    }

    private async Task<bool> CheckIfPermissionGranted(Task<AuthenticationState> authState, Guid organizationId, Guid folderId)
    {
        var userAuthState = await authState;
        var policyRequirementResource = new PolicyRequirementResource(organizationId, folderId);
        var authorizationResult = await AuthorizationService.AuthorizeAsync(
            userAuthState.User,  // Current user from AuthenticationStateProvider
            policyRequirementResource, // The resource being authorized
            AppPermissions.Main.Folders.Update.GetEnumPermissionString() // The policy name
        );

        return authorizationResult.Succeeded;
    }

    private void SubscribeToAuthStateChanged()
    {
        AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
    }

    private void UnsubscribeFromAuthStateChanged()
    {
        AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
    }
    #endregion
}
