# VideoTimeline Component

Компонент временной шкалы для видеоплеера MSE, который отображает текущий просматриваемый временной промежуток в пределах 3-часового окна.

## Описание

`VideoTimeline` - это Blazor компонент, предназначенный для работы с архивными видеозаписями в MSE плеере. Он показывает:

- Временную шкалу с диапазоном 3 часа (настраивается)
- Текущую позицию воспроизведения на шкале
- Абсолютное реальное время записи (timestamp)
- Временные метки каждые 15 минут
- Визуальный индикатор текущей позиции с анимацией

## Основные возможности

1. **Отображение временного окна**: Показывает 3-часовое окно времени с временными метками
2. **Текущая позиция**: Красный индикатор показывает текущую позицию воспроизведения
3. **Абсолютное время**: Использует реальное время записи, а не относительное время плеера
4. **Автообновление**: Обновляется в реальном времени (по умолчанию каждые 250мс)
5. **Автоматическое окно**: Автоматически сдвигает временное окно при выходе за границы
6. **Адаптивный дизайн**: Поддерживает мобильные устройства

## Использование

### Базовое использование

```razor
@using Teslametrics.App.Web.Components.MSEPlayer

<MsePlayer @ref="msePlayer"
           CameraId="@cameraId"
           Type="StreamType.View">
    <ToolbarContent>
        <PlayToggleButton Player="context" />
        <!-- другие кнопки управления -->
    </ToolbarContent>
</MsePlayer>

@if (msePlayer != null)
{
    <VideoTimeline Player="msePlayer" />
}
```

### Расширенное использование с параметрами

```razor
<VideoTimeline Player="msePlayer"
               UpdateInterval="250"
               WindowSizeHours="3"
               CurrentTimeChanged="OnCurrentTimeChanged" />

@code {
    private void OnCurrentTimeChanged(object? sender, DateTime? currentTime)
    {
        // Обработка изменения текущего времени
        Console.WriteLine($"Текущее время: {currentTime}");
    }
}
```

## Параметры

| Параметр | Тип | Обязательный | По умолчанию | Описание |
|----------|-----|--------------|--------------|----------|
| `Player` | `IMsePlayer` | Да | - | Ссылка на MSE плеер |
| `UpdateInterval` | `int` | Нет | 250 | Интервал обновления в миллисекундах |
| `WindowSizeHours` | `int` | Нет | 3 | Размер временного окна в часах |

## События

| Событие | Тип | Описание |
|---------|-----|----------|
| `CurrentTimeChanged` | `EventHandler<DateTime?>` | Срабатывает при изменении текущего времени |

## Свойства (IVideoTimeline)

| Свойство | Тип | Описание |
|----------|-----|----------|
| `CurrentTime` | `DateTime?` | Текущее абсолютное время воспроизведения |
| `WindowStart` | `DateTime?` | Начало временного окна |
| `WindowEnd` | `DateTime?` | Конец временного окна |
| `CurrentPositionPercent` | `double` | Позиция в процентах (0-100) |

## Методы

| Метод | Описание |
|-------|----------|
| `UpdateCurrentTimeAsync(DateTime?)` | Обновляет текущее время |
| `SetTimeWindowAsync(DateTime, DateTime)` | Устанавливает временное окно |

## Стилизация

Компонент использует CSS-переменные и классы для стилизации:

- `.video-timeline` - основной контейнер
- `.timeline-track` - дорожка временной шкалы
- `.timeline-position` - индикатор текущей позиции
- `.position-indicator` - визуальный маркер с анимацией
- `.timeline-mark` - временные метки

### Кастомизация стилей

```css
.video-timeline {
    --timeline-bg: #2a2a2a;
    --timeline-track-bg: linear-gradient(to right, #1a1a1a, #333333, #1a1a1a);
    --position-color: #ff4444;
    --mark-color: #666;
}
```

## Требования

- .NET 8.0+
- Blazor Server
- MudBlazor
- MSE Player компонент

## Примеры

### Демонстрационная страница

Полный пример использования доступен в:
- `MsePlayerCameraDemo.razor` - базовая демонстрация
- `MsePlayerTimelineDemo.razor` - расширенная демонстрация с дополнительной информацией

### Интеграция в существующий проект

```razor
@page "/camera/{CameraId}"
@using Teslametrics.App.Web.Components.MSEPlayer

<div class="camera-viewer">
    <MsePlayer @ref="player"
               CameraId="@Guid.Parse(CameraId)"
               Type="StreamType.Archive">
        <ToolbarContent>
            <PlayToggleButton Player="context" />
            <VolumeComponent Player="context" />
            <SeekToLiveButton Player="context" />
            <FullscreenToggleButton Player="context" />
        </ToolbarContent>
    </MsePlayer>
    
    @if (player != null)
    {
        <VideoTimeline Player="player"
                       UpdateInterval="200"
                       WindowSizeHours="6" />
    }
</div>

@code {
    [Parameter] public string CameraId { get; set; } = string.Empty;
    private MsePlayer? player;
}
```

## Производительность

- Компонент оптимизирован для частых обновлений
- Использует `Timer` для регулярного обновления времени
- Автоматически освобождает ресурсы при уничтожении
- Минимальное влияние на производительность плеера

## Ограничения

- Работает только с MSE плеером
- Требует поддержки абсолютного времени в плеере
- Оптимизирован для архивных записей (не для live-потоков)
- Временное окно фиксированного размера (настраивается только размер)

## Поддержка браузеров

Компонент поддерживает все современные браузеры, поддерживающие:
- Media Source Extensions (MSE)
- CSS Grid и Flexbox
- ES6+ JavaScript
