using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.IncidentsCount;

public partial class IncidentsCountComponent : IAsyncDisposable
{
    private IJSObjectReference? _jsModule;

    private GetIncidentsCountUseCase.Response? _response = null;

    [Inject]
    private IJSRuntime JSRuntime { get; set; } = null!;
    [Parameter]
    public DateTime DateFrom { get; set; } = DateTime.Today;

    [Parameter]
    public DateTime DateTo { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);
    [Parameter]
    public Guid? CityId { get; set; }
    [Parameter]
    public Guid? BuildingId { get; set; }
    [Parameter]
    public Guid? FloorId { get; set; }
    [Parameter]
    public Guid? RoomId { get; set; }
    [Parameter]
    public Guid? FridgeId { get; set; }

    protected override bool ShouldRender() => false;

    protected override async Task OnParametersSetAsync()
    {
        await LoadDataAsync();
        await BuildChartsAsync();
        await base.OnParametersSetAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            try
            {
                _jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./Features/Main/IncidentsDashboard/IncidentsCount/IncidentsCountComponent.razor.js");
                await BuildChartsAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, ex.Message);
                Snackbar.Add("Не удалось загрузить скрипт для построения графика", MudBlazor.Severity.Error);
            }
        }
    }

    private async Task BuildChartsAsync()
    {
        if (_jsModule == null) return;

        try
        {
            var processedIncidents = PreprocessIncidentsData(_response?.Incidents);
            await _jsModule.InvokeVoidAsync("initIncidentsChart", processedIncidents, "dd.MM");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
            Snackbar.Add("Не удалось построить график на полученных данных", MudBlazor.Severity.Error);
        }
    }

    /// <summary>
    /// Заполняет пропущенные даты нулевыми значениями для обеспечения непрерывной временной шкалы на графике
    /// </summary>
    /// <param name="incidents">Исходные данные об инцидентах</param>
    /// <returns>Обработанный список инцидентов с заполненными пропусками</returns>
    private List<GetIncidentsCountUseCase.Response.Incident> PreprocessIncidentsData(List<GetIncidentsCountUseCase.Response.Incident>? incidents)
    {
        if (incidents == null || incidents.Count == 0)
        {
            // Если нет данных, создаем пустой список с нулевыми значениями для всего диапазона дат
            return FillMissingDatesWithZeros([], DateFrom.Date, DateTo.Date);
        }

        return FillMissingDatesWithZeros(incidents, DateFrom.Date, DateTo.Date);
    }

    /// <summary>
    /// Заполняет пропущенные даты в указанном диапазоне нулевыми значениями
    /// </summary>
    /// <param name="incidents">Исходные данные об инцидентах</param>
    /// <param name="startDate">Начальная дата диапазона</param>
    /// <param name="endDate">Конечная дата диапазона</param>
    /// <returns>Список инцидентов с заполненными пропусками</returns>
    private static List<GetIncidentsCountUseCase.Response.Incident> FillMissingDatesWithZeros(
        List<GetIncidentsCountUseCase.Response.Incident> incidents,
        DateTime startDate,
        DateTime endDate)
    {
        // Создаем словарь для быстрого поиска существующих данных по дате
        var incidentsByDate = incidents.ToDictionary(i => i.Date.Date, i => i.Count);

        var result = new List<GetIncidentsCountUseCase.Response.Incident>();

        // Проходим по всем дням в диапазоне
        for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
        {
            var count = incidentsByDate.TryGetValue(date, out var existingCount) ? existingCount : 0;
            result.Add(new GetIncidentsCountUseCase.Response.Incident(date, count));
        }

        return result;
    }

    // Mock data methods - these would be replaced with actual API calls
    private async Task LoadDataAsync()
    {
        await SetLoadingAsync(true);
        try
        {
            _response = await ScopeFactory.MediatorSend(new GetIncidentsCountUseCase.Query(DateFrom, DateTo, CityId, BuildingId, FloorId, RoomId, FridgeId));
        }
        catch (Exception ex)
        {
            _response = null;
            Snackbar.Add("Ошибка при получении количества происшествий во время запроса к серверу. Обратитесь к администратору.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetLoadingAsync(false);
        if (_response is null) return;

        switch (_response.Result)
        {
            case GetIncidentsCountUseCase.Result.Success:
                break;
            case GetIncidentsCountUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении количества происшествий", MudBlazor.Severity.Error);
                break;
            case GetIncidentsCountUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(IncidentsCountComponent), nameof(GetIncidentsCountUseCase));
                Snackbar.Add($"Не удалось получить количество происшествий из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(IncidentsCountComponent), nameof(GetIncidentsCountUseCase), _response.Result);
                Snackbar.Add($"Не удалось получить количество происшествий из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    public async ValueTask DisposeAsync()
    {
        try
        {
            // Очищаем ресурсы JavaScript модуля
            if (_jsModule != null)
            {
                await _jsModule.InvokeVoidAsync("cleanupIncidentsChart");
                await _jsModule.DisposeAsync();
                _jsModule = null;
            }
        }
        catch (JSDisconnectedException)
        {
            // Игнорируем ошибки отключения SignalR
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при очистке ресурсов компонента IncidentsCountComponent");
        }

        GC.SuppressFinalize(this);
    }
}