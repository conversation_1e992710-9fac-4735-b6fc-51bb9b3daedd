﻿@using Teslametrics.App.Web.Exceptions
@inherits InteractiveBaseComponent

<MudPaper Outlined="true"
          Class="pa-4 d-flex gap-4 flex-column">
    <MudText Typo="Typo.subtitle2"
             Class="pa-2">Фильтры</MudText>
    <MudStack Row="true">
        <MudAutocomplete T="GetCityListUseCase.Response.Item"
                         SearchFunc="@SearchCityAsync"
                         ToStringFunc="@(e => e == null ? null : e.Name)"
                         Value="@_selectedCity"
                         ValueChanged="@CityValueChanged"
                         Label="Город"
                         Clearable="true"
                         Margin="Margin.Dense"
                         ResetValueOnEmptyText="true"
                         Variant="Variant.Outlined" />
        <MudAutocomplete T="GetBuildingListUseCase.Response.Item"
                         SearchFunc="@SearchBuildingAsync"
                         ToStringFunc="@(e => e == null ? null : e.Name)"
                         Value="@_selectedBuilding"
                         ValueChanged="@BuildingValueChanged"
                         Label="Здание"
                         Clearable="true"
                         Margin="Margin.Dense"
                         ResetValueOnEmptyText="true"
                         Variant="Variant.Outlined"
                         Disabled="City is null" />
    </MudStack>
</MudPaper>

@code {
    private GetCityListUseCase.Response.Item? _selectedCity;
    private GetBuildingListUseCase.Response.Item? _selectedBuilding;

    [Parameter]
    public Guid? City { get; set; }
    [Parameter]
    public EventCallback<Guid?> CityChanged { get; set; }

    [Parameter]
    public Guid? Building { get; set; }
    [Parameter]
    public EventCallback<Guid?> BuildingChanged { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        await UpdateSelectedItemsAsync();
    }

    private async Task UpdateSelectedItemsAsync()
    {
        var cities = await SearchCityAsync(string.Empty, CancellationToken.None);
        // Update selected city if needed
        if (City.HasValue && (_selectedCity == null || _selectedCity.Id != City.Value))
        {
            _selectedCity = cities.FirstOrDefault(c => c.Id == City.Value);
            City = _selectedCity?.Id;
        }
        else if (!City.HasValue)
        {
            _selectedCity = cities.FirstOrDefault();
            City = _selectedCity?.Id;
            await CityChanged.InvokeAsync(City);
        }

        var buildings = await SearchBuildingAsync(string.Empty, CancellationToken.None);
        // Update selected building if needed
        if (Building.HasValue && (_selectedBuilding == null || _selectedBuilding.Id != Building.Value))
        {
            _selectedBuilding = buildings.FirstOrDefault(b => b.Id == Building.Value);
        }
        else if (!Building.HasValue)
        {
            _selectedBuilding = buildings.FirstOrDefault();
            Building = _selectedBuilding?.Id;
            await BuildingChanged.InvokeAsync(Building);
        }
    }

    private async Task CityValueChanged(GetCityListUseCase.Response.Item? city)
    {
        _selectedCity = city;
        await CityChanged.InvokeAsync(city?.Id);

        // Reset building when city changes
        _selectedBuilding = null;
        await BuildingChanged.InvokeAsync(null);
    }

    private async Task BuildingValueChanged(GetBuildingListUseCase.Response.Item? building)
    {
        _selectedBuilding = building;
        await BuildingChanged.InvokeAsync(building?.Id);
    }

    private async Task<IEnumerable<GetBuildingListUseCase.Response.Item>> SearchBuildingAsync(string value, CancellationToken token)
    {
        if (City == null)
            return Enumerable.Empty<GetBuildingListUseCase.Response.Item>();

        GetBuildingListUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetBuildingListUseCase.Query(City.Value, value), token);
        }
        catch (TaskCanceledException)
        {
            // Search was canceled, just return empty result
            return Enumerable.Empty<GetBuildingListUseCase.Response.Item>();
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching buildings");
            Snackbar.Add("Не удалось получить список зданий из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
            return Enumerable.Empty<GetBuildingListUseCase.Response.Item>();
        }

        switch (response.Result)
        {
            case GetBuildingListUseCase.Result.Success:
                return response.Items;

            case GetBuildingListUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении списка зданий", MudBlazor.Severity.Error);
                break;

            default:
                Snackbar.Add("Не удалось получить список зданий из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
                break;
        }
        return Enumerable.Empty<GetBuildingListUseCase.Response.Item>();
    }

    private async Task<IEnumerable<GetCityListUseCase.Response.Item>> SearchCityAsync(string value, CancellationToken token)
    {
        GetCityListUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetCityListUseCase.Query(value), token);
        }
        catch (TaskCanceledException)
        {
            // Search was canceled, just return empty result
            return Enumerable.Empty<GetCityListUseCase.Response.Item>();
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching cities");
            Snackbar.Add("Не удалось получить список городов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
            return Enumerable.Empty<GetCityListUseCase.Response.Item>();
        }

        if (response.Result == GetCityListUseCase.Result.Success)
            return response.Items;

        if (response.Result == GetCityListUseCase.Result.ValidationError)
            Snackbar.Add("Ошибка валидации при получении списка городов", MudBlazor.Severity.Error);
        else
            Snackbar.Add("Не удалось получить список городов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);

        return Enumerable.Empty<GetCityListUseCase.Response.Item>();
    }
}
