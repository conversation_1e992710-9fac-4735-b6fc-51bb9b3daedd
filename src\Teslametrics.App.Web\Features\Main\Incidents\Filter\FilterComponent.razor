﻿@using Teslametrics.App.Web.Features.Main.Incidents.Filter.City
@using Teslametrics.App.Web.Features.Main.Incidents.Filter.Building
@using Teslametrics.App.Web.Features.Main.Incidents.Filter.Floor
@using Teslametrics.App.Web.Features.Main.Incidents.Filter.Room
@using Teslametrics.App.Web.Features.Main.Incidents.Filter.Device
@using Teslametrics.App.Web.Features.Main.Incidents.Filter.DateFilter
@using Teslametrics.App.Web.Features.Main.Incidents.Filter.TimeRangePicker
@using Teslametrics.Shared

<MudPaper Elevation="0"
          Outlined="false"
          Class="mud-width-full pa-4 d-flex gap-4 flex-column">
    <div class="d_contents">
        <MudStack Row="true"
                  AlignItems="AlignItems.Center"
                  Class="filters_header">
            <MudText Typo="Typo.subtitle2">Фильтры</MudText>
        </MudStack>
    </div>
    <div class="d-flex align-center gap-3">
        <DateFilterComponent DateRange="@_dateRange"
                             DateRangeChanged="OnDateRangeChanged" />
        <TimeRangePickerComponent TimeFrom="@_timeFrom"
                                  TimeTo="@_timeTo"
                                  TimeFromChanged="OnTimeFromChanged"
                                  TimeToChanged="OnTimeToChanged" />
        <MudSpacer />
        <MudIconButton OnClick="OpenDialog"
                       Icon="@Icons.Material.Filled.FilterList"
                       Variant="Variant.Filled"
                       Color="Color.Primary"
                       Size="Size.Medium" />
    </div>
</MudPaper>

<MudDialog Visible="_isVisible"
           Class="filter_dialog pa-5 br_16"
           ActionsClass="align-center justify-center px-0 pb-0 py-7"
           ContentClass="content px-0"
           Options="_dialogOptions">
    <DialogContent>
        <div class="title d-flex">
            <MudStack Spacing="0">
                <MudText Typo="Typo.subtitle1">Фильтры</MudText>
            </MudStack>
            <MudSpacer />
            <div>
                <MudIconButton OnClick="Cancel"
                               Icon="@Icons.Material.Outlined.Close" />
            </div>
        </div>

        <MudStack Spacing="5">
            <div class="grid grid_3">
                <CitySelectorComponent City="@_localCityId"
                                       CityChanged="OnLocalCityIdChanged" />
                <BuildingSelectorComponent City="@_localCityId"
                                           BuildingId="@_localBuildingId"
                                           BuildingIdChanged="OnLocalBuildingIdChanged" />
                <FloorSelectorComponent CityId="@_localCityId"
                                        BuildingId="@_localBuildingId"
                                        Floor="@_localFloorId"
                                        FloorChanged="OnLocalFloorIdChanged" />
            </div>

            <div class="grid grid_2">
                <RoomSelectorComponent CityId="@_localCityId"
                                       BuildingId="@_localBuildingId"
                                       FloorId="@_localFloorId"
                                       RoomId="@_localRoomId"
                                       RoomIdChanged="OnLocalRoomIdChanged" />
                <DeviceSelectorComponent CityId="@_localCityId"
                                         BuildingId="@_localBuildingId"
                                         FloorId="@_localFloorId"
                                         RoomId="@_localRoomId"
                                         DeviceId="@_localFridgeId"
                                         DeviceIdChanged="OnLocalFridgeIdChanged" />
            </div>

            <div class="grid grid_2">
                <MudSelect T="IncidentType ?"
                           Label="Тип происшествия"
                           AnchorOrigin="Origin.BottomCenter"
                           Value="@_localIncidentType"
                           ValueChanged="OnLocalIncidentTypeChanged"
                           Variant="Variant.Outlined"
                           Margin="Margin.Dense"
                           Clearable="true">
                    @foreach (IncidentType selectOption in (IncidentType[])Enum.GetValues(typeof(IncidentType)))
                    {
                        <MudSelectItem T="IncidentType ?"
                                       Value="selectOption"
                                       @key="selectOption">@selectOption.GetName()</MudSelectItem>
                    }
                </MudSelect>
                <MudSelect T="bool?"
                           Clearable="true"
                           Value="@_localIsResolved"
                           ValueChanged="OnLocalIsResolvedChanged"
                           Label="Статус"
                           Margin="Margin.Dense"
                           Variant="Variant.Outlined">
                    <MudSelectItem T="bool?"
                                   Value="null">Все</MudSelectItem>
                    <MudSelectItem T="bool?"
                                   Value="true">Решено</MudSelectItem>
                    <MudSelectItem T="bool?"
                                   Value="false">Не решено</MudSelectItem>
                </MudSelect>
            </div>
        </MudStack>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel"
                   Variant="Variant.Outlined">Отмена</MudButton>
        <MudButton OnClick="Save"
                   Variant="Variant.Filled"
                   Color="Color.Primary">Сохранить</MudButton>
    </DialogActions>
</MudDialog>

<style>
    .filter_dialog .grid {
        display: grid;
        gap: 8px;
    }

    .filter_dialog .grid_2 {
        grid-template-columns: repeat(2, 1fr);
    }

    .filter_dialog .grid_3 {
        grid-template-columns: repeat(3, 1fr);
    }

    @@media (min-width: 576px) and (max-width: 767.98px) {
        .filter_dialog .grid_2 {
            grid-template-columns: 1fr;
        }

        .filter_dialog .grid_3 {
            grid-template-columns: 1fr;
        }
    }
</style>