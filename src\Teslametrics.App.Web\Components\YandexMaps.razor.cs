using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Globalization;
using System.Text.Json;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Components;

public partial class YandexMaps : IAsyncDisposable
{
    private readonly Guid _id = Guid.NewGuid();
    private string? _apiKey;
    private IJSObjectReference? _jsModule;
    private DotNetObjectReference<YandexMaps>? _dotNetRef;

    private Coordinates? _coordinates { get; set; }
    private List<Coordinates> _markers { get; set; } = [];

    [Inject]
    private IJSRuntime _jsRuntime { get; set; } = default!;

    [Inject]
    private IConfiguration _configuration { get; set; } = default!;

    protected class YandexMapsConverter : MudBlazor.Converter<Coordinates?, string?>
    {
        public YandexMapsConverter()
        {
            SetFunc = coordinates => coordinates != null
                ? JsonSerializer.Serialize(coordinates)
                : null;

            GetFunc = str => !string.IsNullOrEmpty(str)
                ? JsonSerializer.Deserialize<Coordinates>(str)
                : null;
        }
    }

    public record CoordinatesWithAddress(double Latitude, double Longitude, string? Address = null);

    [Parameter]
    public bool ReadOnly { get; set; } = true;

    [Parameter]
    public string Width { get; set; } = "600px";

    [Parameter]
    public string Height { get; set; } = "400px";

    [Parameter]
    public Coordinates? Coordinates { get; set; }

    [Parameter]
    public EventCallback<Coordinates?> CoordinatesChanged { get; set; }

    [Parameter]
    public EventCallback<CoordinatesWithAddress?> CoordinatesWithAddressChanged { get; set; }

    [Parameter]
    public List<Coordinates> Markers { get; set; } = [];
    [Parameter]

    public EventCallback<List<Coordinates>> MarkersChanged { get; set; }

    [Parameter]
    public bool SingleMarkerMode { get; set; } = true;


    public YandexMaps() : base(new YandexMapsConverter())
    {
    }

    /// <summary>
    /// Вызывается из JavaScript когда пользователь перемещает маркер
    /// </summary>
    [JSInvokable]
    public async Task OnMarkerPositionChanged(double? latitude, double? longitude)
    {
        Touched = true;
        if (latitude.HasValue && longitude.HasValue)
        {
            Coordinates = new Coordinates(latitude.Value, longitude.Value);
        }
        else
        {
            Coordinates = null;
        }
        _coordinates = Coordinates;

        if (CoordinatesChanged.HasDelegate)
        {
            await CoordinatesChanged.InvokeAsync(Coordinates);
        }
        await WriteValueAsync(Coordinates);
        await BeginValidateAsync();
        FieldChanged(Coordinates);
    }

    /// <summary>
    /// Вызывается из JavaScript когда пользователь перемещает маркер с адресной информацией
    /// </summary>
    [JSInvokable]
    public async Task OnMarkerPositionChangedWithAddress(double? latitude, double? longitude, string? address)
    {
        Touched = true;
        CoordinatesWithAddress? coordinatesWithAddress = null;

        if (latitude.HasValue && longitude.HasValue)
        {
            Coordinates = new Coordinates(latitude.Value, longitude.Value);
            coordinatesWithAddress = new CoordinatesWithAddress(latitude.Value, longitude.Value, address);
        }
        else
        {
            Coordinates = null;
        }
        _coordinates = Coordinates;

        // Вызываем оба события для обратной совместимости
        if (CoordinatesChanged.HasDelegate)
        {
            await CoordinatesChanged.InvokeAsync(Coordinates);
        }

        if (CoordinatesWithAddressChanged.HasDelegate)
        {
            await CoordinatesWithAddressChanged.InvokeAsync(coordinatesWithAddress);
        }

        await WriteValueAsync(Coordinates);
        await BeginValidateAsync();
        FieldChanged(Coordinates);
    }

    /// <summary>
    /// Вызывается из JavaScript когда пользователь удаляет маркер
    /// </summary>
    [JSInvokable]
    public async Task OnMarkerRemovedAsync()
    {
        Coordinates = null;
        Touched = true;
        if (CoordinatesChanged.HasDelegate)
        {
            await CoordinatesChanged.InvokeAsync(Coordinates);
        }
        await WriteValueAsync(Coordinates);
        await BeginValidateAsync();
        FieldChanged(Coordinates);
    }

    [JSInvokable]
    public async Task OnMarkersUpdated(List<Coordinates> newMarkers)
    {
        Touched = true;
        Markers = newMarkers;
        if (MarkersChanged.HasDelegate)
        {
            await MarkersChanged.InvokeAsync(Markers);
        }
        _markers = Markers;
        await BeginValidateAsync();
    }

    /// <summary>
    /// Автоматически масштабирует карту для отображения всех маркеров
    /// </summary>
    public async Task FitBoundsAsync()
    {
        if (_jsModule is not null)
        {
            try
            {
                await _jsModule.InvokeVoidAsync("fitBounds", _id.ToString());
            }
            catch (JSDisconnectedException)
            {
                // Connection already terminated
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fitting bounds: {ex.Message}");
            }
        }
    }

    public new async ValueTask DisposeAsync()
    {
        try
        {
            if (_jsModule is not null)
            {
                await _jsModule.DisposeAsync();
            }
            _dotNetRef?.Dispose();
        }
        catch (JSDisconnectedException)
        {
            // Connection already terminated
        }
        catch (Exception exc)
        {
            Console.WriteLine($"Error disposing map: {exc.Message}");
        }
        finally
        {
            // Call base class disposal
            await base.DisposeAsync();

            // Suppress finalization to follow proper disposal pattern
            GC.SuppressFinalize(this);
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (_jsModule is not null && (_coordinates != Coordinates || _markers != Markers))
        {
            _coordinates = Coordinates;
            _markers = Markers;
            try
            {
                await _jsModule.InvokeVoidAsync("setMarkers", _id.ToString(), Markers);
            }
            catch (JSDisconnectedException)
            {
                // Connection already terminated
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }
        await base.OnParametersSetAsync();
    }

    protected override void OnInitialized()
    {
        try
        {
            _coordinates = Coordinates;
            _markers = Markers;
            _apiKey = _configuration["YandexMaps:ApiKey"]
                ?? throw new InvalidOperationException("Yandex Maps API key not found in configuration");
            //Form?.Register(this);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender && !string.IsNullOrEmpty(_apiKey))
        {
            try
            {
                _jsModule = await _jsRuntime.InvokeAsync<IJSObjectReference>(
                    "import", "./Components/YandexMaps.razor.js");

                if (_jsModule is not null)
                {
                    _dotNetRef = DotNetObjectReference.Create(this);

                    var markersToPass = SingleMarkerMode
                        ? (Coordinates.HasValue ? [Coordinates.Value] : [])
                        : Markers;

                    await _jsModule.InvokeVoidAsync(
                        "Initialize",
                        _id.ToString(),
                        _apiKey,
                        GetLocale(),
                        markersToPass,
                        ReadOnly,
                        SingleMarkerMode,
                        _dotNetRef);
                }
            }
            catch (JSDisconnectedException)
            {
                // Connection already terminated
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing map: {ex.Message}");
            }
        }
    }

    private static string GetLocale()
    {
        // Convert culture name (e.g. "ru-RU") to Yandex Maps format (e.g. "ru_RU")
        return CultureInfo.CurrentCulture.Name.Replace("-", "_");
    }
}
