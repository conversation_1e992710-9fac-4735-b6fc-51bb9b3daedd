using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Home.IncidentTypes;

public partial class IncidentTypesComponent
{
    public record ChartData(Teslametrics.Shared.IncidentType ChartType, string Name, int Count);

    private IJSObjectReference? _jsModule;
    private GetIncidentTypeCountUseCase.Response? _response = null;

    [Inject]
    private IJSRuntime JSRuntime { get; set; } = null!;

    protected override async Task OnParametersSetAsync()
    {
        await LoadDataAsync();
        await BuildChartsAsync();
        await base.OnParametersSetAsync();
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadDataAsync();
        await base.OnInitializedAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            try
            {
                _jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./Features/Main/Home/IncidentTypes/IncidentTypesComponent.razor.js");
                await BuildChartsAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing charts");
                Snackbar.Add("Не удалось загрузить скрипт для построения графика", MudBlazor.Severity.Error);
            }
        }
    }

    private async Task BuildChartsAsync()
    {
        if (_jsModule == null) return;

        try
        {
            var chartsData = _response?.Incidents.Select(d => new ChartData(d.IncidentType, d.IncidentType.GetName()!, d.Count)).ToList();
            await _jsModule.InvokeVoidAsync("renderIncidentTypes", chartsData);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading charts data");
            Snackbar.Add("Не удалось построить график на полученных данных", MudBlazor.Severity.Error);
        }
    }

    // Mock data methods - these would be replaced with actual API calls
    private async Task LoadDataAsync()
    {
        await SetLoadingAsync(true);
        try
        {
            var currentDate = DateTime.Now;
            var start = currentDate.AddDays(-7);
            var end = currentDate;
            _response = await ScopeFactory.MediatorSend(new GetIncidentTypeCountUseCase.Query(start, end, null, null, null, null, null));
        }
        catch (Exception ex)
        {
            _response = null;
            Snackbar.Add("Ошибка при получении типов происшествий во время запроса к серверу. Обратитесь к администратору.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetLoadingAsync(false);
        if (_response is null) return;

        switch (_response.Result)
        {
            case GetIncidentTypeCountUseCase.Result.Success:
                break;
            case GetIncidentTypeCountUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении типов происшествий", MudBlazor.Severity.Error);
                break;
            case GetIncidentTypeCountUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(IncidentTypesComponent), nameof(GetIncidentTypeCountUseCase));
                Snackbar.Add($"Не удалось получить типы происшествий из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(IncidentTypesComponent), nameof(GetIncidentTypeCountUseCase), _response.Result);
                Snackbar.Add($"Не удалось получить типы происшествий из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
}
