::deep .apexcharts-datalabels rect {
    fill: var(--mud-palette-surface) !important;
}

::deep .apexcharts-datalabels text {
    fill: var(--mud-palette-text-primary) !important;
}

::deep .main-svg {
    overflow: visible !important;
}

/* Стили для кастомных аннотаций круговой диаграммы */
::deep .annotation .bg,
::deep .annotation-text-g rect {
    rx: 8px !important;
    ry: 8px !important;
    filter: drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.1)) !important;
}

::deep .annotation-text-g text {
    font-weight: 500 !important;
    letter-spacing: 0.02em !important;
    font-family: 'Inter', sans-serif !important;
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
    ::deep .annotation-text-g text {
        font-size: 10px !important;
    }

    ::deep .annotation-text-g rect {
        rx: 6px !important;
        ry: 6px !important;
    }
}