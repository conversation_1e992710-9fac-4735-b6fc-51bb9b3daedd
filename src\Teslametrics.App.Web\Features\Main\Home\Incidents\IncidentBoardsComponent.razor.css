::deep .incident_time {
    color: #B8C2CC;
}

::deep .dotted {
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 100px;
    background: var(--color-bg-2);
}

::deep .dotted::before {
    border-radius: 50%;
    content: " ";
    display: block;
    width: 9px;
    height: 9px;
}

.mud_theme_light div ::deep .dotted::before {
    background: rgba(184, 194, 204, 1);
}

.mud_theme_dark div ::deep .dotted::before {
    background: rgb(83 88 93);
}

div ::deep .dotted.primary::before {
    background: var(--mud-palette-success);
}

div ::deep .dotted.error {
    background: var(--color-system-err-bg);
    color: var(--mud-palette-error);
}

div ::deep .dotted.error::before {
    background: var(--mud-palette-error);
}

@media screen and (max-width: 767px) {
    ::deep .incidents {
        flex-direction: column !important;
	}
}