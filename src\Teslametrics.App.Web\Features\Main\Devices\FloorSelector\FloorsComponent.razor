@inherits InteractiveBaseComponent

<div class="d_contents">
	@if (_response is not null)
	{
		<MudText>Этаж</MudText>
		<MudStack Row="true">
			@foreach (var item in _response.Items)
			{
				<MudIconButton OnClick="() => OnFloorSelectAsync(item)"
							   Color="@(item.Id == Floor ? Color.Primary : Color.Default)"
							   Class="@(item.Id == Floor ? "selected button" : "button")"
							   Size="Size.Small">
					@item.Number
				</MudIconButton>
			}
		</MudStack>
	}
	else
	{
		<MudText>Не удалось получить список этажей здания</MudText>
		<MudIconButton OnClick="FetchDataAsync"
					   Icon="@Icons.Material.Outlined.Refresh" />
	}
</div>
