using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.MediaServer.Orleans.Camera.Events;

namespace Teslametrics.App.Web.Features.Main.Incidents.IncidentDialog.Preview;

public partial class CameraPreview : IPreviewObserver, IAsyncDisposable
{
    private Guid? _cameraStreamId;
    private IPreviewObserver? _observer;

    [Inject]
    private NavigationManager _navigationManager { get; set; } = null!;

    [Inject]
    private IClusterClient _clusterClient { get; set; } = null!;

    [Parameter]
    [EditorRequired]
    public DateTimeOffset FiredAt { get; set; } = default!;

    [Parameter]
    [EditorRequired]
    public Guid OrganizationId { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid CameraId { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid? CameraStreamId { get; set; }

    private SubscribeCameraPreviewUseCase.Response? _subscriptionResult;

    public Task PreviewUpdated(CameraPreviewImageUpdatedEvent @event)
    {
        return InvokeAsync(StateHasChanged);
    }

    public async ValueTask DisposeAsync()
    {
        await UnsubscribeAsync();
        GC.SuppressFinalize(this);
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        var timer = new System.Timers.Timer(240_000); // 5 минут
        timer.Elapsed += OnTimerElapsed;
        timer.AutoReset = true;
        timer.Start();

        CompositeDisposable.Add(timer);
    }

    protected override async Task OnParametersSetAsync()
    {
        if (_cameraStreamId != CameraStreamId)
        {
            await UnsubscribeAsync();
            _cameraStreamId = CameraStreamId;
            await SubscribeAsync();
        }
        await base.OnParametersSetAsync();
    }

    private async Task SubscribeAsync()
    {
        try
        {
            if (!CameraStreamId.HasValue || CameraStreamId == Guid.Empty) return;

            // Ensure we have a clean state before subscribing
            if (_observer is not null)
                await UnsubscribeAsync();

            // Create a new observer reference
            _observer = _clusterClient.CreateObjectReference<IPreviewObserver>(this);
            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraPreviewUseCase.Request(_observer, CameraStreamId.Value));
        }
        catch (Exception ex)
        {
            _subscriptionResult = null;
            _observer = null; // Reset observer on failure
            Snackbar.Add($"Не удалось подписаться на события превью камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, "Failed to subscribe to camera preview events");
        }

        switch (_subscriptionResult?.Result)
        {
            case SubscribeCameraPreviewUseCase.Result.Success:
                break;
            case SubscribeCameraPreviewUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события превью камеры", MudBlazor.Severity.Error);
                break;
            case SubscribeCameraPreviewUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraPreview), nameof(SubscribeCameraPreviewUseCase));
                Snackbar.Add($"Не удалось получить подписку на события превью камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraPreview), nameof(SubscribeCameraPreviewUseCase), _subscriptionResult?.Result);
                Snackbar.Add($"Не удалось получить подписку на события превью камеры из-за ошибки", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task UnsubscribeAsync()
    {
        if (_observer is null || !_cameraStreamId.HasValue) return;

        var observerToDelete = _observer;
        UnsubscribeCameraPreviewUseCase.Response? response = null;

        try
        {
            response = await ScopeFactory.MediatorSend(new UnsubscribeCameraPreviewUseCase.Request(_observer, _cameraStreamId.Value));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Не удалось отменить подписку на события превью изображения из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, "Failed to unsubscribe from camera preview events");
        }

        // Always try to delete the object reference, even if unsubscribe failed
        try
        {
            if (observerToDelete is not null)
            {
                _clusterClient.DeleteObjectReference<IPreviewObserver>(observerToDelete);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to delete Orleans object reference for observer");
        }
        finally
        {
            // Always reset the observer reference to null after attempting deletion
            _observer = null;
        }

        switch (response?.Result)
        {
            case UnsubscribeCameraPreviewUseCase.Result.Success:
                break;
            case UnsubscribeCameraPreviewUseCase.Result.ValidationError:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraPreview), nameof(UnsubscribeCameraPreviewUseCase));
                Snackbar.Add($"Не удалось отменить подписку на события превью изображения из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            case UnsubscribeCameraPreviewUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraPreview), nameof(UnsubscribeCameraPreviewUseCase));
                Snackbar.Add($"Не удалось получить подписку на события превью камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraPreview), nameof(UnsubscribeCameraPreviewUseCase), _subscriptionResult?.Result);
                Snackbar.Add($"Не удалось получить подписку на события превью камеры из-за ошибки", MudBlazor.Severity.Error);
                break;
        }
    }

    private void OnTimerElapsed(object? sender, System.Timers.ElapsedEventArgs e)
    {
        InvokeAsync(async () =>
        {
            await SubscribeAsync();
            StateHasChanged();
        });
    }

    private void ShowArchive()
    {
        var encoded = Uri.EscapeDataString(FiredAt.ToDateTimeOffsetString());
        _navigationManager.NavigateTo($"/cameras/archive/{OrganizationId}/{CameraId}?StartFrom={encoded}");
    }
}
