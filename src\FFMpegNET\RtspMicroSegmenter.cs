using System.Runtime.InteropServices;
using System.Threading.Channels;
using FFmpeg.AutoGen;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace FFMpegNET;

/// <summary>
/// Класс для получения RTSP потока и разрезания его по I-кадрам на микросегменты.
/// Используется в CameraStreamGrain для создания микросегментов.
/// </summary>
public unsafe sealed class RtspMicroSegmenter : IDisposable
{
    public enum State
    {
        Initialized,
        Running,
        Stopped,
        Faulted,
        Disposed
    }

    public record Options
    {
        /// <summary>
        /// Тип транспорта RTSP
        /// </summary>
        public RtspTransportType RtspTransport { get; set; } = RtspTransportType.Tcp;

        /// <summary>
        /// Размер буфера в байтах
        /// </summary>
        public int BufferSize { get; set; } = 16777216;

        /// <summary>
        /// Максимальная задержка в микросекундах
        /// </summary>
        public int MaxDelay { get; set; } = 1000000;

        /// <summary>
        /// Размер очереди для переупорядочивания пакетов
        /// </summary>
        public int ReorderQueueSize { get; set; } = 2000;

        /// <summary>
        /// Таймаут сокета в микросекундах
        /// </summary>
        public int SocketTimeout { get; set; } = 30000000;

        public int FifoSize { get; set; } = 65536;

        public int AnalyzeDuration { get; set; } = 5000000;

        public static Options Default => new Options();
    }

    #region Константы
    /// <summary>
    /// Максимальная задержка для выходного контекста (в микросекундах)
    /// </summary>
    private const int _outputMaxDelay = 500000;
    #endregion

    #region Настройки и зависимости
    private readonly ILogger<RtspMicroSegmenter> _logger;
    private readonly Options _options;
    private State _state;
    #endregion

    #region FFmpeg контексты и потоки
    // Основной контекст FFmpeg для входного потока
    private AVFormatContext* _inputFormatContext;
    // Контекст FFmpeg для выходного потока
    private AVFormatContext* _outputFormatContext;
    // Поток для записи выходных данных
    private AVStream* _outputVideoStream;
    // Буфер в памяти для хранения текущего сегмента
    private MemoryOutputBuffer? _currentSegmentBuffer;
    #endregion

    #region Индексы и параметры потоков
    // Индекс видеопотока среди всех потоков
    private int _videoStreamIndex;
    // Временная база для видео
    private AVRational _videoTimeBase;
    // Индекс аудио потока
    private int _audioStreamIndex;
    // Временная база для аудио
    private AVRational _audioTimeBase;
    // Частота кадров
    private double _videoFrameRate;
    #endregion

    #region Управление сегментами
    // Временная метка начала текущего сегмента
    private long _segmentStartPts;
    // Порядковый номер сегмента
    private int _segmentNumber;
    #endregion

    #region Временные метки и синхронизация
    private DateTimeOffset _streamStartTime;
    private long _firstPts;
    private long _lastPts;
    #endregion

    #region Управление асинхронной обработкой
    private CancellationTokenSource? _runCts;
    private Task? _inputTask;
    private Task? _outputTask;
    private Channel<(MemoryOutputBuffer, DateTimeOffset, double)>? _streamChannel;

    private readonly EventWaitHandle _stoppedEvent;
    #endregion

    private long _videoTotalBytes;
    private long _audioTotalBytes;
    private bool _hasAudio;

    /// <summary>
    /// Инициализирует новый экземпляр микросегментатора RTSP потока
    /// </summary>
    public RtspMicroSegmenter(ILogger<RtspMicroSegmenter> logger, IOptions<Options>? options = null)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? Options.Default;

        _stoppedEvent = new EventWaitHandle(false, EventResetMode.ManualReset);

        _state = State.Initialized;
    }

    public void ResetFields()
    {
        _currentSegmentBuffer = null;

        _videoTimeBase = new AVRational();
        _audioTimeBase = new AVRational();

        _videoStreamIndex = -1;
        _audioStreamIndex = -1;

        _segmentNumber = 0;
        _videoFrameRate = 0;

        _streamStartTime = DateTimeOffset.MinValue;

        _segmentStartPts = -1;
        _firstPts = -1;
        _lastPts = -1;

        _videoTotalBytes = 0;
        _audioTotalBytes = 0;
        _hasAudio = false;

        _stoppedEvent.Reset();
    }

    /// <summary>
    /// Основной метод микросегментации видеопотока.
    /// Читает пакеты из входного потока, обрабатывает их и записывает микросегменты на каждом I-кадре.
    /// </summary>
    public Task Run(string rtspUrl, bool enableAudio, Func<MicroSegment, Task> onNextMicroSegment, CancellationToken cancellationToken)
    {
        ThrowIfDisposed();

        if (_state is State.Running)
        {
            throw new InvalidOperationException("Segmentation already running");
        }

        if (string.IsNullOrEmpty(rtspUrl))
        {
            throw new ArgumentNullException(nameof(rtspUrl));
        }

        ResetFields();

        CreateInputContext();

        ConfigureAndOpenInput(rtspUrl);

        LoadStreamInfo();

        FindAndConfigureStreams(rtspUrl, enableAudio);

        _hasAudio = enableAudio && _audioStreamIndex != -1;

        _runCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

        _streamChannel = Channel.CreateBounded<(MemoryOutputBuffer, DateTimeOffset, double)>(new BoundedChannelOptions(128)
        {
            SingleWriter = true,
            SingleReader = true
        });

        _inputTask = Task.Run(() =>
        {
            try
            {
                ProcessInputStream(_runCts.Token);
            }
            finally
            {
                CloseInputContext();

                if (!_runCts.IsCancellationRequested)
                {
                    _runCts.Cancel();
                }
            }
        }, cancellationToken);

        _outputTask = RtspMicroSegmenterHelper.ProcessOutputStream(_streamChannel, onNextMicroSegment, _runCts.Token);

        _state = State.Running;

        return Task.WhenAll(_inputTask, _outputTask)
            .ContinueWith(task =>
            {
                _inputTask.Dispose();
                _outputTask.Dispose();
                _runCts.Dispose();
                _runCts = null;

                _state = task.IsFaulted ? State.Faulted : State.Stopped;

                _stoppedEvent.Set();
            }, cancellationToken);
    }

    /// <summary>
    /// Запрашивает плавную остановку обработки после завершения текущего сегмента
    /// </summary>
    public void RequestGracefulStop()
    {
        ThrowIfDisposed();

        if (_state is State.Running)
        {
            _runCts!.Cancel();
        }
    }

    private void ThrowIfDisposed()
    {
        ObjectDisposedException.ThrowIf(_state is State.Disposed, nameof(RtspMicroSegmenter));
    }

    ~RtspMicroSegmenter()
    {
        Dispose(false);
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    private void Dispose(bool disposing)
    {
        if (_state is State.Disposed)
            return;

        if (disposing)
        {
            if (_state is State.Running)
            {
                _runCts?.Cancel();

                _stoppedEvent.WaitOne();
                _stoppedEvent.Dispose();
            }
        }

        CloseInputContext();

        if (_outputFormatContext != null)
        {
            ffmpeg.avformat_free_context(_outputFormatContext);
            _outputFormatContext = null;
        }

        _currentSegmentBuffer?.Dispose();
        _currentSegmentBuffer = null;

        _state = State.Disposed;
    }

    /// <summary>
    /// Создает контекст для входного потока
    /// </summary>
    private void CreateInputContext()
    {
        _inputFormatContext = ffmpeg.avformat_alloc_context();
        if (_inputFormatContext == null)
        {
            throw new ApplicationException("Failed to allocate memory for input context.");
        }
    }

    /// <summary>
    /// Настраивает и открывает входной поток
    /// </summary>
    private void ConfigureAndOpenInput(string rtspUrl)
    {
        // Устанавливаем параметры буферизации для RTSP
        AVDictionary* dict = null;

        try
        {
            SetDictionaryValue(&dict, "rtsp_transport", _options.RtspTransport.ToString().ToLowerInvariant());
            SetDictionaryValue(&dict, "buffer_size", _options.BufferSize.ToString());
            SetDictionaryValue(&dict, "max_delay", _options.MaxDelay.ToString());
            SetDictionaryValue(&dict, "reorder_queue_size", _options.ReorderQueueSize.ToString());
            SetDictionaryValue(&dict, "timeout", _options.SocketTimeout.ToString());
            SetDictionaryValue(&dict, "stimeout", _options.SocketTimeout.ToString());
            SetDictionaryValue(&dict, "fifo_size", _options.FifoSize.ToString());
            //SetDictionaryValue(&dict, "fflags", "nobuffer");
            SetDictionaryValue(&dict, "analyzeduration", _options.AnalyzeDuration.ToString());
            SetDictionaryValue(&dict, "rtsp_flags", "prefer_tcp");

            fixed (AVFormatContext** inputFormatContext = &_inputFormatContext)
            {
                if (ffmpeg.avformat_open_input(inputFormatContext, rtspUrl, null, &dict) < 0)
                {
                    throw new ApplicationException($"Failed to open stream by URL: {rtspUrl}");
                }
            }

            //TODO учесть задержку сети?
            _streamStartTime = DateTimeOffset.UtcNow;
        }
        finally
        {
            if (dict != null)
            {
                ffmpeg.av_dict_free(&dict);
            }
        }
    }

    private void CloseInputContext()
    {
        fixed (AVFormatContext** ppFormatContext = &_inputFormatContext)
        {
            if (_inputFormatContext != null)
            {
                ffmpeg.avformat_close_input(ppFormatContext);
                _inputFormatContext = null;
            }
        }
    }

    /// <summary>
    /// Устанавливает значение в словаре с проверкой результата
    /// </summary>
    private static void SetDictionaryValue(AVDictionary** dict, string key, string value)
    {
        int result = ffmpeg.av_dict_set(dict, key, value, 0);
        if (result < 0)
        {
            throw new ApplicationException($"Failed to set {key}: {result}");
        }
    }

    /// <summary>
    /// Загружает информацию о потоке
    /// </summary>
    private void LoadStreamInfo()
    {
        if (ffmpeg.avformat_find_stream_info(_inputFormatContext, null) < 0)
        {
            throw new ApplicationException("Failed to retrieve stream information.");
        }
    }

    /// <summary>
    /// Ищет и настраивает видео и аудио потоки
    /// </summary>
    private void FindAndConfigureStreams(string rtspUrl, bool enableAudio)
    {
        _videoStreamIndex = -1;
        _audioStreamIndex = -1;

        for (int i = 0; i < _inputFormatContext->nb_streams; i++)
        {
            var stream = _inputFormatContext->streams[i];
            var codecType = stream->codecpar->codec_type;

            if (codecType == AVMediaType.AVMEDIA_TYPE_VIDEO)
            {
                ConfigureVideoStream(i, stream);
            }
            else if (enableAudio && codecType == AVMediaType.AVMEDIA_TYPE_AUDIO)
            {
                ConfigureAudioStream(i, stream);
            }

            if (ShouldStopStreamSearch(enableAudio))
            {
                break;
            }
        }

        ValidateStreams(enableAudio);
    }

    /// <summary>
    /// Настраивает видеопоток
    /// </summary>
    private void ConfigureVideoStream(int index, AVStream* videoStream)
    {
        _videoStreamIndex = index;
        _videoTimeBase = videoStream->time_base;

        //_logger.LogInformation("Input stream parameters: time_base={TimeBase}, avg_frame_rate={AvgFrameRate}, r_frame_rate={RFrameRate}",
        //    $"{videoStream->time_base.num}/{videoStream->time_base.den}",
        //    $"{videoStream->avg_frame_rate.num}/{videoStream->avg_frame_rate.den}",
        //    $"{videoStream->r_frame_rate.num}/{videoStream->r_frame_rate.den}");

        ConfigureFrameRate(videoStream);
    }

    /// <summary>
    /// Настраивает частоту кадров
    /// </summary>
    private void ConfigureFrameRate(AVStream* videoStream)
    {
        if (videoStream->avg_frame_rate.den != 0)
        {
            _videoFrameRate = (double)videoStream->avg_frame_rate.num / videoStream->avg_frame_rate.den;
            //_logger.LogInformation("Frame rate: {FrameRate:F2} fps", _videoFrameRate);
        }
        else if (videoStream->r_frame_rate.den != 0)
        {
            _videoFrameRate = (double)videoStream->r_frame_rate.num / videoStream->r_frame_rate.den;
            //_logger.LogInformation("Frame rate (real): {FrameRate:F2} fps", _videoFrameRate);
        }
        else
        {
            //_logger.LogInformation("Unable to determine frame rate, using adaptive calculation");
            _videoFrameRate = 0;
        }
    }

    /// <summary>
    /// Настраивает аудиопоток
    /// </summary>
    private void ConfigureAudioStream(int index, AVStream* stream)
    {
        _audioStreamIndex = index;
        _audioTimeBase = stream->time_base;
        //_logger.LogInformation("Found audio stream with index {Index}", index);
    }

    /// <summary>
    /// Проверяет, нужно ли прекратить поиск потоков
    /// </summary>
    private bool ShouldStopStreamSearch(bool enableAudio)
    {
        if (_videoStreamIndex != -1)
        {
            if (!enableAudio || _audioStreamIndex != -1)
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// Проверяет корректность найденных потоков
    /// </summary>
    private void ValidateStreams(bool enableAudio)
    {
        if (_videoStreamIndex == -1)
        {
            throw new ApplicationException("Video stream not found.");
        }

        if (enableAudio && _audioStreamIndex == -1)
        {
            _logger.LogWarning("Audio processing was enabled but no audio stream found");
        }
    }

    #region Обработка входного потока

    private void ProcessInputStream(CancellationToken cancellationToken)
    {
        ThrowIfDisposed();

        var packet = AllocatePacket();

        try
        {
            CreateOutputSegment();
            ProcessInputFrames(packet, cancellationToken);
            CloseOutputSegment();
            _streamChannel!.Writer.Complete();
        }
        finally
        {
            ffmpeg.av_packet_free(&packet);
        }
    }

    /// <summary>
    /// Выделяет память под пакет данных
    /// </summary>
    private static AVPacket* AllocatePacket()
    {
        var packet = ffmpeg.av_packet_alloc();
        if (packet == null)
        {
            throw new ApplicationException("Failed to allocate memory for packet.");
        }
        return packet;
    }

    /// <summary>
    /// Обрабатывает кадры из входного потока
    /// </summary>
    private void ProcessInputFrames(AVPacket* packet, CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested &&
               ffmpeg.av_read_frame(_inputFormatContext, packet) >= 0)
        {
            if (packet->stream_index == _videoStreamIndex)
            {
                ProcessVideoPacket(packet);
            }
            else if (packet->stream_index == _audioStreamIndex)
            {
                ProcessAudioPacket(packet);
            }

            ffmpeg.av_packet_unref(packet);
        }
    }

    /// <summary>
    /// Обрабатывает пакет видеоданных
    /// </summary>
    private void ProcessVideoPacket(AVPacket* packet)
    {
        _videoTotalBytes += packet->size;

        UpdatePacketPtsDts(packet);
        UpdateFirstPts(packet);

        if (_segmentStartPts < 0)
        {
            _segmentStartPts = packet->pts;
        }

        bool isKeyFrame = (packet->flags & ffmpeg.AV_PKT_FLAG_KEY) != 0;

        // Если это ключевой кадр, создаем новый микросегмент
        if (isKeyFrame && _segmentStartPts >= 0)
        {
            _lastPts = packet->pts;

            CloseOutputSegment();

            // Если запрошена остановка, завершаем после закрытия сегмента
            if (_runCts!.IsCancellationRequested)
            {
                return;
            }

            CreateOutputSegment();
            _segmentStartPts = packet->pts;
        }

        WriteVideoPacket(packet);
    }

    /// <summary>
    /// Обновляет временные метки пакета при необходимости
    /// </summary>
    private void UpdatePacketPtsDts(AVPacket* packet)
    {
        if (packet->pts == ffmpeg.AV_NOPTS_VALUE)
        {
            packet->pts = _lastPts + 1;
        }
        if (packet->dts == ffmpeg.AV_NOPTS_VALUE)
        {
            packet->dts = packet->pts;
        }
    }

    /// <summary>
    /// Обновляет первую временную метку потока
    /// </summary>
    private void UpdateFirstPts(AVPacket* packet)
    {
        if (_firstPts == -1 && packet->pts != ffmpeg.AV_NOPTS_VALUE)
        {
            _firstPts = packet->pts;
            //_logger.LogInformation("First PTS: {Pts}", _firstPts);
        }
    }

    /// <summary>
    /// Записывает пакет видеоданных в выходной поток
    /// </summary>
    private void WriteVideoPacket(AVPacket* packet)
    {
        AVPacket* outPacket = AllocatePacket();

        try
        {
            if (ffmpeg.av_packet_ref(outPacket, packet) < 0)
            {
                throw new ApplicationException("Error copying packet.");
            }

            outPacket->stream_index = _videoStreamIndex;

            AdjustPacketPtsDts(outPacket,
                _inputFormatContext->streams[_videoStreamIndex]->time_base,
                _outputVideoStream->time_base);

            WritePacketToOutput(outPacket);
        }
        finally
        {
            ffmpeg.av_packet_free(&outPacket);
        }
    }

    /// <summary>
    /// Обрабатывает пакет аудиоданных
    /// </summary>
    private void ProcessAudioPacket(AVPacket* packet)
    {
        _audioTotalBytes += packet->size;

        WriteAudioPacket(packet);
    }

    /// <summary>
    /// Записывает пакет аудиоданных в выходной поток
    /// </summary>
    private void WriteAudioPacket(AVPacket* packet)
    {
        AVPacket* outPacket = ffmpeg.av_packet_alloc();
        if (outPacket == null)
        {
            throw new ApplicationException("Failed to allocate memory for output audio packet.");
        }

        try
        {
            if (ffmpeg.av_packet_ref(outPacket, packet) < 0)
            {
                throw new ApplicationException("Error copying audio packet.");
            }

            outPacket->stream_index = _audioStreamIndex;

            AdjustPacketPtsDts(outPacket,
                _inputFormatContext->streams[_audioStreamIndex]->time_base,
                _outputFormatContext->streams[_audioStreamIndex]->time_base);

            WritePacketToOutput(outPacket);
        }
        finally
        {
            ffmpeg.av_packet_free(&outPacket);
        }
    }

    /// <summary>
    /// Корректирует временные метки пакета относительно начала сегмента
    /// </summary>
    private static void AdjustPacketPtsDts(AVPacket* packet, AVRational sourceTimeBase, AVRational destinationTimeBase)
    {
        // Используем оригинальные временные метки из входного потока
        ffmpeg.av_packet_rescale_ts(packet, sourceTimeBase, destinationTimeBase);
    }

    /// <summary>
    /// Записывает пакет в выходной поток
    /// </summary>
    private void WritePacketToOutput(AVPacket* outPacket)
    {
        if (ffmpeg.av_interleaved_write_frame(_outputFormatContext, outPacket) < 0)
        {
            _logger.LogError("Error writing packet");
        }
    }

    #endregion

    #region Создание и настройка выходного потока

    /// <summary>
    /// Создаёт новый сегмент для записи части видеопотока в формате MPEGTS
    /// </summary>
    private void CreateOutputSegment()
    {
        ThrowIfDisposed();

        //_logger.LogInformation("Creating segment {SegmentNumber:D3}", _segmentNumber);

        CreateOutputFormatContext();
        CreateAndSetupVideoStream();

        if (_audioStreamIndex != -1)
        {
            CreateAndSetupAudioStream();
        }

        SetupOutputFormatFlags();
        InitializeSegmentBuffer();
        InitializeSegmentPts();
    }

    /// <summary>
    /// Создает контекст формата для выходного потока
    /// </summary>
    private void CreateOutputFormatContext()
    {
        fixed (AVFormatContext** ppOutputContext = &_outputFormatContext)
        {
            if (ffmpeg.avformat_alloc_output_context2(ppOutputContext, null,
                OutputStreamFormat.MpegTs.ToString().ToLowerInvariant(), null) < 0)
            {
                throw new ApplicationException("Failed to create output segment context.");
            }
        }
    }

    /// <summary>
    /// Создает и настраивает видеопоток для выходного сегмента
    /// </summary>
    private void CreateAndSetupVideoStream()
    {
        _outputVideoStream = ffmpeg.avformat_new_stream(_outputFormatContext, null);
        if (_outputVideoStream == null)
        {
            throw new ApplicationException("Failed to create stream for segment.");
        }

        // Копируем параметры кодека из входного видеопотока в выходной
        if (ffmpeg.avcodec_parameters_copy(_outputVideoStream->codecpar,
            _inputFormatContext->streams[_videoStreamIndex]->codecpar) < 0)
        {
            throw new ApplicationException("Error copying codec parameters.");
        }

        // Устанавливаем параметры тайминга и фреймрейта
        _outputVideoStream->time_base = _inputFormatContext->streams[_videoStreamIndex]->time_base;
    }

    /// <summary>
    /// Создает и настраивает аудиопоток для выходного сегмента
    /// </summary>
    private void CreateAndSetupAudioStream()
    {
        var outputAudioStream = ffmpeg.avformat_new_stream(_outputFormatContext, null);
        if (outputAudioStream == null)
        {
            throw new ApplicationException("Failed to create audio stream for segment.");
        }

        // Копируем параметры аудио кодека
        if (ffmpeg.avcodec_parameters_copy(outputAudioStream->codecpar,
                                           _inputFormatContext->streams[_audioStreamIndex]->codecpar) < 0)
        {
            throw new ApplicationException("Error copying audio codec parameters.");
        }

        outputAudioStream->time_base = _inputFormatContext->streams[_audioStreamIndex]->time_base;
        outputAudioStream->codecpar->codec_tag = 0;
    }

    /// <summary>
    /// Устанавливает флаги для выходного формата
    /// </summary>
    private void SetupOutputFormatFlags()
    {
        _outputFormatContext->flags |= ffmpeg.AVFMT_FLAG_IGNDTS;
        _outputFormatContext->flags |= ffmpeg.AVFMT_FLAG_GENPTS;
        _outputFormatContext->flags &= ~ffmpeg.AVFMT_FLAG_NOBUFFER;

        // Устанавливаем максимальное время задержки
        _outputFormatContext->max_delay = _outputMaxDelay;
    }

    /// <summary>
    /// Инициализирует буфер для сегмента и записывает заголовок
    /// </summary>
    private void InitializeSegmentBuffer()
    {
        // Инициализируем буфер в памяти для хранения данных сегмента
        _currentSegmentBuffer = new MemoryOutputBuffer();
        _outputFormatContext->pb = _currentSegmentBuffer.AvioContext;

        // Записываем заголовок MPEGTS
        if (ffmpeg.avformat_write_header(_outputFormatContext, null) < 0)
        {
            throw new ApplicationException("Error writing segment header.");
        }
    }

    /// <summary>
    /// Инициализирует временные метки для нового сегмента
    /// </summary>
    private void InitializeSegmentPts()
    {
        // Инициализируем временную метку только для первого сегмента
        if (_segmentNumber == 0)
        {
            _segmentStartPts = -1;
        }
    }

    #endregion

    /// <summary>
    /// Закрывает текущий сегмент и подготавливает данные для обработки
    /// </summary>
    private void CloseOutputSegment()
    {
        ThrowIfDisposed();

        if (_outputFormatContext != null && _currentSegmentBuffer != null)
        {
            // Записываем завершающие данные сегмента
            ffmpeg.av_write_trailer(_outputFormatContext);

            _currentSegmentBuffer.Stream.Position = 0;

            WriteMicroSegment(_currentSegmentBuffer);

            _currentSegmentBuffer.Dispose();
            _currentSegmentBuffer = null;

            ffmpeg.avformat_free_context(_outputFormatContext);
            _outputFormatContext = null;
            _segmentNumber++;
        }
    }

    /// <summary>
    /// Обработка готового микросегмента видео
    /// </summary>
    /// <param name="outputBuffer">Буфер памяти с данными сегмента в формате MPEGTS</param>
    private void WriteMicroSegment(MemoryOutputBuffer outputBuffer)
    {
        ThrowIfDisposed();

        var duration = (_lastPts - _segmentStartPts) * _videoTimeBase.num / (double)_videoTimeBase.den;

        var currentTime = _firstPts >= 0
            ? _streamStartTime.AddSeconds((double)_segmentStartPts * _videoTimeBase.num / _videoTimeBase.den)
            : _streamStartTime;

        //_logger.LogInformation(
        //    "MicroSegment {SegmentNumber:D3} | Start time: {Time} | Duration: {Duration:F3} sec | Size: {Size} bytes | Time base: {TimeBaseNum}/{TimeBaseDen}",
        //    _segmentNumber,
        //    currentTime.ToString("yyyy-MM-dd HH:mm:ss.fff"),
        //    duration,
        //    outputBuffer.Stream.Length,
        //    _videoTimeBase.num,
        //    _videoTimeBase.den
        //);

        outputBuffer.Stream.Position = 0;

        if (!_streamChannel!.Writer.TryWrite((outputBuffer, currentTime, duration)))
        {
            _logger.LogError("Failed to write micro segment to channel");
        }
    }
}
