
using FluentValidation;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Reactive;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.UserDevice;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.Shared;
namespace Teslametrics.App.Web.Features.Main.Cameras.CameraSettingDialog;

public partial class CameraSettingsDialog : IAsyncDisposable
{
    #region [Types]
    private class OnvifSettings
    {
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;

        public OnvifSettings(string host, int port, string username, string password)
        {
            Host = host;
            Port = port;
            Username = username;
            Password = password;
        }

        public OnvifSettings()
        {
        }
    }

    private class Model
    {
        private bool _onvifEnabled;

        public Guid Id { get; set; }
        public string Name { get; set; }
        public Guid? Quota { get; set; }
        public string ArchiveUri { get; set; }
        public string PublicUri { get; set; }
        public string ViewUri { get; set; }
        public TimeSpan TimeZone { get; set; }
        public Coordinates? Coordinates { get; set; }
        public bool AutoStart { get; set; }

        public bool OnvifEnabled
        {
            get => _onvifEnabled;
            set
            {
                if (_onvifEnabled != value)
                {
                    _onvifEnabled = value;
                    Onvif = value ? new OnvifSettings() : null;
                }
            }
        }

        public OnvifSettings? Onvif { get; set; }

        public Model(Guid id, string name, Guid? quota, string archiveUri, string publicUri, string viewUri, TimeSpan timeZone, Coordinates? coordinates, bool autoStart, bool onvifEnabled, OnvifSettings? onvif)
        {
            Id = id;
            Name = name;
            Quota = quota;
            ArchiveUri = archiveUri;
            PublicUri = publicUri;
            ViewUri = viewUri;
            TimeZone = timeZone;
            Coordinates = coordinates;
            AutoStart = autoStart;
            OnvifEnabled = onvifEnabled;
            Onvif = onvif;
        }
    }

    private class Validator : BaseFluentValidator<Model>
    {
        private static string? ExtractHostAndPort(string uri)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(uri)) return null;

                var rtspUri = new Uri(uri);
                if (rtspUri.Scheme != "rtsp") return null;

                return rtspUri.Authority;
            }
            catch
            {
                return null;
            }
        }

        private static bool ValidateUriHostMatch(string currentUri, Model model)
        {
            if (string.IsNullOrWhiteSpace(currentUri))
                return true;

            var currentHost = ExtractHostAndPort(currentUri);
            if (currentHost == null)
                return true;

            var allUris = new[] { model.ArchiveUri, model.PublicUri, model.ViewUri };
            var allHosts = allUris
                .Where(uri => !string.IsNullOrWhiteSpace(uri))
                .Select(ExtractHostAndPort)
                .Where(h => h != null);

            return allHosts.All(h => h == currentHost);
        }

        public Validator()
        {
            RuleFor(model => model.Name)
                .Length(3, 60).WithMessage("наименование должно быть длиной от 3 до 60 символов");

            RuleFor(model => model.ArchiveUri)
                .NotEmpty().WithMessage("Поле должно быть заполнено")
                .Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out var parsed) && parsed.Scheme == "rtsp")
                .WithMessage("Некорректный формат RTSP URI")
                .Must((model, uri) => ValidateUriHostMatch(uri, model))
                .WithMessage("Все RTSP URI должны иметь одинаковый хост и порт");

            RuleFor(model => model.ViewUri)
                .NotEmpty().WithMessage("Поле должно быть заполнено")
                .Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out var parsed) && parsed.Scheme == "rtsp")
                .WithMessage("Некорректный формат RTSP URI")
                .Must((model, uri) => ValidateUriHostMatch(uri, model))
                .WithMessage("Все RTSP URI должны иметь одинаковый хост и порт");

            RuleFor(model => model.PublicUri)
                .NotEmpty().WithMessage("Поле должно быть заполнено")
                .Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out var parsed) && parsed.Scheme == "rtsp")
                .WithMessage("Некорректный формат RTSP URI")
                .Must((model, uri) => ValidateUriHostMatch(uri, model))
                .WithMessage("Все RTSP URI должны иметь одинаковый хост и порт");

            // RuleFor(model => model.Preset)
            //     .NotEmpty().WithMessage("Поле должно быть заполнено");

            RuleFor(model => model.TimeZone)
                .NotNull().WithMessage("Поле должно быть заполнено")
                .NotEmpty().WithMessage("Поле должно быть заполнено");

            // ONVIF validation rules - only when ONVIF is enabled
            When(model => model.OnvifEnabled && model.Onvif is not null, () =>
            {
                RuleFor(model => model.Onvif!.Host)
                    .NotNull().WithMessage("Поле должно быть заполнено")
                    .NotEmpty().WithMessage("Адрес ONVIF должен быть заполнен");

                RuleFor(model => model.Onvif!.Port)
                    .InclusiveBetween(1, 65535).WithMessage("Порт должен быть в диапазоне от 1 до 65535");

                RuleFor(model => model.Onvif!.Username)
                    .NotNull().WithMessage("Поле должно быть заполнено")
                    .NotEmpty().WithMessage("Логин ONVIF должен быть заполнен");

                RuleFor(model => model.Onvif!.Password)
                    .NotNull().WithMessage("Поле должно быть заполнено")
                    .NotEmpty().WithMessage("Пароль ONVIF должен быть заполнен");
            });
        }
    }
    #endregion

    #region [Form]
    [Inject]
    private IWebHostEnvironment _env { get; set; } = null!;

    private MudBlazor.MudTextField<string>? _archiveUriRef;
    private MudBlazor.MudTextField<string>? _publicUriRef;
    private MudBlazor.MudTextField<string>? _viewUriRef;

    private bool _isValid => _model is null ? false : _validator.Validate(_model).IsValid;
    private Model? _model;
    private readonly Validator _validator = new();
    #endregion

    #region [Dialog]
    private DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Small, NoHeader = true, BackdropClick = false };
    private bool _isVisible;

    [Inject]
    private IUserDeviceService _userDeviceService { get; set; } = null!;
    #endregion

    #region [Camera]
    private Guid _id;
    private Guid? _organizationId;
    private bool _subscribing;
    private CameraStatus _cameraStatus;
    private GetCameraUseCase.Response? _camera;
    private SubscribeCameraUseCase.Response? _subscriptionResult;
    private SubscribeCameraStatusUseCase.Response? _statusSubscriptionResponse = null;
    #endregion

    public async ValueTask DisposeAsync()
    {
        await UnsubscribeStatusAsync();

        GC.SuppressFinalize(this);
    }

    protected override void OnInitialized()
    {
        if (_userDeviceService.IsMobile)
        {
            _dialogOptions = new()
            {
                CloseOnEscapeKey = true,
                FullWidth = true,
                FullScreen = true,
                NoHeader = true
            };
        }

        CompositeDisposable.Add(EventSystem.Subscribe<CameraEditEto>(OnEventHandler));

        base.OnInitialized();
    }


    private async Task FetchAsync()
    {
        await SetLoadingAsync(true);
        try
        {
            _camera = await ScopeFactory.MediatorSend(new GetCameraUseCase.Query(_id));
        }
        catch (Exception ex)
        {
            _camera = null;
            Logger.LogError(ex, ex.Message);
            Snackbar.Add($"Не удалось получить камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
        }
        await SetLoadingAsync(false);

        if (_camera is null) return;

        switch (_camera.Result)
        {
            case GetCameraUseCase.Result.Success:
                OnvifSettings? onvifSettings = _camera.OnvifEnabled && _camera.OnvifOptions is not null ? new(_camera.OnvifOptions.Host, _camera.OnvifOptions.Port, _camera.OnvifOptions.Username, _camera.OnvifOptions.Password) : null;
                _model = new Model(
                    _camera.Id,
                    _camera.Name,
                    _camera.QuotaId,
                    _camera.ArchiveUri.Trim(),
                    _camera.PublicUri.Trim(),
                    _camera.ViewUri.Trim(),
                    _camera.TimeZone,
                    _camera.Coordinates,
                    _camera.AutoStart,
                    _camera.OnvifEnabled,
                    onvifSettings
                );
                _cameraStatus = _camera.CameraStatus;

                await SubscribeStatusAsync();
                break;
            case GetCameraUseCase.Result.CameraNotFound:
                Snackbar.Add("Не удалось получить камеру. Возможно камера уже удалена.", MudBlazor.Severity.Warning);
                await CancelAsync();
                break;
            case GetCameraUseCase.Result.ValidationError:
                Snackbar.Add("Не удалось получить камеру из-за ошибки валидации.", MudBlazor.Severity.Error);
                await CancelAsync();
                break;
            case GetCameraUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraSettingsDialog), nameof(GetCameraUseCase));
                Snackbar.Add($"Не удалось получить камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraSettingsDialog), nameof(GetCameraUseCase), _camera.Result);
                Snackbar.Add($"Не удалось получить камеру из-за ошибки: {_camera.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task SubscribeAsync()
    {
        try
        {
            Unsubscribe();
            await SetSubscribingAsync(true);
            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _id));
        }
        catch (Exception ex)
        {
            _subscriptionResult = null;
            Snackbar.Add($"Не удалось подписаться на события камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetSubscribingAsync(false);

        if (_subscriptionResult is null) return;

        switch (_subscriptionResult.Result)
        {
            case SubscribeCameraUseCase.Result.Success:
                CompositeDisposable.Add(_subscriptionResult.Subscription!);
                break;
            case SubscribeCameraUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
                break;
            case SubscribeCameraUseCase.Result.CameraNotFound:
                Snackbar.Add("Ошибка подписки на события камеры", MudBlazor.Severity.Error);
                break;
            case SubscribeCameraUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraSettingsDialog), nameof(SubscribeCameraUseCase));
                Snackbar.Add($"Не удалось подписаться на события камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraSettingsDialog), nameof(SubscribeCameraUseCase), _subscriptionResult.Result);
                Snackbar.Add($"Не удалось подписаться на события камеры из-за ошибки: {_subscriptionResult.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }
    protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [Actions]
    private async Task CancelAsync()
    {
        _isVisible = false;
        _model = null;
        _camera = null;
        _cameraStatus = CameraStatus.Stopped;
        Unsubscribe();
        await UnsubscribeStatusAsync();
    }
    private Task RefreshAsync() => FetchAsync();
    private async Task SubmitAsync()
    {
        if (_model is null)
        {
            Snackbar.Add("Камера не найдена. Невозможно применить изменения!", MudBlazor.Severity.Error);
            return;
        }

        UpdateCameraUseCase.Response? response = null;
        await SetLoadingAsync(true);
        try
        {
            UpdateCameraUseCase.Command.OnvifSettings? onvifSettings = null;
            if (_model.OnvifEnabled && _model.Onvif is not null)
            {
                onvifSettings = new(_model.Onvif.Host, _model.Onvif.Port, _model.Onvif.Username, _model.Onvif.Password);
            }
            response = await ScopeFactory.MediatorSend(new UpdateCameraUseCase.Command(
                _model.Id,
                _model.Quota!.Value,
                _model.Name,
                _model.TimeZone,
                _model.Coordinates,
                _model.ArchiveUri.Trim(),
                _model.ViewUri.Trim(),
                _model.PublicUri.Trim(),
                _model.AutoStart,
                _model.OnvifEnabled,
                onvifSettings
            ));
        }
        catch (Exception ex)
        {
            response = null;
            Logger.LogError(ex, ex.Message);
            Snackbar.Add($"Не удалось сохранить изменения из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
        }

        if (response is null) return;
        switch (response.Result)
        {
            case UpdateCameraUseCase.Result.Success:
                Snackbar.Add("Камера успешно сохранена", MudBlazor.Severity.Success);
                await CancelAsync();
                break;
            case UpdateCameraUseCase.Result.ValidationError:
                Snackbar.Add("Не удалось сохранить изменения из-за ошибки валидации. Проверьте правильность заполнения полей", MudBlazor.Severity.Error);
                break;
            case UpdateCameraUseCase.Result.CameraNotFound:
                Snackbar.Add("Не удалось сохранить изменения, так как камера не найдена. Возможно камера уже удалена.", MudBlazor.Severity.Warning);
                await CancelAsync();
                break;
            case UpdateCameraUseCase.Result.CameraPresetNotFound:
                Snackbar.Add("Не удалось сохранить изменения. Пресет не найден.", MudBlazor.Severity.Error);
                await CancelAsync();
                break;
            case UpdateCameraUseCase.Result.CameraQuotaNotFound:
                Snackbar.Add("Не удалось сохранить изменения. Квота не найдена. Выберите другую квоту", MudBlazor.Severity.Error);
                await CancelAsync();
                break;

            case UpdateCameraUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraSettingsDialog), nameof(UpdateCameraUseCase));
                Snackbar.Add($"Не удалось сохранить изменения из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;

            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraSettingsDialog), nameof(UpdateCameraUseCase), response.Result);
                Snackbar.Add($"Не удалось сохранить изменения из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
    #endregion

    private async Task SubscribeStatusAsync()
    {
        await SetLoadingAsync(true);
        await UnsubscribeStatusAsync();

        SubscribeCameraStatusUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new SubscribeCameraStatusUseCase.Request(Observer.Create<SubscribeCameraStatusUseCase.StatusChangedEvent>(StatusUpdated, OnError), _id));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Не удалось подписаться на события статуса камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, "Failed to subscribe to camera preview events");
        }

        await SetLoadingAsync(false);
        if (response is null) return;

        switch (response.Result)
        {
            case SubscribeCameraStatusUseCase.Result.Success:
                break;
            case SubscribeCameraStatusUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события статуса камеры", MudBlazor.Severity.Error);
                break;
            case SubscribeCameraStatusUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraSettingsDialog), nameof(SubscribeCameraStatusUseCase));
                Snackbar.Add($"Не удалось получить подписку на события статуса камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraSettingsDialog), nameof(SubscribeCameraStatusUseCase), response.Result);
                Snackbar.Add($"Не удалось получить подписку на события статуса камеры из-за ошибки", MudBlazor.Severity.Error);
                break;
        }
    }
    private async Task UnsubscribeStatusAsync()
    {
        if (_statusSubscriptionResponse?.Subscription is null)
        {
            return;
        }

        await _statusSubscriptionResponse.Subscription.DisposeAsync();
    }

    #region [Event Handlers]
    private async void OnEventHandler(CameraEditEto eto)
    {
        _id = eto.CameraId;
        _organizationId = eto.OrganizationId;
        _isVisible = true;
        await FetchAsync();
        await SubscribeAsync();
        await UpdateViewAsync();
    }

    private async void OnAppEventHandler(object appEvent)
    {
        switch (appEvent)
        {
            case SubscribeCameraUseCase.UpdatedEvent updatedEto:
                Snackbar.Add("Камера была обновлена", MudBlazor.Severity.Warning, config =>
                {
                    config.Action = "Обновить форму";
                    config.ActionColor = Color.Primary;
                    config.OnClick = async snackbar =>
                    {
                        await FetchAsync();
                        await UpdateViewAsync();
                    };
                });
                break;

            case SubscribeCameraUseCase.DeletedEvent deletedEto:
                Snackbar.Add("Камера была удалена", MudBlazor.Severity.Warning);
                await CancelAsync();
                break;

            default:
                Snackbar.Add("Было получено непредвиденное событие.", MudBlazor.Severity.Warning);
                Logger.LogWarning("Unexpected event in {UseCase}: {Event}", nameof(SubscribeCameraUseCase), nameof(appEvent));
                break;
        }
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
        Logger.LogError(exc, exc.Message);
    }

    public void StatusUpdated(SubscribeCameraStatusUseCase.StatusChangedEvent @event)
    {
        InvokeAsync(() =>
        {
            _cameraStatus = @event.Status;
            StateHasChanged();
        });
    }

    private void OnViewUriChanged(string uri)
    {
        if (_model is not null)
        {
            _model.ViewUri = uri;
            _archiveUriRef?.Validate();
            _publicUriRef?.Validate();
        }
    }

    private void OnArchiveUriChanged(string uri)
    {
        if (_model is not null)
        {
            _model.ArchiveUri = uri;
            _viewUriRef?.Validate();
            _publicUriRef?.Validate();
        }
    }

    private void OnPublicUriChanged(string uri)
    {
        if (_model is not null)
        {
            _model.PublicUri = uri;
            _viewUriRef?.Validate();
            _archiveUriRef?.Validate();
        }
    }
    #endregion [Event Handlers]
}