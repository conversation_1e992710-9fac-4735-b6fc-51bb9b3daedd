let doorOpeningPlot = null;
let customTooltip = null;

/* ---------- 4. Layout (две оси Y, сетка, легенда, прозрачный фон) ---------- */
const rootStyles = getComputedStyle(document.documentElement);
const gridClr =
	rootStyles.getPropertyValue("--mud-palette-lines-default").trim() ||
	"#e0e0e0";

const layout = {
	margin: {
		//  ← вся «белая рамка» внутри SVG
		t: 40, // сверху (под заголовок)
		r: 40,
		b: 40,
		l: 40,
		pad: 0, // доп‑зазор, по умолчанию 0
	},
	xaxis: {
		gridcolor: gridClr,
		gridwidth: 1,
		tickangle: 0,
	},
	yaxis: {
		// левая ось
		title: "Количество",
		rangemode: "tozero",
		gridcolor: gridClr,
		gridwidth: 1,
		tickformat: ",d", // ➌ только целые
		dtick: 5, // шаг сетки
	},

	yaxis2: {
		// правая ось
		title: "мин",
		overlaying: "y",
		side: "right",
		rangemode: "tozero",
		gridcolor: "transparent", // без второй сетки
	},
	bargap: 0.25,
	showlegend: false,
	paper_bgcolor: "transparent", // прозрачный фон «листа»
	plot_bgcolor: "transparent", // прозрачный фон области графика
};

/**
 * Создает кастомную HTML подсказку
 */
function createCustomTooltip() {
	if (customTooltip) return customTooltip;

	const tmpl = document.getElementById("doorTooltipTemplate");
	customTooltip = tmpl.content.firstElementChild.cloneNode(true);
	document.body.appendChild(customTooltip);
	return customTooltip;
}

/**
 * Показывает кастомную подсказку
 * @param {{left:number, top:number}} position  // ISO‑дату Blazor отдаёт строкой
 * @param {{date: string, count: number, averageTime: number}} data
 */
function showCustomTooltip(position, data) {
	const MARGIN = 8; // «воздушный зазор» от краёв

	const tooltip = createCustomTooltip();

	/* ---- заполняем контент (тот же код, что у вас) ---- */
	const dateElement = tooltip.querySelector(".tooltip-date");
	const doorValueTextElement = tooltip.querySelector(
		".door-opened b.tooltip-value-text"
	);
	const timeValueTextElement = tooltip.querySelector(
		".average-time b.tooltip-value-text"
	);

	const date = new Date(data.date); // ISO‑строка → Date
	const formatted = Object.fromEntries(
		new Intl.DateTimeFormat("ru-RU", {
			day: "2-digit",
			month: "long",
			year: "numeric",
			weekday: "long",
		})
			.formatToParts(date)
			.map((p) => [p.type, p.value])
	);
	dateElement.textContent = `${formatted.day} ${formatted.month} ${formatted.year} ${formatted.weekday}`;
	doorValueTextElement.textContent = data.count;
	timeValueTextElement.textContent = data.averageTime.toFixed(2);
	/* --------------------------------------------------- */

	const { offsetWidth: w, offsetHeight: h } = tooltip;
	// Границы видимой области (с учётом прокрутки страницы)
	const viewportLeft = window.scrollX + MARGIN;
	const viewportTop = window.scrollY + MARGIN;
	const viewportRight =
		window.scrollX + document.documentElement.clientWidth - MARGIN;
	const viewportBottom =
		window.scrollY + document.documentElement.clientHeight - MARGIN;

	// Начальные координаты (где «хотелось» показать)
	let left = position.left;
	let top = position.top;

	/* 2. горизонтальная коррекция */
	if (left + w > viewportRight) {
		// не помещается справа
		left = viewportRight - w;
	}
	if (left < viewportLeft) {
		// ушёл за левую границу
		left = viewportLeft;
	}

	/* 3. вертикальная коррекция
          если не хватает места снизу, пробуем показать над точкой */
	if (top + h > viewportBottom) {
		top = position.top - h; // над курсором/точкой
		if (top < viewportTop) {
			// всё ещё не влазит − прижимаем к верху
			top = viewportBottom - h;
		}
	}
	if (top < viewportTop) {
		top = viewportTop;
	}

	tooltip.style.left = `${left}px`;
	tooltip.style.top = `${top}px`;
	tooltip.style.visibility = "visible";
	tooltip.style.opacity = "1";
}

/**
 * Скрывает кастомную подсказку
 */
function hideCustomTooltip() {
	if (customTooltip) {
		customTooltip.style.visibility = "hidden";
		customTooltip.style.opacity = "0";
	}
}

export function initDoorOpeningsChart(data) {
	// Сносим прежний, если был
	if (doorOpeningPlot) {
		Plotly.purge("humidity-chart"); // удаляем старый график из DOM
		doorOpeningPlot = null;
	}

	/* ---------- 0. «Нет данных» ---------- */
	if (!data || data.length === 0) {
		Plotly.newPlot(
			"door-openings-chart",
			[], // пустой data‑массив
			{
				paper_bgcolor: "rgba(0,0,0,0)",
				plot_bgcolor: "rgba(0,0,0,0)",
				xaxis: { visible: false },
				yaxis: { visible: false },
				annotations: [
					{
						text: "Нет данных для отображения",
						xref: "paper",
						yref: "paper",
						x: 0.5,
						y: 0.5,
						showarrow: false,
						font: { size: 16, color: "#888" },
					},
				],
				margin: { t: 40, r: 10, b: 40, l: 48 },
			},
			{ displayModeBar: false }
		).then((plot) => (doorOpeningPlot = plot));
		return;
	}

	/* ---------- 1. Исходные массивы ---------- */
	const x = data.map((o) => o.date); // ISO‑строки
	const yCount = data.map((o) => o.count);
	const yAvg = data.map((o) => o.averageTime);

	/* ---------- 2. Bar‑trace ---------- */

	const bar = {
		x,
		y: yCount,
		customdata: data, // любые данные
		type: "bar",
		name: "Кол-во",
		marker: { color: "#1A5A65" },
		hoverinfo: "none",
	};

	const line = {
		x,
		y: yAvg,
		customdata: data, // любые данные
		type: "scatter",
		mode: "lines+markers",
		yaxis: "y2",
		line: { color: "#33C7F8" },
		marker: { size: 11, color: "#33C7F8" },
		name: "Среднее время",
		hoverinfo: "none",
	};

	/* ---------- 3. Ось 'date' с локализованным выводом %a ---------- */
	layout.xaxis = {
		type: "date",
		dtick: "D1", // тик каждый день
		tickformat: "%d.%m", // ось: 21.07
	};

	/* ---------- 5. Рендер ---------- */
	Plotly.newPlot("door-openings-chart", [bar, line], layout, {
		responsive: true,
		displayModeBar: false,
		locale: "ru",
	}).then((plot) => (doorOpeningPlot = plot));

	// Добавляем обработчики событий для кастомных подсказок

	const plotDiv = document.getElementById("door-openings-chart");
	// Обработчик наведения
	plotDiv.on("plotly_hover", function (eventData) {
		if (eventData.points && eventData.points.length > 0) {
			const data = eventData.points[0].customdata;

			if (data) {
				showCustomTooltip(
					{ left: eventData.event.x, top: eventData.event.y },
					data
				);
			}
		}
	});

	// Обработчик ухода мыши
	plotDiv.on("plotly_unhover", function () {
		hideCustomTooltip();
	});

	// Дополнительный обработчик для скрытия при движении мыши вне графика
	plotDiv.addEventListener("mouseleave", function () {
		hideCustomTooltip();
	});
}

/**
 * Очищает ресурсы компонента
 */
export function cleanupChart() {
	// Скрываем и удаляем кастомную подсказку
	if (customTooltip) {
		hideCustomTooltip();
		if (customTooltip.parentNode) {
			customTooltip.parentNode.removeChild(customTooltip);
		}
		customTooltip = null;
	}
}
