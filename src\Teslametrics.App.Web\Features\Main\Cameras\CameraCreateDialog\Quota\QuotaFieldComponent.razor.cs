using System.Linq.Expressions;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraCreateDialog.Quota;

public partial class QuotaFieldComponent
{
    private Preset? _selected;
    private record Preset(Guid id, string Title);

    #region [Parameters]
    [Parameter]
    public Variant variant { get; set; } = Variant.Outlined;

    [Parameter]
    public Margin Margin { get; set; } = Margin.Normal;

    [Parameter]
    public string Label { get; set; } = string.Empty;

    [Parameter]
    public Guid? Selected { get; set; }

    [Parameter]
    public EventCallback<Guid?> SelectedChanged { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid OrganizationId { get; set; }
    #endregion

    protected override async Task OnParametersSetAsync()
    {
        if (Selected != _selected?.id)
        {
            await FetchQuotaAsync();
        }
        await base.OnParametersSetAsync();
    }

    private async Task FetchQuotaAsync()
    {
        if (Selected is null) return;

        GetQuotaUseCase.Response? response = null;
        await SetLoadingAsync(true);
        try
        {
            response = await ScopeFactory.MediatorSend(new GetQuotaUseCase.Query(Selected.Value));
        }
        catch (Exception exc)
        {
            response = null;
            Logger.LogError(exc, exc.Message);
            Snackbar.Add("Не удалось получить квоту из-за непредвиденной ошибки.", Severity.Error);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case GetQuotaUseCase.Result.Success:
                _selected = new Preset(response.Id, response.Name);
                break;
            case GetQuotaUseCase.Result.QuotaNotFound:
                Snackbar.Add("Не удалось получить квоту. Возможно квота уже удалена.", Severity.Warning);
                break;
            case GetQuotaUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при запросе квоты.", Severity.Error);
                break;
            case GetQuotaUseCase.Result.Unknown:
                Logger.LogError("Unexpected result in {Component}, {UseCase}: {Result}", nameof(QuotaFieldComponent), nameof(GetQuotaUseCase), response.Result);
                Snackbar.Add("Не удалось получить квоту из-за непредвиденной ошибки.", Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected result in {Component}, {UseCase}: {Result}", nameof(QuotaFieldComponent), nameof(GetQuotaUseCase), response.Result);
                Snackbar.Add("Не удалось получить квоту из-за непредвиденной ошибки.", Severity.Error);
                break;
        }
    }


    private async Task<IEnumerable<Preset>> SearchAsync(string value, CancellationToken token)
    {
        GetQuotaListUseCase.Response? response = null;
        await SetLoadingAsync(true);
        try
        {
            response = await ScopeFactory.MediatorSend(new GetQuotaListUseCase.Query(OrganizationId, 0, 25, value));
        }
        catch (Exception exc)
        {
            response = null;
            Logger.LogError(exc, exc.Message);
            Snackbar.Add("Не удалось получить список пресетов камеры из-за непредвиденной ошибки.", Severity.Error);
        }

        if (response is null) return [];

        switch (response.Result)
        {
            case GetQuotaListUseCase.Result.Success:
                return response.Items.Select(item => new Preset(item.Id, item.Name));
            case GetQuotaListUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при запросе квот камеры.", Severity.Error);
                break;
            case GetQuotaListUseCase.Result.Unknown:
                Logger.LogError("Unexpected result in {Component}, {UseCase}: {Result}", nameof(QuotaFieldComponent), nameof(GetQuotaListUseCase), response.Result);
                Snackbar.Add("Не удалось получить список квот из-за непредвиденной ошибки.", Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected result in {Component}, {UseCase}: {Result}", nameof(QuotaFieldComponent), nameof(GetQuotaListUseCase), response.Result);
                Snackbar.Add("Не удалось получить список квот из-за непредвиденной ошибки.", Severity.Error);
                break;
        }

        return [];
    }

    #region
    private async Task OnSelectedValudeChanged(Preset? preset)
    {
        _selected = preset;
        await SelectedChanged.InvokeAsync(preset?.id);
    }
    #endregion
}
