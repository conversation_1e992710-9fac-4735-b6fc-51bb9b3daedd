using Microsoft.EntityFrameworkCore;
using Teslametrics.Core.Abstractions;
using Teslametrics.Core.Domain.Incidents;
using Teslametrics.Shared;

namespace Teslametrics.Core.Services.Persistence;

public class IncidentRepository : BaseRepository<IncidentAggregate>, IIncidentRepository
{
    public IncidentRepository(CommandAppDbContext dbContext)
        : base(dbContext)
    {
    }

    public async Task<List<IncidentAggregate>> GetActiveIncidentsAsync(string? city = null,
                                                                       string? building = null,
                                                                       int? floor = null,
                                                                       string? room = null,
                                                                       string? device = null,
                                                                       IncidentType? incidentType = null,
                                                                       CancellationToken cancellationToken = default)
    {
        var query = DbContext.Set<IncidentAggregate>()
            .AsTracking()
            .Where(i => i.ResolvedAt == null);

        if (!string.IsNullOrEmpty(city))
            query = query.Where(i => i.City == city);

        if (!string.IsNullOrEmpty(building))
            query = query.Where(i => i.Building == building);

        if (floor is not null)
            query = query.Where(i => i.Floor == floor);

        if (!string.IsNullOrEmpty(room))
            query = query.Where(i => i.Room == room);

        if (!string.IsNullOrEmpty(device))
            query = query.Where(i => i.Device == device);

        if (incidentType.HasValue)
            query = query.Where(i => i.IncidentType == incidentType.Value);

        return await query
            .OrderByDescending(i => i.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public Task<IncidentAggregate?> GetLastUnresolvedIncidentByTypeAsync(string topic, IncidentType incidentType, CancellationToken cancellationToken = default) =>
        DbContext.Set<IncidentAggregate>()
            .Where(i => i.Topic == topic && i.ResolvedAt == null && i.IncidentType == incidentType)
            .OrderByDescending(i => i.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);
}