﻿@using System.Text;
@using CsvHelper.Configuration;
@using System.Globalization;
@using CsvHelper;

@typeparam TImportModel where TImportModel : class
@typeparam TImportMap where TImportMap : ClassMap<TImportModel>

<MudStack>
    @if (_fileContentBytes is null)
    {
        <FileUploadComponent UploadFileButtonText="@LocaleStrings.UploadFileButtonText"
                             UploadFileContainerText="@LocaleStrings.UploadFileContainerText"
                             OnFileRead="OnFileRead"
                             OnReset="OnFileReset" />
    }
    @if (_fileContentBytes is not null)
    {
        <MudGrid Class="align-end">
            <MudItem Class="ml-n4">
                <MudIconButton Icon="@Icons.Material.Filled.ArrowBackIosNew"
                               OnClick="OnFileReset"/>
            </MudItem>
            <MudItem xs="3">
                <MudSelect T="string"
                           Disabled="false"
                           ReadOnly="false"
                           Value="@_delimeter"
                           ValueChanged="DelimeterChanged"
                           Label="@LocaleStrings.DelimeterSelectLabel"
                           Immediate="true">
                    <MudSelectItem T="string"
                                   Value="@(",")">,</MudSelectItem>
                    <MudSelectItem T="string"
                                   Value="@(";")">;</MudSelectItem>
                </MudSelect>
            </MudItem>
        </MudGrid>

        <CsvHeaderMapper Headers="@_headers"
                         Fields="@Fields"
                         Mapping="_currentMapping"
                         MappingChanged="OnMappingChanged"
                         AllowDuplicateHeaders="true"
                         LocaleStrings="@LocaleStrings.Mapping" />


        <MudStack Row="true"
                  Justify="Justify.FlexEnd">
            <MudButton Disabled="!IsValid || !_currentMapping.Any()"
                       OnClick="Import">@LocaleStrings.ConfirmHeaderMapping</MudButton>
        </MudStack>
    }
</MudStack>
