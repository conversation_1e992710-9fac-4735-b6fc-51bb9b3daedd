using Dapper;
using static Dapper.SqlBuilder;

namespace Teslametrics.Shared;

/// <summary>
/// Represents a SQL query builder with fluent interface
/// </summary>
public class SqlQueryBuilder
{
    private readonly SqlBuilder _builder;

    private SqlQueryBuilder()
    {
        _builder = new SqlBuilder();
    }

    /// <summary>
    /// Creates a new instance of SqlQueryBuilder
    /// </summary>
    public static SqlQueryBuilder Create() => new();

    /// <summary>
    /// Adds a SELECT clause with optional column alias
    /// </summary>
    /// <param name="column">Column name or expression</param>
    /// <param name="alias">Optional alias for the column</param>
    public SqlQueryBuilder Select(string column, string? alias = null)
    {
        _builder.Select(alias == null ? column : $"{column} AS {alias}");
        return this;
    }

    /// <summary>
    /// Adds a WHERE clause with raw SQL condition
    /// </summary>
    /// <param name="sql">Raw SQL condition</param>
    /// <param name="parameters">Optional parameters for the condition</param>
    public SqlQueryBuilder Where(string sql, object? parameters = null)
    {
        _builder.Where(sql, parameters);
        return this;
    }

    /// <summary>
    /// Adds a WHERE clause with comparison for IsNull or IsNotNull operators
    /// </summary>
    /// <param name="first">First operand (usually column name)</param>
    /// <param name="op">Comparison operator (must be IsNull or IsNotNull)</param>
    public SqlQueryBuilder Where(string first, SqlOperator op)
    {
        if (op == SqlOperator.IsNull || op == SqlOperator.IsNotNull)
        {
            _builder.Where($"{first} {GetOperatorString(op)}");
            return this;
        }

        throw new ArgumentException($"This overload can only be used with IsNull or IsNotNull operators. Operator provided: {op}");
    }

    /// <summary>
    /// Adds a WHERE clause with comparison
    /// </summary>
    /// <param name="first">First operand (usually column name)</param>
    /// <param name="second">Second operand (value or parameter name)</param>
    /// <param name="op">Comparison operator</param>
    /// <param name="parameters">Optional parameters for the condition</param>
    public SqlQueryBuilder Where(string first, string second, SqlOperator op, object? parameters = null)
    {
        if (op == SqlOperator.IsNull || op == SqlOperator.IsNotNull)
        {
            return Where(first, op);
        }
        else if (op == SqlOperator.Any)
        {
            _builder.Where($"{first} {GetOperatorString(op)}({second})", parameters);
        }
        else
        {
            _builder.Where($"{first} {GetOperatorString(op)} {second}", parameters);
        }
        return this;
    }

    /// <summary>
    /// Conditionally adds a WHERE clause with comparison for IsNull or IsNotNull operators
    /// </summary>
    /// <param name="condition">Condition that determines if the WHERE clause should be added</param>
    /// <param name="first">First operand (usually column name)</param>
    /// <param name="op">Comparison operator (must be IsNull or IsNotNull)</param>
    public SqlQueryBuilder WhereIf(bool condition, string first, SqlOperator op)
    {
        if (condition)
        {
            return Where(first, op);
        }
        return this;
    }

    /// <summary>
    /// Conditionally adds a WHERE clause with comparison
    /// </summary>
    /// <param name="condition">Condition that determines if the WHERE clause should be added</param>
    /// <param name="first">First operand (usually column name)</param>
    /// <param name="second">Second operand (value or parameter name)</param>
    /// <param name="op">Comparison operator</param>
    /// <param name="parameters">Optional parameters for the condition</param>
    public SqlQueryBuilder WhereIf(bool condition, string first, string second, SqlOperator op, object? parameters = null)
    {
        if (condition)
        {
            if (op == SqlOperator.IsNull || op == SqlOperator.IsNotNull)
            {
                return WhereIf(condition, first, op);
            }
            else
            {
                _builder.Where($"{first} {GetOperatorString(op)} {second}", parameters);
            }
        }
        return this;
    }

    /// <summary>
    /// Conditionally adds a WHERE clause with raw SQL condition
    /// </summary>
    /// <param name="condition">Condition that determines if the WHERE clause should be added</param>
    /// <param name="sql">Raw SQL condition</param>
    /// <param name="parameters">Optional parameters for the condition</param>
    public SqlQueryBuilder WhereIf(bool condition, string sql, object? parameters = null)
    {
        if (condition)
        {
            _builder.Where(sql, parameters);
        }
        return this;
    }

    /// <summary>
    /// Adds an INNER JOIN clause
    /// </summary>
    /// <param name="table">Table name to join</param>
    /// <param name="first">First join condition operand</param>
    /// <param name="second">Second join condition operand</param>
    /// <param name="op">Join condition operator</param>
    /// <param name="parameters">Optional parameters for the join condition</param>
    public SqlQueryBuilder InnerJoin(string table, string first, string second, SqlOperator op, object? parameters = null)
    {
        _builder.InnerJoin($"{table} ON {first} {GetOperatorString(op)} {second}", parameters);
        return this;
    }

    /// <summary>
    /// Adds an INNER JOIN clause with raw SQL
    /// </summary>
    /// <param name="sql">Raw SQL join clause</param>
    /// <param name="parameters">Optional parameters for the join condition</param>
    public SqlQueryBuilder InnerJoin(string sql, object? parameters = null)
    {
        _builder.InnerJoin(sql, parameters);
        return this;
    }

    /// <summary>
    /// Adds a LEFT JOIN clause
    /// </summary>
    /// <param name="table">Table name to join</param>
    /// <param name="first">First join condition operand</param>
    /// <param name="second">Second join condition operand</param>
    /// <param name="op">Join condition operator</param>
    /// <param name="parameters">Optional parameters for the join condition</param>
    public SqlQueryBuilder LeftJoin(string table, string first, string second, SqlOperator op, object? parameters = null)
    {
        _builder.LeftJoin($"{table} ON {first} {GetOperatorString(op)} {second}", parameters);
        return this;
    }

    /// <summary>
    /// Adds a LEFT JOIN clause with raw SQL
    /// </summary>
    /// <param name="sql">Raw SQL join clause</param>
    /// <param name="parameters">Optional parameters for the join condition</param>
    public SqlQueryBuilder LeftJoin(string sql, object? parameters = null)
    {
        _builder.LeftJoin(sql, parameters);
        return this;
    }

    /// <summary>
    /// Adds a HAVING clause with raw SQL condition
    /// </summary>
    /// <param name="sql">Raw SQL condition</param>
    /// <param name="parameters">Optional parameters for the condition</param>
    public SqlQueryBuilder Having(string sql, object? parameters = null)
    {
        _builder.Having(sql, parameters);
        return this;
    }

    /// <summary>
    /// Adds a HAVING clause with comparison for IsNull or IsNotNull operators
    /// </summary>
    /// <param name="first">First operand (usually column name or aggregate function)</param>
    /// <param name="op">Comparison operator (must be IsNull or IsNotNull)</param>
    public SqlQueryBuilder Having(string first, SqlOperator op)
    {
        if (op == SqlOperator.IsNull || op == SqlOperator.IsNotNull)
        {
            _builder.Having($"{first} {GetOperatorString(op)}");
            return this;
        }

        throw new ArgumentException($"This overload can only be used with IsNull or IsNotNull operators. Operator provided: {op}");
    }

    /// <summary>
    /// Adds a HAVING clause with comparison
    /// </summary>
    /// <param name="first">First operand (usually column name or aggregate function)</param>
    /// <param name="second">Second operand (value or parameter name)</param>
    /// <param name="op">Comparison operator</param>
    /// <param name="parameters">Optional parameters for the condition</param>
    public SqlQueryBuilder Having(string first, string second, SqlOperator op, object? parameters = null)
    {
        if (op == SqlOperator.IsNull || op == SqlOperator.IsNotNull)
        {
            return Having(first, op);
        }
        else
        {
            _builder.Having($"{first} {GetOperatorString(op)} {second}", parameters);
        }
        return this;
    }

    /// <summary>
    /// Conditionally adds a HAVING clause with comparison for IsNull or IsNotNull operators
    /// </summary>
    /// <param name="condition">Condition that determines if the HAVING clause should be added</param>
    /// <param name="first">First operand (usually column name or aggregate function)</param>
    /// <param name="op">Comparison operator (must be IsNull or IsNotNull)</param>
    public SqlQueryBuilder HavingIf(bool condition, string first, SqlOperator op)
    {
        if (condition)
        {
            return Having(first, op);
        }
        return this;
    }

    /// <summary>
    /// Conditionally adds a HAVING clause with comparison
    /// </summary>
    /// <param name="condition">Condition that determines if the HAVING clause should be added</param>
    /// <param name="first">First operand (usually column name or aggregate function)</param>
    /// <param name="second">Second operand (value or parameter name)</param>
    /// <param name="op">Comparison operator</param>
    /// <param name="parameters">Optional parameters for the condition</param>
    public SqlQueryBuilder HavingIf(bool condition, string first, string second, SqlOperator op, object? parameters = null)
    {
        if (condition)
        {
            if (op == SqlOperator.IsNull || op == SqlOperator.IsNotNull)
            {
                return HavingIf(condition, first, op);
            }
            else
            {
                _builder.Having($"{first} {GetOperatorString(op)} {second}", parameters);
            }
        }
        return this;
    }

    /// <summary>
    /// Conditionally adds a HAVING clause with raw SQL condition
    /// </summary>
    /// <param name="condition">Condition that determines if the HAVING clause should be added</param>
    /// <param name="sql">Raw SQL condition</param>
    /// <param name="parameters">Optional parameters for the condition</param>
    public SqlQueryBuilder HavingIf(bool condition, string sql, object? parameters = null)
    {
        if (condition)
        {
            _builder.Having(sql, parameters);
        }
        return this;
    }

    /// <summary>
    /// Adds a GROUP BY clause
    /// </summary>
    /// <param name="columns">Columns to group by</param>
    public SqlQueryBuilder GroupBy(params string[] columns)
    {
        _builder.GroupBy(string.Join(", ", columns));
        return this;
    }

    /// <summary>
    /// Adds an ORDER BY clause for multiple columns
    /// </summary>
    /// <param name="orders">Array of tuples containing column name and sort direction</param>
    public SqlQueryBuilder OrderBy(params (string Column, OrderDirection Direction)[] orders)
    {
        if (orders.Length == 0)
            return this;

        var orderClauses = orders.Select(o => $"{o.Column} {(o.Direction == OrderDirection.Ascending ? "ASC" : "DESC")}");
        _builder.OrderBy(string.Join(", ", orderClauses));
        return this;
    }

    /// <summary>
    /// Adds an ORDER BY clause for a single column
    /// </summary>
    /// <param name="column">Column name to order by</param>
    /// <param name="direction">Order direction (ASC or DESC)</param>
    public SqlQueryBuilder OrderBy(string column, OrderDirection direction = OrderDirection.Ascending)
    {
        return OrderBy((column, direction));
    }

    /// <summary>
    /// Conditionally adds an ORDER BY clause for multiple columns
    /// </summary>
    /// <param name="condition">Condition that determines if the ORDER BY clause should be added</param>
    /// <param name="orders">Array of tuples containing column name and sort direction</param>
    public SqlQueryBuilder OrderByIf(bool condition, params (string Column, OrderDirection Direction)[] orders)
    {
        if (condition)
        {
            OrderBy(orders);
        }
        return this;
    }

    /// <summary>
    /// Conditionally adds an ORDER BY clause for a single column
    /// </summary>
    /// <param name="condition">Condition that determines if the ORDER BY clause should be added</param>
    /// <param name="column">Column name to order by</param>
    /// <param name="direction">Order direction (ASC or DESC)</param>
    public SqlQueryBuilder OrderByIf(bool condition, string column, OrderDirection direction = OrderDirection.Ascending)
    {
        return OrderByIf(condition, (column, direction));
    }

    /// <summary>
    /// Builds the final SQL template
    /// </summary>
    /// <param name="queryType">Type of query (Standard or Paginated)</param>
    /// <param name="from">FROM clause content</param>
    /// <param name="rowSelection">Row selection strategy (AllRows or UniqueRows)</param>
    /// <param name="select">Optional explicit SELECT columns</param>
    /// <param name="parameters">Optional parameters for the template</param>
    public Template Build(QueryType queryType, string from, RowSelection rowSelection, string[]? select = null, object? parameters = null)
    {
        var template = queryType == QueryType.Paginated
            ? GetPagedTemplate(from, rowSelection, select)
            : GetTemplate(from, rowSelection, select);

        return _builder.AddTemplate(template, parameters);
    }

    private static string GetTemplate(string from, RowSelection rowSelection, string[]? select = null) =>
        $"SELECT {(rowSelection == RowSelection.UniqueRows ? "DISTINCT " : string.Empty)}{(select is not null && select.Length > 0 ? string.Join(" , ", select) + "\n" : "/**select**/")}FROM {from} /**innerjoin**//**leftjoin**//**where**//**groupby**//**having**//**orderby**/";

    private static string GetPagedTemplate(string from, RowSelection rowSelection, string[]? select = null) =>
        GetTemplate(from, rowSelection, select) + " LIMIT :limit OFFSET :offset";

    private static string GetOperatorString(SqlOperator op) => op switch
    {
        SqlOperator.Equals => "=",
        SqlOperator.NotEquals => "<>",
        SqlOperator.GreaterThan => ">",
        SqlOperator.GreaterThanOrEqual => ">=",
        SqlOperator.LessThan => "<",
        SqlOperator.LessThanOrEqual => "<=",
        SqlOperator.Like => "LIKE",
        SqlOperator.In => "IN",
        SqlOperator.NotIn => "NOT IN",
        SqlOperator.IsNull => "IS NULL",
        SqlOperator.IsNotNull => "IS NOT NULL",
        SqlOperator.Any => "= ANY",
        _ => throw new ArgumentException($"Unsupported operator: {op}")
    };
}

/// <summary>
/// SQL comparison operators
/// </summary>
public enum SqlOperator
{
    Equals,
    NotEquals,
    GreaterThan,
    GreaterThanOrEqual,
    LessThan,
    LessThanOrEqual,
    Like,
    In,
    NotIn,
    IsNull,
    IsNotNull,
    Any
}

/// <summary>
/// Defines the type of SQL query to build
/// </summary>
public enum QueryType
{
    /// <summary>
    /// Regular SQL query without pagination
    /// </summary>
    Standard,

    /// <summary>
    /// SQL query with pagination using LIMIT and OFFSET
    /// </summary>
    Paginated
}

/// <summary>
/// Defines how rows should be selected in the query
/// </summary>
public enum RowSelection
{
    /// <summary>
    /// Returns all rows including duplicates
    /// </summary>
    AllRows,

    /// <summary>
    /// Returns only unique rows
    /// </summary>
    UniqueRows
}

/// <summary>
/// Defines the order direction for ORDER BY clause
/// </summary>
public enum OrderDirection
{
    /// <summary>
    /// Ascending order (ASC)
    /// </summary>
    Ascending,

    /// <summary>
    /// Descending order (DESC)
    /// </summary>
    Descending
}
