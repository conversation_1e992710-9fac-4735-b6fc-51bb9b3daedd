:global(.test_tooltip)
{
	z-index: 1600 !important;
}

.test_tooltip:not(::deep)
{
	z-index: 1600 !important;
}

:root .test_tooltip
{
	z-index: 1600 !important;
}

/* Field with error icon layout */
.field-with-error-icon {
    display: flex;
    align-items: center;
    gap: 4px;
}

.field-with-error-icon .mud-input {
    flex: 1;
}

.error-icon-container {
    display: flex;
    align-items: center;
}
.error-icon-container::before {
    content: " ";
    width: 1px;
    height: 100%;
    min-height: 24px;
    margin: 0 8px;
}
.error-icon-container::before:has(>.error-tooltip)
{
    background: var(--mud-palette-divider-light);
}

/* Validation error icon and tooltip styles */
.validation-error-icon {
    margin-left: 4px;
    cursor: pointer;
}

.validation-error-tooltip {
    z-index: 1600 !important;
}

.validation-error-content {
    max-width: 300px;
    padding: 8px;
}

.validation-error-message {
    margin-bottom: 4px;
    line-height: 1.4;
}

.validation-error-message:last-child {
    margin-bottom: 0;
}