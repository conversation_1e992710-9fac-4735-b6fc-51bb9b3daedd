﻿@inherits InteractiveBaseComponent
<div>
    @if (_items.Any())
    {
        <MudBreadcrumbs Items="_items"
                        Class="breadcrumbs" />
    }
    @if (IsLoading && !_items.Any())
    {
        <MudSkeleton Width="@(_rnd.Next(10, 35) + "%")"
                     Height="30px" />
    }
</div>
@code {
    private static Random _rnd = new Random();

    [Parameter]
    [EditorRequired]
    public Guid CityId { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid BuildingId { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid FloorId { get; set; }

    [Parameter]
    public Guid? RoomId { get; set; }

    private List<MudBlazor.BreadcrumbItem> _items = new();

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        await UpdateBreadcrumbsAsync();
    }

    private async Task UpdateBreadcrumbsAsync()
    {
        await SetLoadingAsync(true);
        GetBreadcrumbsUseCase.Response? response = null;
        _items.Clear();
        try
        {
            response = await ScopeFactory.MediatorSend(new GetBreadcrumbsUseCase.Query(CityId, BuildingId, FloorId, RoomId));
        }
        catch (Exception ex)
        {
            response = null;
            Logger.LogError(ex, "Error while fetching breadcrumbs");
        }

        await SetLoadingAsync(false);
        if (response is null) return;

        switch (response.Result)
        {
            case GetBreadcrumbsUseCase.Result.Success:
                _items.Clear();

                if (response.Floor is not null)
                {
                    _items.Add(new($"{response.Floor.Name} Этаж",
                    href: $"/devices?cityId={CityId}&buildingId={BuildingId}&floorId={FloorId}",
                    disabled: false));
                }

                if (response.Room is not null)
                {
                    _items.Add(new(response.Room.Name ?? "Комната",
                    href: $"/devices?cityId={CityId}&buildingId={BuildingId}&floorId={FloorId}&roomId={RoomId}",
                    disabled: true));
                }
                break;
            case GetBreadcrumbsUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(BreadcrumbsComponent), nameof(GetBreadcrumbsUseCase));
                Snackbar.Add($"Не удалось получить данные из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(BreadcrumbsComponent), nameof(GetBreadcrumbsUseCase), response.Result);
                Snackbar.Add($"Не удалось получить данные из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }

    }
}
