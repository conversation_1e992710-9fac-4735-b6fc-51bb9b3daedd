using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using System.Text.Json;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraSettingDialog.Quota;

public static class GetQuotaListUseCase
{
    public record Query(Guid OrganizationId, int Offset, int Limit, string Filter) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Item> Items { get; init; }

        public int TotalCount { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Item> items, int totalCount)
        {
            Items = items;
            TotalCount = totalCount;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Items = [];
            TotalCount = 0;
        }

        public record Item(Guid Id, string Name, StreamConfig? ArchiveStreamConfig, StreamConfig? ViewStreamConfig, StreamConfig? PublicStreamConfig);

        public record StreamConfig(Resolution Resolution, VideoCodec VideoCodec, FrameRate FrameRate, SceneDynamic SceneDynamic, AudioCodec AudioCodec);
    }
    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.OrganizationId).NotEmpty();
            RuleFor(q => q.Offset).GreaterThanOrEqualTo(0);
            RuleFor(q => q.Limit).GreaterThan(0);
            RuleFor(q => q.Filter).MaximumLength(60);
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var builder = SqlQueryBuilder.Create()
                .LeftJoin(Db.Presets.Table, Db.Presets.Props.Id, Db.CameraQuotas.Props.PresetId, SqlOperator.Equals)
                .LeftJoin(Db.Cameras.Table, Db.CameraQuotas.Props.Id, Db.Cameras.Props.QuotaId, SqlOperator.Equals)
                .Where(Db.CameraQuotas.Props.OrganizationId, ":OrganizationId", SqlOperator.Equals, new { request.OrganizationId })
                .WhereIf(!string.IsNullOrEmpty(request.Filter), Db.CameraQuotas.Props.Name, "CONCAT('%', :Filter, '%')", SqlOperator.Like, new { request.Filter })
                .GroupBy(Db.CameraQuotas.Props.Id, Db.CameraQuotas.Props.Limit, Db.Presets.Props.ArchiveStreamConfig, Db.Presets.Props.ViewStreamConfig, Db.Presets.Props.PublicStreamConfig)
                .Having($"{Db.CameraQuotas.Props.Limit} = -1 OR COUNT(DISTINCT {Db.Cameras.Props.Id}) < {Db.CameraQuotas.Props.Limit}");

            var countTemplate = builder.Build(QueryType.Standard, Db.CameraQuotas.Table, RowSelection.AllRows, [$"COUNT(DISTINCT {Db.CameraQuotas.Props.Id})"]);
            var totalCount = await _dbConnection.ExecuteScalarAsync<int>(countTemplate.RawSql, countTemplate.Parameters);

            var selectTemplate = builder.Build(QueryType.Paginated,
                                               Db.CameraQuotas.Table,
                                               RowSelection.AllRows,
                                               [
                                                   Db.CameraQuotas.Props.Id,
                                                   Db.CameraQuotas.Props.Name,
                                                   Db.Presets.Props.ArchiveStreamConfig,
                                                   Db.Presets.Props.ViewStreamConfig,
                                                   Db.Presets.Props.PublicStreamConfig
                                               ],
                                               new { request.Limit, request.Offset });
            var presets = await _dbConnection.QueryAsync<CameraQuotaModel>(selectTemplate.RawSql, selectTemplate.Parameters);

            return new Response(presets.Select(u => new Response.Item(u.Id,
                                                                      u.Name,
                                                                      ToStreamConfigResponse(u.ArchiveStreamConfig),
                                                                      ToStreamConfigResponse(u.ViewStreamConfig),
                                                                      ToStreamConfigResponse(u.PublicStreamConfig))).ToList(), totalCount);
        }

        private static Response.StreamConfig? ToStreamConfigResponse(string streamConfig)
        {
            if (string.IsNullOrEmpty(streamConfig))
            {
                return null;
            }

            var streamConfigModel = JsonSerializer.Deserialize<Response.StreamConfig>(streamConfig);
            return new Response.StreamConfig(streamConfigModel!.Resolution, streamConfigModel.VideoCodec, streamConfigModel.FrameRate, streamConfigModel.SceneDynamic, streamConfigModel.AudioCodec);
        }
    }

    public record CameraQuotaModel(Guid Id, string Name, string ArchiveStreamConfig, string ViewStreamConfig, string PublicStreamConfig);
}