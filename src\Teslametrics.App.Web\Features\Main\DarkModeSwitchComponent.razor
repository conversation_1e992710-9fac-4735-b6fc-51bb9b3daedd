﻿<div class="d_contents">
    <MudPaper Elevation="0"
              Outlined="false"
              Class="d-flex align-center justify-center paper">
        <MudIconButton OnClick="SetLightMode"
                       Icon="@TeslaIcons.Theme.LightMode"
                       Color="Color.Primary"
                       Variant="@(!IsDarkMode ? Variant.Filled : Variant.Text)" />

        <MudIconButton OnClick="SetDarkMode"
                       Icon="@TeslaIcons.Theme.DarkMode"
                       Color="Color.Primary"
                       Variant="@(IsDarkMode ? Variant.Filled : Variant.Text)" />
    </MudPaper>
</div>

@code {
    [Parameter]
    public bool IsDarkMode { get; set; }

    [Parameter]
    public EventCallback<bool> IsDarkModeChanged { get; set; }

    private async Task SetDarkMode()
    {
        if (IsDarkMode) return;
        IsDarkMode = true;
        await IsDarkModeChanged.InvokeAsync(IsDarkMode);
    }

    private async Task SetLightMode()
    {
        if (!IsDarkMode) return;

        IsDarkMode = false;
        await IsDarkModeChanged.InvokeAsync(IsDarkMode);
    }
}
