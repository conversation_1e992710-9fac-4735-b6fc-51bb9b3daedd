﻿@using System.Text
@inherits BaseNodeComponent<RoomElement>

<div class="absolute room_node @(IsSelected ? "selected" : "")"
     style="left: @ToCssValue(Element.X)px; top: @ToCssValue(Element.Y)px;"
     @onclick="SelectAsync"
     data-id="@Element.Id">
    @if (_bounds is not null)
    {
        <svg class="area-svg"
             viewBox="@($"{_bounds.MinX} {_bounds.MinY} {Math.Round(_bounds.Width)} {Math.Round(_bounds.Height)}")"
             width="@Math.Round(_bounds.Width)"
             height="@Math.Round(_bounds.Height)"
             xmlns="http://www.w3.org/2000/svg">
            <path d="@CreateRoundedPolygonPath(4)"
                  class="area-polygon" />
            <text x="10"
                  y="20"
                  class="area-title">@Element.Title</text>
        </svg>
    }
</div>

@code {
    private SvgBounds? _bounds;

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        _bounds = CalculateBounds(Element.Points);
    }

    private string CreateRoundedPolygonPath(double radius)
    {
        var path = new StringBuilder();
        int count = Element.Points.Count;

        for (int i = 0; i < count; i++)
        {
            var p0 = Element.Points[(i - 1 + count) % count]; // предыдущая
            var p1 = Element.Points[i]; // текущая
            var p2 = Element.Points[(i + 1) % count]; // следующая

            // Векторы
            var v1 = Normalize(p1.X - p0.X, p1.Y - p0.Y);
            var v2 = Normalize(p2.X - p1.X, p2.Y - p1.Y);

            // Точки начала и конца дуги
            var start = new AreaPoint(p1.X - v1.X * radius, p1.Y - v1.Y * radius);
            var end = new AreaPoint(p1.X + v2.X * radius, p1.Y + v2.Y * radius);

            if (i == 0)
                path.AppendFormat("M {0} {1} ", Math.Floor(start.X), Math.Floor(start.Y));
            else
                path.AppendFormat("L {0} {1} ", Math.Floor(start.X), Math.Floor(start.Y));

            // Угол и направление дуги
            int sweepFlag = CrossProductZ(v1, v2) < 0 ? 0 : 1;

            path.AppendFormat("A {0} {0} 0 0 {1} {2} {3} ", radius, sweepFlag, Math.Floor(end.X), Math.Floor(end.Y));
        }

        path.Append('Z'); // замыкаем

        return path.ToString();
    }

    private static (double X, double Y) Normalize(double x, double y)
    {
        double len = Math.Sqrt(x * x + y * y);
        return (x / len, y / len);
    }

    private static double CrossProductZ((double X, double Y) v1, (double X, double Y) v2)
    {
        return v1.X * v2.Y - v1.Y * v2.X;
    }

    private class SvgBounds
    {
        public double MinX { get; set; }
        public double MinY { get; set; }
        public double MaxX { get; set; }
        public double MaxY { get; set; }

        public double Width => MaxX - MinX;
        public double Height => MaxY - MinY;
    }

    private static SvgBounds CalculateBounds(List<AreaPoint> points)
    {
        if (points == null || points.Count == 0)
            throw new ArgumentException("Points list is empty.");

        double minX = points.Min(p => p.X);
        double maxX = points.Max(p => p.X);
        double minY = points.Min(p => p.Y);
        double maxY = points.Max(p => p.Y);

        return new SvgBounds
        {
            MinX = minX,
            MinY = minY,
            MaxX = maxX,
            MaxY = maxY
        };
    }
}
