﻿using Microsoft.Extensions.DependencyInjection;

namespace Orleans.Providers
{
    internal static class LiveMessageBodySerializerFactory<TSerializer>
        where TSerializer : class, ILiveMessageBodySerializer
    {
        private static readonly Lazy<ObjectFactory> ObjectFactory = new Lazy<ObjectFactory>(
            () => ActivatorUtilities.CreateFactory(
                typeof(TSerializer),
                Type.EmptyTypes));

        public static TSerializer GetOrCreateSerializer(IServiceProvider serviceProvider)
        {
            return serviceProvider.GetService<TSerializer>() ??
                   (TSerializer) ObjectFactory.Value(serviceProvider, null);
        }
    }
}