﻿@using Teslametrics.App.Web.Features.Main.Cameras.CameraCreateDialog.PublicAccessView
@using Teslametrics.App.Web.Features.Main.Cameras.CameraCreateDialog.Quota
@inherits InteractiveBaseComponent
<MudDialog @bind-Visible="_isVisible"
           Class="CameraSettingsDialog"
           ActionsClass="align-center justify-center"
           ContentClass="content"
           Options="_dialogOptions"
           Style="height: 70vh;">
    <DialogContent>
        <div class="title d-flex">
            <MudText Typo="Typo.h6">Создание камеры</MudText>
            <MudSpacer />
            <MudIconButton OnClick="Cancel"
                           Icon="@Icons.Material.Outlined.Close" />
        </div>

        @if (_model is not null)
        {
            <MudForm Model="_model"
                     Validation="_validator"
                     Class="form mx-n4 px-4"
                     Spacing="5"
                     OverrideFieldValidation="true"
                     UserAttributes="@(new Dictionary<string, object>() { { "autocomplete", "off" }, { "aria-autocomplete", "none" }, { "role", "presentation" } })">
                <MudStack Spacing="3">
                    <MudText Typo="Typo.subtitle2">Описание камеры</MudText>

                    <MudText Typo="Typo.caption">Имя камеры</MudText>
                    <MudTextField @bind-Value="_model.Name"
                                  For="() => _model.Name"
                                  Clearable="true"
                                  Immediate="true"
                                  Placeholder="Наименование"
                                  RequiredError="Данное поле обязательно"
                                  Required="true"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense" />
                </MudStack>

                <MudStack Spacing="3">
                    <MudText Typo="Typo.subtitle2">Параметры камеры</MudText>

                    <MudText Typo="Typo.caption">Наименование квоты</MudText>
                    <QuotaFieldComponent @bind-Selected="@_model.Quota"
                                         OrganizationId="@_model.OrganizationId"
                                         Variant="Variant.Outlined"
                                         Margin="Margin.Dense" />

                    <MudText Typo="Typo.caption">Ссылка на архивный поток</MudText>
                    <MudTextField T="string"
                                  Value="_model.ArchiveUri"
                                  ValueChanged="OnArchiveUriChanged"
                                  For="() => _model.ArchiveUri"
                                  InputType="InputType.Text"
                                  Immediate="true"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense"
                                  @ref="@_archiveUriRef" />

                    <MudText Typo="Typo.caption">Ссылка на поток для видов</MudText>
                    <MudTextField T="string"
                                  Value="_model.ViewUri"
                                  ValueChanged="OnViewUriChanged"
                                  For="() => _model.ViewUri"
                                  InputType="InputType.Text"
                                  Immediate="true"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense"
                                  @ref="@_viewUriRef" />

                    <MudText Typo="Typo.caption">Ссылка на публичный поток</MudText>
                    <MudTextField T="string"
                                  Value="_model.PublicUri"
                                  ValueChanged="OnPublicUriChanged"
                                  For="() => _model.PublicUri"
                                  InputType="InputType.Text"
                                  Immediate="true"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense"
                                  @ref="@_publicUriRef" />

                    <MudCheckBox @bind-Value="_model.AutoStart"
                                 Label="Автозапуск при перезапуске системы"
                                 Color="Color.Primary"
                                 Class="ml-n3" />
                    <MudCheckBox T="bool"
                                 @bind-Value="_model.OnvifEnabled"
                                 Label="Включить ONVIF"
                                 Color="Color.Primary"
                                 Class="ml-n3" />
                </MudStack>

                @if (_model.OnvifEnabled)
                {
                    <MudStack Spacing="3">
                        <MudText Typo="Typo.subtitle2">Подключение к ONVIF</MudText>
                        <MudText Typo="Typo.caption">Логин</MudText>
                        <MudTextField T="string"
                                      @bind-Value="_model.Onvif!.Username"
                                      For="() => _model.Onvif!.Username"
                                      Immediate="true"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />

                        <MudText Typo="Typo.caption">Пароль</MudText>
                        <PasswordFieldComponent @bind-Value="_model.Onvif!.Password"
                                                For="() => _model.Onvif!.Password"
                                                Immediate="true"
                                                Variant="Variant.Outlined"
                                                Margin="Margin.Dense" />

                        <MudText Typo="Typo.caption">Адрес ONVIF</MudText>
                        <MudTextField T="string"
                                      @bind-Value="_model.Onvif!.Host"
                                      For="() => _model.Onvif!.Host"
                                      Immediate="true"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />

                        <MudText Typo="Typo.caption">Порт</MudText>
                        <MudNumericField T="int"
                                         @bind-Value="_model.Onvif!.Port"
                                         For="() => _model.Onvif!.Port"
                                         Immediate="true"
                                         Variant="Variant.Outlined"
                                         Margin="Margin.Dense" />
                    </MudStack>
                }

                <MudStack Spacing="3">
                    <MudText Typo="Typo.subtitle2">Местоположение камеры</MudText>

                    <MudText Typo="Typo.caption">Часовой пояс</MudText>
                    <TimeZoneSelector @bind-TimeZone="@_model.TimeZone"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />

                    <YandexMaps @bind-Coordinates="@_model.Coordinates"
                                Width="100%"
                                ReadOnly="false"
                                For="() => _model.Coordinates"
                                Class="rounded-b overflow-hidden"
                                Height="400px" />
                </MudStack>
            </MudForm>
        }
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel"
                   Variant="Variant.Outlined">Отмена</MudButton>
        <MudButton OnClick="SubmitAsync"
                   Color="Color.Primary"
                   Variant="Variant.Filled"
                   Disabled="@(!_isValid)">Сохранить</MudButton>
    </DialogActions>
</MudDialog>

<style>
    .CameraSettingsDialog .content {
        display: grid;
        grid-template-rows: auto 1fr;
        overflow: hidden;
    }

    .CameraSettingsDialog form {
        overflow: auto;
    }
</style>
