using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Incidents.Filter.Room;

public partial class RoomSelectorComponent
{
    private GetRoomListUseCase.Response.Item? _selected;

    [Parameter]
    public Guid? CityId { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }

    [Parameter]
    public Guid? FloorId { get; set; }

    [Parameter]
    public Guid? RoomId { get; set; }
    [Parameter]
    public EventCallback<Guid?> RoomIdChanged { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        if (_selected?.Id != RoomId)
        {
            _selected = null;
            await GetDataAsync();
        }
        await base.OnParametersSetAsync();
    }

    private async Task GetDataAsync()
    {
        if (!CityId.HasValue || !BuildingId.HasValue || !FloorId.HasValue || !RoomId.HasValue) return;

        GetRoomUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetRoomUseCase.Query(CityId.Value, BuildingId.Value, FloorId.Value, RoomId.Value));
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching cities");
            Snackbar.Add("Не удалось получить комнату из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case GetRoomUseCase.Result.Success:
                _selected = new(CityId.Value, response.Name);
                break;
            case GetRoomUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении комнаты", MudBlazor.Severity.Error);
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(RoomSelectorComponent), nameof(GetRoomUseCase));
                break;
            case GetRoomUseCase.Result.CityNotFound:
                Snackbar.Add("Город не найден", MudBlazor.Severity.Error);

                RoomId = null;
                if (RoomIdChanged.HasDelegate)
                    await RoomIdChanged.InvokeAsync(BuildingId);

                break;

            case GetRoomUseCase.Result.BuildingNotFound:
                Snackbar.Add("Здание не найдено", MudBlazor.Severity.Error);

                RoomId = null;
                if (RoomIdChanged.HasDelegate)
                    await RoomIdChanged.InvokeAsync(BuildingId);
                break;

            case GetRoomUseCase.Result.FloorNotFound:
                Snackbar.Add("Этаж не найден", MudBlazor.Severity.Error);

                RoomId = null;
                if (RoomIdChanged.HasDelegate)
                    await RoomIdChanged.InvokeAsync(BuildingId);
                break;

            case GetRoomUseCase.Result.RoomNotFound:
                Snackbar.Add("Комната не найдена", MudBlazor.Severity.Error);

                RoomId = null;
                if (RoomIdChanged.HasDelegate)
                    await RoomIdChanged.InvokeAsync(BuildingId);
                break;

            case GetRoomUseCase.Result.Unknown:
                Snackbar.Add("Не удалось получить комнату из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(RoomSelectorComponent), nameof(GetRoomUseCase));
                break;

            default:
                Snackbar.Add("Неизвестная ошибка при получении комнаты", MudBlazor.Severity.Error);
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(RoomSelectorComponent), nameof(GetRoomUseCase), response.Result);
                break;
        }
    }

    private async Task ValueChanged(GetRoomListUseCase.Response.Item? item)
    {
        _selected = item;
        await RoomIdChanged.InvokeAsync(item?.Id);
    }

    private async Task<IEnumerable<GetRoomListUseCase.Response.Item>> SearchAsync(string value, CancellationToken token)
    {
        if (!CityId.HasValue || !BuildingId.HasValue || !FloorId.HasValue) return [];

        GetRoomListUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetRoomListUseCase.Query(CityId!.Value, BuildingId!.Value, FloorId!.Value, value), token);
        }
        catch (TaskCanceledException)
        {
            // Search was canceled, just return empty result
            return Enumerable.Empty<GetRoomListUseCase.Response.Item>();
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching cities");
            Snackbar.Add("Не удалось получить список этажей из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
            return Enumerable.Empty<GetRoomListUseCase.Response.Item>();
        }

        if (response.Result == GetRoomListUseCase.Result.Success)
            return response.Items;

        if (response.Result == GetRoomListUseCase.Result.ValidationError)
            Snackbar.Add("Ошибка валидации при получении списка городов", MudBlazor.Severity.Error);
        else
            Snackbar.Add("Не удалось получить список городов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);

        return Enumerable.Empty<GetRoomListUseCase.Response.Item>();
    }
}
