using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Shared;
using Teslametrics.Core.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.Cameras.FolderDeleteDialog;

public static class GetFolderUseCase
{
    public record Query(Guid Id) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public string Name { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id, string name)
        {
            Id = id;
            Name = name;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Id = Guid.Empty;
            Name = string.Empty;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        FolderNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.Id).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Folders.Props.Id)
                .Select(Db.Folders.Props.Name)
                .Where(Db.Folders.Props.Id, ":Id", SqlOperator.Equals, new { request.Id })
                .Build(QueryType.Standard, Db.Folders.Table, RowSelection.AllRows);

            var folder = await _dbConnection.QuerySingleOrDefaultAsync<FolderModel>(template.RawSql, template.Parameters);
            if (folder is null)
            {
                return new Response(Result.FolderNotFound);
            }

            return new Response(folder.Id, folder.Name);
        }
    }

    public record FolderModel(Guid Id, string Name);
}