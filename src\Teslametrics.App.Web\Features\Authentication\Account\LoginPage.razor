@using Microsoft.AspNetCore.Antiforgery
@using Microsoft.AspNetCore.Components.Forms
@using Teslametrics.App.Web.Features.Authentication.Account.LoginForceChangePassword
@using Teslametrics.App.Web.Features.Authentication.Account.TwoFactorAuthentication.Login
@using Teslametrics.App.Web.Features.Authentication.Account.TwoFactorAuthentication.Setup
@layout AuthLayout
@attribute [RequireAntiforgeryToken]
@attribute [Route(RouteConstants.LogInPage)]
@attribute [Microsoft.AspNetCore.Mvc.ResponseCache(Duration = 0, Location =
Microsoft.AspNetCore.Mvc.ResponseCacheLocation.None, NoStore = true)]
@inherits InteractiveBaseComponent<LoginPage>
<PageTitle>Multimonitor | Аутентификация</PageTitle>
<div style="display: contents">
	<MudContainer MaxWidth="MaxWidth.Large" Class="mud-height-full container">
		<img class="ad laptop" src="/multimonitor_login_laptop_ad.svg">
		<div class="items_container mud-width-full mud-height-full">
			<header class="d-flex align-start">
				<img src="/logo.svg" alt="Multimonitor" height="30rem;" loading="lazy">
			</header>

			<div class="main_card">
				@switch(_authStep)
				{
					case AuthStep.Login:
						<MudText Typo="Typo.h2" Class="mb-8">
							Добро пожаловать, введите данные для входа в систему
						</MudText>
						<MudForm Model="@Model" Validation="_signInValidator.ValidateValue" @bind-IsValid="_signInIsValid" Spacing="6" ValidationDelay="0" @onkeydown="OnKeyDown">
							<MudStack Spacing="3">
								<MudTextField T="string" @bind-Value="Model.UserName" Margin="Margin.Dense" For="() => Model.UserName" Immediate="true" Placeholder="Введите логин" Required="true" ShrinkLabel="false"  Variant="Variant.Outlined" @ref="_loginFormLoginRef" />
								<PasswordFieldComponent @bind-Value="Model.Password" Margin="Margin.Dense" For="() => Model.Password" Immediate="true" Placeholder="Введите пароль" Required="true" ShrinkLabel="false" Variant="Variant.Outlined" @ref="_loginFormPasswordRef" />
							</MudStack>
							<MudButton OnClick="Submit" Disabled="!_signInIsValid" Variant="Variant.Filled" Color="Color.Primary" FullWidth="true" Style="height: 48px;">Войти</MudButton>
						</MudForm>
						break;
					case AuthStep.TwoFa:
						<TwoFaLoginForm Password="@Model.Password" UserName="@Model.UserName" />
						break;
					case AuthStep.TwoFaSetup:
						<TwoFaSetupLoginForm Password="@Model.Password" UserName="@Model.UserName" />
						break;
					case AuthStep.ForcedPasswordChange:
						<LoginForceChangePassword Password="@Model.Password" UserName="@Model.UserName" />
						break;
				}
				<MudStack Class="form_helper mt-12" Spacing="3" AlignItems="AlignItems.Start">
					<MudText>Не можете войти?</MudText>
					<MudText>Свяжитесь с <b>администратором</b></MudText>
				</MudStack>
			</div>

			<footer>© @DateTime.Now.Year ООО «Теслател».</footer>
		</div>
		<img class="ad" src="/multimonitor_login_ad.svg" loading="lazy">
	</MudContainer>
</div>
<UserBannedDialog />