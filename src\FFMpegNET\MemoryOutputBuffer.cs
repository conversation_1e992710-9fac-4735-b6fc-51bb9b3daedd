using System.Buffers;
using System.Collections.Concurrent;
using System.Runtime.InteropServices;
using FFmpeg.AutoGen;

namespace FFMpegNET;

public unsafe class MemoryOutputBuffer : IDisposable
{
    private static class Pools
    {
        public static MemoryStream RentStream() => new MemoryStream(MIN_STREAM_CAPACITY);
        public static void ReturnStream(MemoryStream stream) => stream.Dispose();

        public static byte[] RentBuffer() => ArrayPool<byte>.Shared.Rent(BUFFER_SIZE);
        public static void ReturnBuffer(byte[] buffer) => ArrayPool<byte>.Shared.Return(buffer);

        public static void Clear()
        {
        }
    }

    /// <summary>
    /// Очищает статические пулы объектов
    /// </summary>
    public static void ClearPools()
    {
        Pools.Clear();
    }

    private const int BUFFER_SIZE = 8192;
    private const int MIN_STREAM_CAPACITY = BUFFER_SIZE * 32;
    private const int MAX_STREAM_CAPACITY = BUFFER_SIZE * 64;

    private byte[] _copyBuffer;
    private MemoryStream _stream;
    private GCHandle _gcHandle;
    private readonly avio_alloc_context_write_packet _writePacket;
    private AVIOContext* _avioContext;
    private bool _disposed;

    public AVIOContext* AvioContext => _avioContext;
    public MemoryStream Stream => _stream;

    public MemoryOutputBuffer()
    {
        _copyBuffer = Pools.RentBuffer();
        _stream = Pools.RentStream();

        _writePacket = WritePacket;
        _gcHandle = GCHandle.Alloc(_writePacket, GCHandleType.Normal);

        var buffer = (byte*)ffmpeg.av_malloc(BUFFER_SIZE);
        _avioContext = ffmpeg.avio_alloc_context(
            buffer,
            BUFFER_SIZE,
            1,
            null,
            null,
            _writePacket,
            null
        );

        if (_avioContext == null)
        {
            ffmpeg.av_free(buffer);
            throw new ApplicationException("Failed to create I/O context");
        }
    }

    ~MemoryOutputBuffer()
    {
        Dispose(false);
    }

    private int WritePacket(void* opaque, byte* buf, int bufSize)
    {
        if (buf == null || bufSize <= 0)
            return 0;

        var size = bufSize;
        try
        {
            while (size > 0)
            {
                var copySize = Math.Min(size, _copyBuffer.Length);

                fixed (byte* dest = _copyBuffer)
                {
                    Buffer.MemoryCopy(buf, dest, copySize, copySize);
                }

                _stream.Write(_copyBuffer, 0, copySize);

                buf += copySize;
                size -= copySize;
            }

            return bufSize;
        }
        catch
        {
            return -1;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    public void Dispose(bool disposing)
    {
        if (_disposed)
            return;

        if (disposing)
        {
            Pools.ReturnBuffer(_copyBuffer);
            Pools.ReturnStream(_stream);
        }

        if (_gcHandle.IsAllocated)
            _gcHandle.Free();

        if (_avioContext != null)
        {
            var buffer = _avioContext->buffer;

            fixed (AVIOContext** pContext = &_avioContext)
            {
                ffmpeg.avio_context_free(pContext);
            }

            ffmpeg.av_free(buffer);
            _avioContext = null;
        }

        _disposed = true;
    }
}