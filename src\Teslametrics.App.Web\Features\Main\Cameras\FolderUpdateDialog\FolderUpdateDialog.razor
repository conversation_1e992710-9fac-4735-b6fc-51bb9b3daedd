﻿@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<MudDialog @bind-Visible="_isVisible"
           Class="br_12"
           ActionsClass="align-center justify-center pb-5 px-5 pt-2"
           ContentClass="content ma-0 pa-5"
           Options="_dialogOptions">
    <DialogContent>
        <div class="title d-flex">
            <MudStack Spacing="0">
                <MudText Typo="Typo.subtitle1">Редактировать директорию</MudText>
            </MudStack>
            <MudSpacer />
            <MudIconButton OnClick="Cancel"
                           Icon="@Icons.Material.Outlined.Close" />
        </div>
        @if (_model is not null)
        {
            <MudForm Model="_model"
                     Validation="_validator"
                     @bind-IsValid="_isValid"
                     UserAttributes="@(new Dictionary<string, object>() { { "autocomplete", "off" }, { "aria-autocomplete", "none" }, { "role", "presentation" } })">

                <MudText Typo="Typo.caption">Название</MudText>
                <MudTextField @bind-Value="_model.Name"
                              For="() => _model.Name"
                              Clearable="true"
                              Immediate="true"
                              Placeholder="Новая директория"
                              RequiredError="Данное поле обязательно"
                              Required="true"
                              Variant="Variant.Outlined"
                              Margin="Margin.Dense" />
            </MudForm>
        }
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel"
                   Variant="Variant.Outlined">Отмена</MudButton>
        <MudButton OnClick="SubmitAsync"
                   Color="Color.Primary"
                   Variant="Variant.Filled"
                   Disabled="@(!_isValid)">Сохранить</MudButton>
    </DialogActions>
</MudDialog>