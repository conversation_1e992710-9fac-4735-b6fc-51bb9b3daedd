import { MediaPipelinePlugin } from "./MediaPipelinePlugin.js";

/**
 * Плагин для управления полноэкранным режимом видеоэлемента
 * Обеспечивает отслеживание состояния полноэкранного режима и уведомление об изменениях
 */
export class FullscreenPlugin extends MediaPipelinePlugin {
  constructor(pipeline, options = {}) {
    super(pipeline, options);

    // Состояние полноэкранного режима
    this.isFullscreen = false;
    this.fullscreenEventHandlers = [];

    // Настройки плагина
    this.autoTrackChanges = options.autoTrackChanges !== false; // по умолчанию true
    this.emitEvents = options.emitEvents !== false; // по умолчанию true
  }

  /**
   * Инициализация плагина
   */
  initialize() {
    super.initialize();

    if (this.autoTrackChanges) {
      this.setupFullscreenTracking();
    }

    // Проверяем начальное состояние
    this.updateFullscreenState();

    console.log("[FullscreenPlugin] Плагин инициализирован", {
      autoTrackChanges: this.autoTrackChanges,
      emitEvents: this.emitEvents,
      initialState: this.isFullscreen,
    });
  }

  /**
   * Деинициализация плагина
   */
  dispose() {
    this.removeFullscreenTracking();
    super.dispose();

    console.log("[FullscreenPlugin] Плагин деинициализирован");
  }

  /**
   * Настраивает отслеживание изменений полноэкранного режима
   */
  setupFullscreenTracking() {
    // Список событий изменения полноэкранного режима для разных браузеров
    const fullscreenEvents = [
      "fullscreenchange",
      "mozfullscreenchange",
      "webkitfullscreenchange",
      "msfullscreenchange",
    ];

    // Создаем обработчик события
    const handler = () => this.handleFullscreenChange();

    // Добавляем обработчики для всех событий
    fullscreenEvents.forEach((eventName) => {
      document.addEventListener(eventName, handler, false);
      this.fullscreenEventHandlers.push({ eventName, handler });
    });

    console.log(
      "[FullscreenPlugin] Отслеживание полноэкранного режима настроено"
    );
  }

  /**
   * Удаляет обработчики событий полноэкранного режима
   */
  removeFullscreenTracking() {
    this.fullscreenEventHandlers.forEach(({ eventName, handler }) => {
      document.removeEventListener(eventName, handler, false);
    });
    this.fullscreenEventHandlers = [];

    console.log(
      "[FullscreenPlugin] Отслеживание полноэкранного режима отключено"
    );
  }

  /**
   * Обрабатывает изменения полноэкранного режима
   */
  handleFullscreenChange() {
    const previousState = this.isFullscreen;
    this.updateFullscreenState();

    // Если состояние изменилось, уведомляем об этом
    if (previousState !== this.isFullscreen && this.emitEvents) {
      this.emitFullscreenChanged();
    }
  }

  /**
   * Обновляет внутреннее состояние полноэкранного режима
   */
  updateFullscreenState() {
    const fullscreenElement =
      document.fullscreenElement ||
      document.mozFullScreenElement ||
      document.webkitFullscreenElement ||
      document.msFullscreenElement;

    this.isFullscreen = fullscreenElement === this.pipeline.video;
  }

  /**
   * Испускает событие изменения полноэкранного режима
   */
  emitFullscreenChanged() {
    const eventData = {
      isFullscreen: this.isFullscreen,
      timestamp: Date.now(),
      videoElement: this.pipeline.video,
    };

    // Испускаем событие через MediaPipeline
    this.pipeline.ev.emit("fullscreen-changed", eventData);

    console.log(
      "[FullscreenPlugin] Состояние полноэкранного режима изменено:",
      this.isFullscreen
    );
  }

  /**
   * Переключает полноэкранный режим
   * @returns {Promise<boolean>} Новое состояние полноэкранного режима
   */
  async toggleFullscreen() {
    try {
      if (this.isFullscreen) {
        await this.exitFullscreen();
      } else {
        await this.enterFullscreen();
      }
      return this.isFullscreen;
    } catch (error) {
      console.error(
        "[FullscreenPlugin] Ошибка при переключении полноэкранного режима:",
        error
      );
      throw error;
    }
  }

  /**
   * Переводит видеоэлемент в полноэкранный режим
   * @returns {Promise<void>}
   */
  async enterFullscreen() {
    const video = this.pipeline.video;

    if (!video) {
      throw new Error("Видеоэлемент не найден");
    }

    if (this.isFullscreen) {
      console.log("[FullscreenPlugin] Видео уже в полноэкранном режиме");
      return;
    }

    try {
      if (video.requestFullscreen) {
        await video.requestFullscreen();
      } else if (video.mozRequestFullScreen) {
        // Firefox
        await video.mozRequestFullScreen();
      } else if (video.webkitRequestFullscreen) {
        // Chrome, Safari, Edge
        await video.webkitRequestFullscreen();
      } else if (video.msRequestFullscreen) {
        // IE/Edge
        await video.msRequestFullscreen();
      } else {
        throw new Error("Полноэкранный режим не поддерживается браузером");
      }

      console.log("[FullscreenPlugin] Переход в полноэкранный режим запрошен");
    } catch (error) {
      console.error(
        "[FullscreenPlugin] Ошибка при входе в полноэкранный режим:",
        error
      );
      throw error;
    }
  }

  /**
   * Выходит из полноэкранного режима
   * @returns {Promise<void>}
   */
  async exitFullscreen() {
    if (!this.isFullscreen) {
      console.log("[FullscreenPlugin] Видео не в полноэкранном режиме");
      return;
    }

    try {
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        // Firefox
        await document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        // Chrome, Safari, Edge
        await document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        // IE/Edge
        await document.msExitFullscreen();
      } else {
        throw new Error(
          "Выход из полноэкранного режима не поддерживается браузером"
        );
      }

      console.log("[FullscreenPlugin] Выход из полноэкранного режима запрошен");
    } catch (error) {
      console.error(
        "[FullscreenPlugin] Ошибка при выходе из полноэкранного режима:",
        error
      );
      throw error;
    }
  }

  /**
   * Возвращает текущее состояние полноэкранного режима
   * @returns {boolean}
   */
  getFullscreenState() {
    return this.isFullscreen;
  }

  /**
   * Проверяет, поддерживается ли полноэкранный режим браузером
   * @returns {boolean}
   */
  isFullscreenSupported() {
    const video = this.pipeline.video;
    if (!video) return false;

    return !!(
      video.requestFullscreen ||
      video.mozRequestFullScreen ||
      video.webkitRequestFullscreen ||
      video.msRequestFullscreen
    );
  }

  /**
   * Возвращает информацию о плагине
   * @returns {Object}
   */
  getInfo() {
    return {
      name: "FullscreenPlugin",
      version: "1.0.0",
      isActive: this.isActive,
      isFullscreen: this.isFullscreen,
      isSupported: this.isFullscreenSupported(),
      autoTrackChanges: this.autoTrackChanges,
      emitEvents: this.emitEvents,
    };
  }
}
