@using Teslametrics.App.Web.Extensions
@using Teslametrics.MediaServer.Orleans.Camera
@using Teslametrics.App.Web.Features.Main.Cameras.List.CameraCard.Preview
@using Teslametrics.Shared
@attribute [StreamRendering(true)]
@inherits InteractiveBaseComponent
<MudCard Class="mud-height-full mud-width-full"
		 Elevation="0">
	<MudCardHeader Class="pa-1">
		@if (_response is null)
		{
			<MudSkeleton />
			<MudSpacer />
			<MudSkeleton Width="30px"
						 Height="30px" />
		}
		else
		{
			<MudText Typo="Typo.h5"
					 class="camera_name mud-width-full">
				@_response.Name
			</MudText>
			@if (_response.IsBlocked)
			{
				<MudChip T="string"
						 Variant="Variant.Outlined"
						 Icon="@TeslaIcons.Devices.Camera"
						 Size="Size.Small"
						 Class="pl-3"
						 Color="Color.Error">Lck</MudChip>
			}
			else
			{
				@if (_cameraStatus == CameraStatus.Connecting)
				{
					<MudChip T="string"
							 Variant="Variant.Outlined"
							 Icon="@TeslaIcons.Devices.Camera"
							 Size="Size.Small"
							 Class="pl-3"
							 Color="Color.Info">Off</MudChip>
				}
				@if (_cameraStatus == CameraStatus.Problem)
				{
					<MudChip T="string"
							 Variant="Variant.Outlined"
							 Icon="@TeslaIcons.Devices.Camera"
							 Size="Size.Small"
							 Class="pl-3"
							 Color="Color.Error">Err</MudChip>
				}
				@if (_cameraStatus == CameraStatus.Running)
				{
					<MudChip T="string"
							 Variant="Variant.Outlined"
							 Icon="@TeslaIcons.Devices.Camera"
							 Size="Size.Small"
							 Class="pl-3"
							 Color="Color.Success">On</MudChip>
				}
				@if (_cameraStatus == CameraStatus.Stopped)
				{
					<MudChip T="string"
							 Variant="Variant.Outlined"
							 Icon="@TeslaIcons.Devices.Camera"
							 Size="Size.Small"
							 Class="pl-3"
							 Color="Color.Info">Off</MudChip>
				}
			}
			<MudMenu Icon="@Icons.Material.Filled.MoreVert"
					 AriaLabel="Действия с камерой"
					 Size="Size.Medium">
				@if ((_cameraStatus == CameraStatus.Running || _cameraStatus == CameraStatus.Problem) && !_response.IsBlocked)
				{
					<MudMenuItem OnClick="ShowPlayer"
								 Icon="@Icons.Material.Outlined.PanoramaFishEye">Просмотр</MudMenuItem>
				}
				<MudMenuItem OnClick="ShowArchive"
							 Icon="@Icons.Material.Filled.FolderZip">Архив</MudMenuItem>
				@if (_cameraStatus == CameraStatus.Running || _cameraStatus == CameraStatus.Connecting || _cameraStatus == CameraStatus.Problem)
				{
					<AuthorizeView Policy="@AppPermissions.Main.Cameras.Disconnect.GetEnumPermissionString()"
								   Resource="new PolicyRequirementResource(OrganizationId, CameraId)"
								   Context="innerContext">
						<MudMenuItem OnClick="DisconnectAsync"
									 Icon="@Icons.Material.Filled.VisibilityOff">Отключить</MudMenuItem>
					</AuthorizeView>
				}
				@if (_cameraStatus == CameraStatus.Stopped)
				{
					<AuthorizeView Policy="@AppPermissions.Main.Cameras.Connect.GetEnumPermissionString()"
								   Resource="new PolicyRequirementResource(OrganizationId, CameraId)"
								   Context="innerContext">
						<MudMenuItem OnClick="ConnectAsync"
									 Icon="@Icons.Material.Filled.Visibility">Подключить</MudMenuItem>
					</AuthorizeView>
				}
				<AuthorizeView Policy="@AppPermissions.Main.Cameras.Update.GetEnumPermissionString()"
							   Resource="new PolicyRequirementResource(OrganizationId, CameraId)"
							   Context="innerContext">
					<MudMenuItem OnClick="Edit"
								 Icon="@Icons.Material.Outlined.Edit">
						Настройки камеры
					</MudMenuItem>
				</AuthorizeView>
				<AuthorizeView Policy="@AppPermissions.Main.Cameras.Delete.GetEnumPermissionString()"
							   Resource="new PolicyRequirementResource(OrganizationId, CameraId)"
							   Context="innerContext">
					<MudDivider Class="my-3" />

					<MudMenuItem OnClick="ClearArchiveAsync"
								 Icon="@Icons.Material.Outlined.Delete"
								 IconColor="Color.Warning">
						Очистить архив
					</MudMenuItem>

					<MudMenuItem OnClick="Delete"
								 Icon="@Icons.Material.Outlined.Delete"
								 IconColor="Color.Warning">
						Удалить
					</MudMenuItem>
				</AuthorizeView>
			</MudMenu>
		}
	</MudCardHeader>
	<div class="image_container pa-1">
		<div class="d-flex mud-width-full flex-column justify-center align-center mud-height-full bg3"
			 @onclick="ShowPlayer">
			@if (_response is null)
			{
				<MudSkeleton SkeletonType="SkeletonType.Rectangle"
							 Width="100%"
							 Height="100%" />
			}
			else
			{
				@switch (_cameraStatus)
				{
					case CameraStatus.Problem:
					case CameraStatus.Running:
						<CameraPreview CameraId="@_response.Id"
									   CameraStreamId="@_response.CameraStreamId" />
						break;
					case CameraStatus.Stopped:
						<MudIcon Icon="@Icons.Material.Filled.Block"
								 Color="Color.Warning" />
						<div>Камера отключена</div>
						break;
					case CameraStatus.Connecting:
						<MudProgressCircular Color="Color.Info"
											 Style="height:70px;width:70px;"
											 Indeterminate="true" />
						<div>Камера подключается</div>
						break;
					default:
						break;
				}
			}
		</div>
	</div>
</MudCard>