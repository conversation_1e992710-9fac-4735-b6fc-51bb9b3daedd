﻿using Microsoft.EntityFrameworkCore;
using Teslametrics.Core.Abstractions;
using Teslametrics.Core.Domain.AccessControl.Users;

namespace Teslametrics.Core.Services.Persistence;

public class UserRepository : BaseRepository<UserAggregate>, IUserRepository
{
    public UserRepository(CommandAppDbContext dbContext)
        : base(dbContext)
    {
    }

    public Task<UserAggregate?> FindByUsernameAsync(string username,
                                                    CancellationToken cancellationToken = default) =>
        DbContext.Set<UserAggregate>()
            .AsTracking()
            .OrderBy(entity => entity.Id)
            //.FirstOrDefaultAsync(entity => EF.Functions.Collate(entity.Name, "utf8mb4_bin") == username, cancellationToken);
            .FirstOrDefaultAsync(entity => entity.Name == username, cancellationToken);

    public Task<List<UserAggregate>> GetUsersByOrganizationIdAsync(Guid organizationId,
                                                                   CancellationToken cancellationToken = default) =>
        DbContext.Set<UserAggregate>()
            .AsTracking()
            .Where(u => u.Organizations.Any(o => o.OrganizationId == organizationId))
            .OrderBy(entity => entity.Id)
            .ToListAsync(cancellationToken);

    public Task<List<UserAggregate>> GetUsersInRoleAsync(Guid roleId,
                                                         CancellationToken cancellationToken = default) =>
        DbContext.Set<UserAggregate>()
            .AsTracking()
            .Where(u => u.Roles.Any(r => r.RoleId == roleId))
            .OrderBy(entity => entity.Id)
            .ToListAsync(cancellationToken);

    public Task<bool> IsUserExistsAsync(Guid id,
                                        CancellationToken cancellationToken = default) =>
        DbContext.Set<UserAggregate>()
            .AsNoTracking()
            .OrderBy(entity => entity.Id)
            .AnyAsync(entity => entity.Id == id, cancellationToken);

    public Task<bool> IsUsernameExists(string username,
                                       CancellationToken cancellationToken = default) =>
        DbContext.Set<UserAggregate>()
            .AsNoTracking()
            .OrderBy(entity => entity.Id)
            .AnyAsync(entity => entity.Name == username, cancellationToken);

    public Task<List<UserAggregate>> GetAllUsersAsync(CancellationToken cancellationToken = default) =>
        DbContext.Set<UserAggregate>()
            .AsNoTracking()
            .OrderBy(entity => entity.Id)
            .ToListAsync(cancellationToken);
}