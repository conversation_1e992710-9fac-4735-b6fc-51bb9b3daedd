::deep #layout_sidebar {
	background: var(--color-primary-900);
}

.mud_theme_dark .d_contents ::deep #layout_sidebar {
	background: #1F221F;
}


/* ::deep #layout_sidebar {
	z-index: calc(var(--mud-zindex-appbar) - 1);
}

@media (min-width: 641px) {
	::deep #layout_sidebar {
		border-right: 1px solid var(--mud-palette-divider);
	}
} */

::deep .nav_menu {
    height: -webkit-fill-available;
    overflow: auto;
}

::deep .mud-nav-link {
	padding-left: 8px;
}

::deep .mud-nav-link .mud-nav-link-text {
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

::deep .nav_link a {
	height: 48px;
	border-radius: 8px;
	padding-top: 12px;
	padding-right: 16px;
	padding-bottom: 12px;
	padding-left: 16px;
	gap: 10px;
	margin: 0;
	display: flex;
	align-items: center;
}

::deep .nav_link svg {
	width: 16px;
	height: 16px;
}

::deep .nav_link a>svg {
	font-size: 16px;
}

::deep .nav_link a .mud-nav-link-text {
	margin: 0;
}

::deep .nav_link>* {
	padding: 16px 12px;
	border-radius: 8px;
	color: rgba(255, 255, 255, 1);
}

::deep .nav_link .active {
	background: var(--color-primary-800) !important;
    color: var(--mud-palette-primary-text) !important;
	border-radius: 8px;
}

::deep .nav_link .active svg {
	color: var(--color-secondary-800) !important;
}


.mud_theme_dark .d_contents ::deep #layout_sidebar .nav_link .active {
    background: var(--color-bg-2) !important;
}

::deep .time_chip {
	border-radius: 12px !important;
	height: 48px !important;
}

.mud_theme_dark .logo:not(.dark),
.logo.dark {
	display: none;
}

.mud_theme_dark .logo,
.logo:not(.dark) {
	display: block;
	width: 150px;
}
