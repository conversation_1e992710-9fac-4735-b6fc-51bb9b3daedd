using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Teslametrics.App.Web.Components.MSEPlayer;

public partial class FullscreenToggleButton : BaseComponent, IAsyncDisposable
{
    private bool _disposed = false;
    private DotNetObjectReference<FullscreenToggleButton>? _dotNetRef;
    private IJSObjectReference? _jsModule;
    private bool _isFullscreen = false;

    #region [Injectables]
    [Inject]
    private IJSRuntime JSRuntime { get; set; } = null!;
    #endregion

    [Parameter]
    [EditorRequired]
    public IMsePlayer Player { get; set; } = null!;

    /// <summary>
    /// Текущее состояние полноэкранного режима (используется для отображения в UI)
    /// </summary>
    public bool IsFullscreen => _isFullscreen;

    protected override void OnInitialized()
    {
        // Подписываемся на событие изменения состояния полноэкранного режима от IMsePlayer
        // Это обеспечивает совместимость с существующим интерфейсом
        Player.FullscreenChanged += OnPlayerFullscreenChanged;

        base.OnInitialized();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            await InitializeJavaScriptModuleAsync();
        }
    }

    /// <summary>
    /// Инициализирует JavaScript модуль для работы с плагином FullscreenPlugin
    /// </summary>
    private async Task InitializeJavaScriptModuleAsync()
    {
        try
        {
            // Создаем ссылку на этот компонент для JavaScript
            _dotNetRef = DotNetObjectReference.Create(this);

            // Загружаем JavaScript модуль
            _jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import",
                "./Components/MSEPlayer/FullscreenToggleButton.razor.js");

            // Инициализируем плагин FullscreenPlugin для данного плеера
            await _jsModule.InvokeVoidAsync("initializeFullscreenPlugin",
                Player.CameraId.ToString(), _dotNetRef, new
                {
                    autoTrackChanges = true,
                    emitEvents = true
                });

            Logger.LogDebug("FullscreenPlugin плагин инициализирован для камеры {CameraId}", Player.CameraId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при инициализации FullscreenPlugin плагина");
        }
    }

    /// <summary>
    /// Обработчик изменения состояния полноэкранного режима от IMsePlayer
    /// Обеспечивает совместимость с существующим интерфейсом
    /// </summary>
    private void OnPlayerFullscreenChanged(object? sender, bool isFullscreen)
    {
        _isFullscreen = isFullscreen;
        InvokeAsync(StateHasChanged);
    }

    /// <summary>
    /// Обработчик изменения состояния полноэкранного режима от JavaScript плагина
    /// </summary>
    [JSInvokable]
    public async Task OnFullscreenStateChanged(bool isFullscreen)
    {
        if (_disposed) return;

        try
        {
            _isFullscreen = isFullscreen;
            await InvokeAsync(StateHasChanged);

            Logger.LogDebug("Состояние полноэкранного режима обновлено через плагин для камеры {CameraId}: {IsFullscreen}",
                Player.CameraId, isFullscreen);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при обработке изменения состояния полноэкранного режима от плагина");
        }
    }

    /// <summary>
    /// Обработчик ошибок от JavaScript плагина
    /// </summary>
    [JSInvokable]
    public void OnError(string errorMessage)
    {
        if (_disposed) return;

        try
        {
            Logger.LogError("Ошибка в FullscreenPlugin для камеры {CameraId}: {ErrorMessage}",
                Player.CameraId, errorMessage);

            // Можно добавить уведомление пользователя через Snackbar если нужно
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при обработке ошибки от FullscreenPlugin");
        }
    }

    /// <summary>
    /// Переключает полноэкранный режим
    /// Использует как плагин (приоритет), так и IMsePlayer интерфейс (fallback)
    /// </summary>
    private async Task ToggleFullscreen()
    {
        try
        {
            // Сначала пытаемся использовать плагин
            if (_jsModule != null)
            {
                var newState = await _jsModule.InvokeAsync<bool>("toggleFullscreen", Player.CameraId.ToString());
                Logger.LogDebug("Полноэкранный режим переключен через плагин для камеры {CameraId}: {NewState}",
                    Player.CameraId, newState);
            }
            else
            {
                // Fallback к IMsePlayer интерфейсу
                await Player.ToggleFullscreenAsync();
                Logger.LogDebug("Полноэкранный режим переключен через IMsePlayer для камеры {CameraId}", Player.CameraId);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при переключении полноэкранного режима для камеры {CameraId}", Player.CameraId);

            // Пытаемся использовать fallback если плагин не сработал
            if (_jsModule != null)
            {
                try
                {
                    await Player.ToggleFullscreenAsync();
                    Logger.LogDebug("Использован fallback IMsePlayer для камеры {CameraId}", Player.CameraId);
                }
                catch (Exception fallbackEx)
                {
                    Logger.LogError(fallbackEx, "Ошибка в fallback методе для камеры {CameraId}", Player.CameraId);
                }
            }
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (_disposed) return;

        try
        {
            // Останавливаем плагин
            if (_jsModule != null)
            {
                await _jsModule.InvokeVoidAsync("stopFullscreenPlugin", Player.CameraId.ToString());
                await _jsModule.DisposeAsync();
            }
        }
        catch (JSDisconnectedException)
        {
            // Игнорируем - соединение уже разорвано
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при освобождении ресурсов FullscreenToggleButton");
        }
        finally
        {
            // Отписываемся от события IMsePlayer
            if (Player != null)
            {
                Player.FullscreenChanged -= OnPlayerFullscreenChanged;
            }

            _dotNetRef?.Dispose();
            _disposed = true;
        }

        GC.SuppressFinalize(this);
    }
}
