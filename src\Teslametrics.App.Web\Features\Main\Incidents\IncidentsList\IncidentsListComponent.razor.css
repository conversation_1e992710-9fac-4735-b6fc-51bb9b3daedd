﻿::deep {
    --row-border-radius: 12px;
    --cell-border: 1px solid var(--mud-palette-table-lines);
}

::deep .mud-table thead tr th {
	border: none;
}

::deep .mud-table tbody tr {
	border-radius: var(--row-border-radius);
	border-top: var(--cell-border);
}

::deep .mud-table tbody tr td {
	border-top: var(--cell-border);
}

@media (max-width: 767px) {
	::deep .mud-table table {
		border-collapse: separate;
		border-spacing: 0 8px;
	}

	::deep .mud-table tbody tr td:first-child {
		border-top-left-radius: var(--row-border-radius);
		border-bottom-left-radius: var(--row-border-radius);
		border-left: var(--cell-border);
	}
	
	::deep .mud-table tbody tr td:last-child {
		border-top-right-radius: var(--row-border-radius);
		border-bottom-right-radius: var(--row-border-radius);
		border-right: var(--cell-border);
	}
}


::deep .mud-table tbody tr {
	display: table-row;
}

::deep .mud-table tbody {
	display: table-row-group;
}

::deep .mud-table .selected td {
	background-color: rgba(248, 252, 255, 1);
}

::deep .mud-table .error td {
	border-color: rgba(255, 243, 244, 1) !important;
}

::deep .actions::after {
	content: " ";
	background: transparent;
	display: block;
	height: 4px;
	width: 4px;
	border-radius: 50%;
	align-self: center;
	justify-self: end;
}

::deep .unread .actions::after {
	background: var(--mud-palette-primary);
}

@media (max-width: 767px) {
	::deep .mud-table tbody tr td:first-child {
        border-bottom-left-radius: 0;
        border-top-right-radius: var(--row-border-radius);
	}
	::deep .mud-table tbody tr td {
        border-left: var(--cell-border);
        border-right: var(--cell-border);
    }
	::deep .mud-table tbody tr td:last-child {
        border-bottom-left-radius: var(--row-border-radius);
        border-top-right-radius: 0;
	}
}