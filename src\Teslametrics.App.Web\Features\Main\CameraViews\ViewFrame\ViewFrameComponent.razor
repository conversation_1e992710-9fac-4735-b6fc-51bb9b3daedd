@using Teslametrics.App.Web.Features.Main.CameraViews.ViewFrame.Camera
@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<div class="cams_list_component mud-height-full grid_container">
	<MudStack Row="true"
			  Class="pb-4 mud-width-full"
			  AlignItems="AlignItems.Center">
		@if (IsLoading)
		{
			<MudSkeleton Width="65%"
						 Height="42px" />
		}
		@if (!IsLoading && _viewResponse is not null && _viewResponse.IsSuccess)
		{
			<MudText Typo="Typo.h3"
					 Class="align-content-center">@_viewResponse.Name</MudText>
			<MudSpacer />

			@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
			{
				<MudTooltip Arrow="true"
							Placement="Placement.Start"
							Text="Ошибка подписки на события">
					<MudIconButton OnClick="SubscribeAsync"
								   Icon="@Icons.Material.Filled.ErrorOutline"
								   Color="Color.Error" />
				</MudTooltip>
			}
			<MudButton OnClick="NavigateToDetails"
					   Variant="Variant.Filled"
					   Color="Color.Primary"
					   StartIcon="@TeslaIcons.Actions.Play">
				Запустить просмотр
			</MudButton>
		}
		@if (!IsLoading && (_viewResponse is null || !_viewResponse.IsSuccess))
		{
			<MudText Typo="Typo.h3"
					 Class="align-content-center">Ошибка загрузки вида</MudText>
			<MudSpacer />
			<div style="height: fit-content;padding-top: 6px;">
				<TimePassedComponent InputTime="@_lastRefreshTime"
									 TooltipText="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")" />
				<MudIconButton OnClick="RefreshAsync"
							   Icon="@Icons.Material.Outlined.Refresh" />
			</div>
		}
	</MudStack>

	@if (_viewResponse is not null && _viewResponse.IsSuccess && _viewResponse.Cells.Count > 0)
	{
		<div class="@($"grid {_viewResponse.GridType.ToString()} {(_viewResponse?.RowCount > _viewResponse?.ColumnCount ? "vertical" : "horizontal")}")"
			 style="@($"--cols: {_viewResponse?.ColumnCount};--rows: {_viewResponse?.RowCount};")">
			@foreach (var cell in _cells.OrderBy(x => x.CellIndex))
			{
				<div @key="cell">
					<CameraComponent CameraId="@cell.CameraId"
									 OrganizationId="@OrganizationId" />
				</div>
			}
		</div>
	}
	@if (_viewResponse is not null && _viewResponse.IsSuccess && _viewResponse.Cells.Count == 0)
	{
		<MudStack AlignItems="AlignItems.Center">
			<MudText Typo="Typo.subtitle1">Нет элементов</MudText>
			<MudText Typo="Typo.body1">Добавьте элементы и попробуйте снова</MudText>
			<MudText Typo="Typo.body2">Время последнего обновления: @_lastRefreshTime.ToLocalTime()</MudText>

			<MudButton OnClick="RefreshAsync"
					   Variant="Variant.Filled"
					   Color="Color.Primary"
					   StartIcon="@Icons.Material.Outlined.Refresh">Обновить</MudButton>
		</MudStack>
	}
	@if (_viewResponse is not null && _viewResponse.Result == GetViewUseCase.Result.ViewNotFound)
	{
		<MudStack AlignItems="AlignItems.Center">
			<MudText Typo="Typo.subtitle1">Вид не найден
			</MudText>
			<MudText Typo="Typo.body1">Данный вид не существует</MudText>
			<MudText Typo="Typo.body2">Время последнего обновления: @_lastRefreshTime.ToLocalTime()</MudText>

			<MudButton OnClick="RefreshAsync"
					   Variant="Variant.Filled"
					   Color="Color.Primary"
					   StartIcon="@Icons.Material.Outlined.Refresh">Обновить</MudButton>
		</MudStack>
	}
	@if (_viewResponse is not null && _viewResponse.Result != GetViewUseCase.Result.ViewNotFound &&
		!_viewResponse.IsSuccess)
	{
		<MudStack AlignItems="AlignItems.Center">
			<MudText Typo="Typo.subtitle1">Вид не найден
			</MudText>
			<MudText Typo="Typo.body1">Нет удалось получить вид из-за внутренней ошибки</MudText>
			<MudText Typo="Typo.body2">Время последнего обновления: @_lastRefreshTime.ToLocalTime()</MudText>

			<MudButton OnClick="RefreshAsync"
					   Variant="Variant.Filled"
					   Color="Color.Primary"
					   StartIcon="@Icons.Material.Outlined.Refresh">Обновить</MudButton>
		</MudStack>
	}
</div>