﻿using Microsoft.Extensions.Logging;

namespace Teslametrics.App.Maui
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                })
                .ConfigureMauiHandlers(handlers =>
                {
#if ANDROID
                    handlers.AddHandler<WebView, Platforms.Android.CustomWebViewHandler>();
#elif IOS
                    handlers.AddHandler<WebView, Platforms.iOS.CustomWebViewHandler>();
#elif MACCATALYST
                    handlers.AddHandler<WebView, Platforms.MacCatalyst.CustomWebViewHandler>();
#elif WINDOWS
                    handlers.AddHandler<WebView, Platforms.Windows.CustomWebViewHandler>();
#endif
                });

            builder.Services.AddMauiBlazorWebView();

#if DEBUG
            builder.Services.AddBlazorWebViewDeveloperTools();
            builder.Logging.AddDebug();
#endif

            return builder.Build();
        }
    }
}
