using Dapper;
using FFMpegNET;
using Orleans.Streams;
using System.Collections.Concurrent;
using System.Data;
using System.Globalization;
using Teslametrics.Core.Services.FileStorage;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;
using static Teslametrics.MediaServer.Orleans.Camera.ICameraStreamGrain;

namespace Teslametrics.MediaServer.Orleans.Camera;

public class CameraStreamGrain : Grain, ICameraStreamGrain
{
    private readonly ILogger _logger;
    private readonly RtspMicroSegmenter _microSegmenter;
    private readonly MicroSegmentAccumulator _segmentAccumulator;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private CameraStreamState _cameraStreamState = CameraStreamState.Disconnected;
    private readonly ConcurrentQueue<CameraStreamSignal> _externalQueue;
    private Guid _cameraId;
    private string _uri = string.Empty;
    private StreamType _streamType;
    private IAsyncStream<MicroSegment>? _videoStream;
    private Task? _segmenterTask;

    private Task? _accumulatorTask;
    private IFileStorage _fileStorage;

    private IGrainTimer? _processTimer;

    public CameraStreamGrain(ILogger<CameraGrain> logger,
                             RtspMicroSegmenter microSegmenter,
                             MicroSegmentAccumulator segmentAccumulator,
                             IFileStorage fileStorage,
                             IServiceScopeFactory serviceScopeFactory)
    {
        _logger = logger;
        _microSegmenter = microSegmenter;
        _segmentAccumulator = segmentAccumulator;
        _fileStorage = fileStorage;
        _serviceScopeFactory = serviceScopeFactory;

        _externalQueue = new ConcurrentQueue<CameraStreamSignal>();
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        var provider = this.GetStreamProvider(StreamNames.VideoLiveStream);
        _videoStream = provider.GetStream<MicroSegment>(StreamId.Create(StreamNamespaces.CameraStreams, this.GetPrimaryKey()));

        await base.OnActivateAsync(cancellationToken);
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        try
        {
            await StopProcessAsync();

            _microSegmenter?.Dispose();
            _segmentAccumulator?.Dispose();

            await base.OnDeactivateAsync(reason, cancellationToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Deactivation was canceled for camera stream. CameraId: {CameraId}, StreamType: {StreamType}", _cameraId, _streamType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during deactivation for camera stream. CameraId: {CameraId}, StreamType: {StreamType}", _cameraId, _streamType);
        }
    }

    public Task<CameraStreamState> GetStatusAsync()
    {
        return Task.FromResult(_cameraStreamState);
    }

    public Task ConnectAsync(ConnectRequest request)
    {
        _cameraId = request.CameraId;
        _uri = request.Uri;
        _streamType = request.StreamType;

        _processTimer ??= this.RegisterGrainTimer(ProcessAsync, new GrainTimerCreationOptions(TimeSpan.Zero, TimeSpan.FromMilliseconds(100)) { KeepAlive = true });

        _externalQueue.Enqueue(CameraStreamSignal.Connect);

        return Task.CompletedTask;
    }

    public Task DisconnectAsync()
    {
        _externalQueue.Enqueue(CameraStreamSignal.Disconnect);

        return Task.CompletedTask;
    }

    private async Task ProcessAsync(CancellationToken cancellationToken)
    {
        await UpdateAsync();

        if (!_externalQueue.TryDequeue(out var signal))
        {
            return;
        }

        switch (signal)
        {
            case CameraStreamSignal.Connect:
                {
                    switch (_cameraStreamState)
                    {
                        case CameraStreamState.Disconnected:
                            {
                                await ChangeStateAsync(CameraStreamState.Connecting);
                                await StartProcessAsync(cancellationToken);
                                break;
                            }
                    }

                    break;
                }
            case CameraStreamSignal.Connected:
                {
                    switch (_cameraStreamState)
                    {
                        case CameraStreamState.Connecting:
                        case CameraStreamState.Reconnecting:
                            {
                                await ChangeStateAsync(CameraStreamState.Connected);
                                break;
                            }
                    }

                    break;
                }
            case CameraStreamSignal.Disconnect:
                {
                    switch (_cameraStreamState)
                    {
                        case CameraStreamState.Connected:
                        case CameraStreamState.Connecting:
                        case CameraStreamState.Reconnecting:
                            {
                                await StopProcessAsync();
                                await ChangeStateAsync(CameraStreamState.Disconnected);
                                break;
                            }
                    }

                    break;
                }
            case CameraStreamSignal.Reconnect:
                {
                    switch (_cameraStreamState)
                    {
                        case CameraStreamState.Connected:
                        case CameraStreamState.Connecting:
                        case CameraStreamState.Reconnecting:
                            {
                                await ChangeStateAsync(CameraStreamState.Reconnecting);
                                await StartProcessAsync(cancellationToken);
                                break;
                            }
                    }

                    break;
                }
        }
    }

    private async Task UpdateAsync()
    {
        switch (_cameraStreamState)
        {
            //case CameraStreamState.Connecting:
            //case CameraStreamState.Reconnecting:
            //    {
            //        if (_runTask is null)
            //        {
            //            _externalQueue.Enqueue(CameraStreamSignal.Reconnect);
            //        }

            //        break;
            //    }
            case CameraStreamState.Connected:
                {
                    if (_segmenterTask!.IsCompleted)
                    {
                        _logger.LogError("RTSP processor failed for camera {Uri}", _uri);

                        _segmenterTask!.Dispose();
                        await _segmenterTask;
                        //_runTask = null;
                        _externalQueue.Enqueue(CameraStreamSignal.Reconnect);
                    }

                    break;
                }
        }
    }

    private async Task StartProcessAsync(CancellationToken cancellationToken)
    {
        try
        {
            // using var scope = _serviceScopeFactory.CreateScope();
            // if (_streamType is StreamType.Archive || _streamType is StreamType.View)
            // {
            //     _previewGrain = scope.ServiceProvider.GetRequiredService<ICameraStreamPreviewGrain>();
            // }

            Func<FFMpegNET.MicroSegment, Task> onNextSegment = OnNextLiveSegmentAsync;

            if (_streamType.HasFlag(StreamType.Archive))
            {
                using var scope = _serviceScopeFactory.CreateScope();

                await CreateTableIfNotExistsAsync(_cameraId);

                var retentionDays = await GetRetentionDaysAsync(_cameraId);
                await _fileStorage.CreateDirectoryIfNotExistsAsync(_cameraId.ToString("N"), retentionDays);

                onNextSegment = OnNextArchiveSegmentAsync;
            }

            // _runTask = _rtspProcessor.Run(_uri, enableAudio: false, async segment =>
            // {
            //     var microSegment = new MicroSegment(segment.Stream.ToArray(), segment.StartTime, segment.Duration);
            //     await onNextSegment(microSegment);
            // }, OnNextPreview, cancellationToken);
            if (_accumulatorTask is null)
            {
                _accumulatorTask = _segmentAccumulator.Run(async segment =>
                {
                    var startTime = segment.StartTime;
                    var endTime = startTime.AddSeconds(segment.Duration);

                    var tags = new Dictionary<string, string>
                    {
                    { "StartTime", startTime.ToUnixTimeMilliseconds().ToString() },
                    { "EndTime", endTime.ToUnixTimeMilliseconds().ToString() },
                    { "Duration", segment.Duration.ToString(CultureInfo.InvariantCulture) }
                    };

                    var fileName = segment.StartTime.ToString("dd-MM-yyyy_HH-mm-ss-fff") + ".ts";

                    try
                    {
                        segment.Stream.Position = 0;

                        // Upload the file with the memory stream that has a known length
                        await _fileStorage.UploadFileAsync(_cameraId.ToString("N"), fileName, segment.Stream, tags);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error uploading segment to storage: {FileName}", fileName);
                    }
                }, CancellationToken.None);
            }

            _segmenterTask = _microSegmenter.Run(_uri, enableAudio: false, segment => onNextSegment(segment), cancellationToken);

            if (_cameraStreamState is not CameraStreamState.Connected)
            {
                _externalQueue.Enqueue(CameraStreamSignal.Connected);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting RTSP processor for URI: {Uri}", _uri);
            _externalQueue.Enqueue(CameraStreamSignal.Reconnect);
        }
    }

    private async Task StopProcessAsync()
    {
        if (_segmenterTask is not null)
        {
            _microSegmenter.RequestGracefulStop();
            await _segmenterTask;
            _segmenterTask = null;
        }

        if (_accumulatorTask is not null)
        {
            _segmentAccumulator.RequestGracefulStop();
            await _accumulatorTask;
            _accumulatorTask = null;
        }

        if (_processTimer is not null)
        {
            _processTimer.Dispose();
            _processTimer = null;
        }
    }

    private async Task OnNextArchiveSegmentAsync(FFMpegNET.MicroSegment segment)
    {
        try
        {
            _segmentAccumulator.AddMicroSegment(segment);

            await OnNextLiveSegmentAsync(segment);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending segment to Kafka. CameraId: {CameraId}, StreamType: {StreamType}", _cameraId, _streamType);
            await OnNextLiveSegmentAsync(segment);
        }
    }

    private async Task OnNextLiveSegmentAsync(FFMpegNET.MicroSegment segment)
    {
        try
        {
            var microSegment = new MicroSegment(segment.Payload!, segment.StartTime, segment.Duration);

            await _videoStream!.OnNextAsync(microSegment);
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "Error sending segment to live stream. CameraId: {CameraId}, StreamType: {StreamType}", _cameraId, _streamType);
        }
    }

    // private async Task OnNextArchiveSegmentAsync(Segment segment)
    // {
    //     var startTime = segment.StartTime;
    //     var endTime = startTime.AddSeconds(segment.Duration);

    //     var tags = new Dictionary<string, string>
    //     {
    //         { "StartTime", startTime.ToUnixTimeMilliseconds().ToString() },
    //         { "EndTime", endTime.ToUnixTimeMilliseconds().ToString() },
    //         { "Duration", segment.Duration.ToString(CultureInfo.InvariantCulture) }
    //     };

    //     var fileName = segment.StartTime.ToString("dd-MM-yyyy_HH-mm-ss-fff") + ".ts";

    //     try
    //     {
    //         // Create a memory stream with known length to avoid ObjectSize error
    //         var memoryStream = new MemoryStream();
    //         segment.Stream.Position = 0;
    //         await segment.Stream.CopyToAsync(memoryStream);
    //         memoryStream.Position = 0;

    //         // Upload the file with the memory stream that has a known length
    //         await _fileStorage.UploadFileAsync(_cameraId.ToString("N"), fileName, memoryStream, tags);

    //         // Reset position for the live segment processing
    //         segment.Stream.Position = 0;
    //         await OnNextLiveSegmentAsync(segment);
    //     }
    //     catch (Exception ex)
    //     {
    //         _logger.LogError(ex, "Error uploading segment to storage: {FileName}", fileName);

    //         // Still try to process the live segment even if storage upload fails
    //         segment.Stream.Position = 0;
    //         await OnNextLiveSegmentAsync(segment);
    //     }
    // }

    // private async Task OnNextLiveSegmentAsync(Segment segment)
    // {
    //     var memoryStream = new MemoryStream((int)segment.Stream.Length);
    //     segment.Stream.Position = 0;

    //     await segment.Stream.CopyToAsync(memoryStream);

    //     _streamCache.AddSegment(_streamIndex, memoryStream.GetBuffer(), segment.StartTime, segment.StartTime.AddSeconds(segment.Duration));
    // }

    private async Task ChangeStateAsync(CameraStreamState state)
    {
        if (_cameraStreamState != state)
        {
            _cameraStreamState = state;

            // // TODO: Заменить
            // 
            // List<object> @event = [_cameraStreamState switch
            // {
            //     CameraStreamState.Disconnected => new CameraStreamDisconnectedEvent(_cameraId, _uri),
            //     CameraStreamState.Connected => new CameraStreamConnectedEvent(_cameraId, _uri),
            //     CameraStreamState.Connecting => new CameraStreamConnectingEvent(_cameraId, _uri),
            //     CameraStreamState.Reconnecting => new CameraStreamReconnectingEvent(_cameraId, _uri),
            //     _ => throw new AppException("Invalid camera stream state")
            // }];

            // using var scope = _serviceScopeFactory.CreateScope();
            // var outbox = scope.ServiceProvider.GetRequiredService<IOutbox>();
            // await outbox.AddRangeAsync(@event);
        }

        await Task.CompletedTask;
    }

    private async Task CreateTableIfNotExistsAsync(Guid cameraId)
    {
        var tableName = $"{Db.StreamSegments.Table}_{cameraId.ToString("N")}";

        using var scope = _serviceScopeFactory.CreateScope();
        using var dbConnection = scope.ServiceProvider.GetRequiredService<IDbConnection>();

        if (dbConnection.State != ConnectionState.Open)
        {
            dbConnection.Open();
        }

        var exists = await dbConnection.ExecuteScalarAsync<bool>("SELECT EXISTS (" +
                                                                    "SELECT FROM pg_tables " +
                                                                    "WHERE schemaname = 'public' " +
                                                                    "AND tablename = @tableName" +
                                                                ")", new { tableName });

        if (!exists)
        {
            using var transaction = dbConnection.BeginTransaction(IsolationLevel.Serializable);
            try
            {
                await dbConnection.ExecuteAsync($"create table {tableName} " +
                                                $"({Db.StreamSegments.Columns.SegmentIndex} BIGSERIAL, " +
                                                $"{Db.StreamSegments.Columns.FileName} text, " +
                                                $"{Db.StreamSegments.Columns.StartTime} timestamptz PRIMARY KEY, " +
                                                $"{Db.StreamSegments.Columns.EndTime} timestamptz)");

                // Создаем индексы до преобразования в гипертаблицу
                await dbConnection.ExecuteAsync($"CREATE INDEX idx_{tableName}_filename ON {tableName} ({Db.StreamSegments.Columns.FileName})");

                // Создаем составной индекс по времени
                // EndTime используется в условиях WHERE чаще, поэтому ставим его первым
                await dbConnection.ExecuteAsync($"CREATE INDEX idx_{tableName}_time ON {tableName} ({Db.StreamSegments.Columns.EndTime}, {Db.StreamSegments.Columns.StartTime})");

                // Преобразуем в гипертаблицу после создания индексов
                await dbConnection.ExecuteAsync($"SELECT create_hypertable('{tableName}', '{Db.StreamSegments.Columns.StartTime}')");

                transaction.Commit();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create table {TableName} and hypertable", tableName);
                transaction.Rollback();
                throw;
            }
        }
    }

    private async Task<int> GetRetentionDaysAsync(Guid cameraId)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        using var dbConnection = scope.ServiceProvider.GetRequiredService<IDbConnection>();

        if (dbConnection.State != ConnectionState.Open)
        {
            dbConnection.Open();
        }

        var retentionPeriodTemplate = SqlQueryBuilder.Create()
            .Select(Db.CameraQuotas.Columns.RetentionPeriodDays)
            .InnerJoin(Db.Cameras.Table, Db.Cameras.Props.QuotaId, Db.CameraQuotas.Props.Id, SqlOperator.Equals)
            .Where(Db.Cameras.Props.Id, ":Id", SqlOperator.Equals, new { Id = cameraId })
            .Build(QueryType.Standard, Db.CameraQuotas.Table, RowSelection.AllRows);

        return await dbConnection.ExecuteScalarAsync<int>(retentionPeriodTemplate.RawSql, retentionPeriodTemplate.Parameters);
    }
}

public enum CameraStreamSignal
{
    Disconnect,
    Connect,
    Connected,
    Reconnect
}