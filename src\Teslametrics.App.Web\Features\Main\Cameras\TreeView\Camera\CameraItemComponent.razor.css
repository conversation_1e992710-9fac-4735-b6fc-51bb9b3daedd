.icon_container
{
	position: relative;
}

::deep.icon_container .loader
{
	position: absolute;
	left: 0;
}

::deep .element {
    height: 48px;
}

.dot
{
	width: 10px;
	height: 10px;
	border-radius: 50%;
}

.dot.Running
{
	background: var(--mud-palette-success);
}

.dot.Connecting
{
	background: var(--mud-palette-info);
}

.dot.Stopped
{
	background: var(--mud-palette-warning);
}

.dot.Problem
{
	background: var(--mud-palette-error);
}