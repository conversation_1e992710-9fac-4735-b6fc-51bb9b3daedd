.player_link {
    display: grid;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    background-color: var(--color-bg-2);
    cursor: pointer;
    aspect-ratio: 16 / 9;
    grid-template-rows: 1fr;
    grid-template-columns: 1fr;
    justify-items: center;
    align-content: center;
    height: min-content;
    overflow: hidden;
}

::deep > div {
    grid-column: 1 / 1;
    grid-row: 1 / 1;
}

::deep svg {
    grid-column: 1 / 1;
    grid-row: 1 / 1;
    z-index: 1;
}