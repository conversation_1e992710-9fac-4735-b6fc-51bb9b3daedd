using FluentValidation;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.App.Web.Services.UserDevice;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraCreateDialog;

public partial class CameraCreateDialog
{
    #region [Types]
    private class OnvifSettings
    {
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; } = 80;
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;

        public OnvifSettings(string host, int port, string username, string password)
        {
            Host = host;
            Port = port;
            Username = username;
            Password = password;
        }

        public OnvifSettings()
        {
        }
    }

    private class Model
    {
        private bool _onvifEnabled;

        public Guid OrganizationId { get; init; }
        public Guid FolderId { get; init; }

        public string Name { get; set; }
        public Guid? Quota { get; set; }
        public string ArchiveUri { get; set; }
        public string PublicUri { get; set; }
        public string ViewUri { get; set; }
        public TimeSpan TimeZone { get; set; } = TimeSpan.FromHours(3);
        public Coordinates? Coordinates { get; set; }
        public bool AutoStart { get; set; }
        public bool StartOnCreate { get; set; }
        public bool OnvifEnabled
        {
            get => _onvifEnabled;
            set
            {
                if (_onvifEnabled != value)
                {
                    _onvifEnabled = value;
                    Onvif = value ? new OnvifSettings() : null;
                }
            }
        }

        public OnvifSettings? Onvif { get; set; }

        public Model(Guid organizationId, Guid folderId)
        {
            OrganizationId = organizationId;
            FolderId = folderId;

            Name = string.Empty;
            Quota = null;
            ArchiveUri = string.Empty;
            PublicUri = string.Empty;
            ViewUri = string.Empty;
            Coordinates = null;
            AutoStart = false;
            OnvifEnabled = false;
            Onvif = null;
        }
    }

    private class Validator : BaseFluentValidator<Model>
    {
        private static string? ExtractHostAndPort(string uri)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(uri)) return null;

                var rtspUri = new Uri(uri);
                if (rtspUri.Scheme != "rtsp") return null;

                return rtspUri.Authority;
            }
            catch
            {
                return null;
            }
        }

        private static bool ValidateUriHostMatch(string currentUri, Model model)
        {
            if (string.IsNullOrWhiteSpace(currentUri))
                return true;

            var currentHost = ExtractHostAndPort(currentUri);
            if (currentHost == null)
                return true;

            var allUris = new[] { model.ArchiveUri, model.PublicUri, model.ViewUri };
            var allHosts = allUris
                .Where(uri => !string.IsNullOrWhiteSpace(uri))
                .Select(ExtractHostAndPort)
                .Where(h => h != null);

            return allHosts.All(h => h == currentHost);
        }

        public Validator()
        {
            RuleFor(model => model.Name)
                .Length(3, 60).WithMessage("наименование должно быть длиной от 3 до 60 символов");

            RuleFor(model => model.ArchiveUri)
                .NotEmpty().WithMessage("Поле должно быть заполнено")
                .Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out var parsed) && parsed.Scheme == "rtsp")
                .WithMessage("Некорректный формат RTSP URI")
                .Must((model, uri) => ValidateUriHostMatch(uri, model))
                .WithMessage("Все RTSP URI должны иметь одинаковый хост и порт");

            RuleFor(model => model.ViewUri)
                .NotEmpty().WithMessage("Поле должно быть заполнено")
                .Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out var parsed) && parsed.Scheme == "rtsp")
                .WithMessage("Некорректный формат RTSP URI")
                .Must((model, uri) => ValidateUriHostMatch(uri, model))
                .WithMessage("Все RTSP URI должны иметь одинаковый хост и порт");

            RuleFor(model => model.PublicUri)
                .NotEmpty().WithMessage("Поле должно быть заполнено")
                .Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out var parsed) && parsed.Scheme == "rtsp")
                .WithMessage("Некорректный формат RTSP URI")
                .Must((model, uri) => ValidateUriHostMatch(uri, model))
                .WithMessage("Все RTSP URI должны иметь одинаковый хост и порт");

            RuleFor(model => model.TimeZone)
                .NotNull().WithMessage("Поле должно быть заполнено")
                .NotEmpty().WithMessage("Поле должно быть заполнено");

            // ONVIF validation rules - only when ONVIF is enabled
            When(model => model.OnvifEnabled && model.Onvif is not null, () =>
            {
                RuleFor(model => model.Onvif!.Host)
                    .NotNull().WithMessage("Поле должно быть заполнено")
                    .NotEmpty().WithMessage("Адрес ONVIF должен быть заполнен");

                RuleFor(model => model.Onvif!.Port)
                    .InclusiveBetween(1, 65535).WithMessage("Порт должен быть в диапазоне от 1 до 65535");

                RuleFor(model => model.Onvif!.Username)
                    .NotNull().WithMessage("Поле должно быть заполнено")
                    .NotEmpty().WithMessage("Логин ONVIF должен быть заполнен");

                RuleFor(model => model.Onvif!.Password)
                    .NotNull().WithMessage("Поле должно быть заполнено")
                    .NotEmpty().WithMessage("Пароль ONVIF должен быть заполнен");
            });
        }
    }
    #endregion

    #region [Form]
    private MudBlazor.MudTextField<string>? _archiveUriRef;
    private MudBlazor.MudTextField<string>? _publicUriRef;
    private MudBlazor.MudTextField<string>? _viewUriRef;

    private bool _isValid => _model is null ? false : _validator.Validate(_model).IsValid;
    private Model? _model;
    private readonly Validator _validator = new();
    #endregion

    #region [Dialog]
    private MudBlazor.DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MudBlazor.MaxWidth.Small, NoHeader = true, BackdropClick = false };
    private bool _isVisible;

    [Inject]
    private IUserDeviceService _userDeviceService { get; set; } = null!;
    #endregion

    protected override void OnInitialized()
    {
        if (_userDeviceService.IsMobile)
        {
            _dialogOptions = new()
            {
                CloseOnEscapeKey = true,
                FullWidth = true,
                FullScreen = true,
                NoHeader = true
            };
        }

        CompositeDisposable.Add(EventSystem.Subscribe<CameraCreateEto>(OnCreateHandler));

        base.OnInitialized();
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            UnsubscribeFromAuthStateChanged();
        }

        base.Dispose(disposing);
    }

    #region [Actions]
    private async Task SubmitAsync()
    {
        if (_model is null) return;

        CreateCameraUseCase.Response? response = null;
        try
        {

            CreateCameraUseCase.Command.OnvifSettings? onvifSettings = null;
            if (_model.OnvifEnabled && _model.Onvif is not null)
            {
                onvifSettings = new CreateCameraUseCase.Command.OnvifSettings(_model.Onvif.Host, _model.Onvif.Port, _model.Onvif.Username, _model.Onvif.Password);
            }

            response = await ScopeFactory.MediatorSend(new CreateCameraUseCase.Command(
                _model.OrganizationId,
                _model.FolderId,
                _model.Quota!.Value,
                _model.Name,
                _model.TimeZone,
                _model.Coordinates,
                _model.ArchiveUri.Trim(),
                _model.PublicUri.Trim(),
                _model.ViewUri.Trim(),
                _model.AutoStart,
                _model.StartOnCreate,
                _model.OnvifEnabled,
                onvifSettings
            ));
        }
        catch (Exception exc)
        {
            response = null;
            Logger.LogError(exc, exc.Message);
            Snackbar.Add($"Не удалось создать камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
        }

        if (response is not null)
        {
            switch (response.Result)
            {
                case CreateCameraUseCase.Result.Success:
                    Snackbar.Add("Камера успешно создана.", MudBlazor.Severity.Success);
                    Cancel();
                    break;
                case CreateCameraUseCase.Result.ValidationError:
                    Snackbar.Add("Ошибка валидации, проверьте правильность заполнения полей", MudBlazor.Severity.Error);
                    break;
                case CreateCameraUseCase.Result.CameraQuotaLimitReached:
                    Snackbar.Add("Невозможно создать камеру. Лимит камер на квоту превышен", MudBlazor.Severity.Error);
                    break;
                case CreateCameraUseCase.Result.OrganizationNotFound:
                    Snackbar.Add("Невозможно создать камеру. Организация не найдена", MudBlazor.Severity.Error);
                    break;
                case CreateCameraUseCase.Result.FolderNotFound:
                    Snackbar.Add("Невозможно создать камеру. Директория не найдена", MudBlazor.Severity.Error);
                    break;
                case CreateCameraUseCase.Result.CameraQuotaNotFound:
                    Snackbar.Add("Невозможно создать камеру. Квота не найдена. Выберите другую квоту", MudBlazor.Severity.Error);
                    break;

                case CreateCameraUseCase.Result.Unknown:
                    Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraCreateDialog), nameof(CreateCameraUseCase));
                    Snackbar.Add($"Не удалось создать камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                    break;

                default:
                    Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraCreateDialog), nameof(CreateCameraUseCase), response.Result);
                    Snackbar.Add($"Не удалось создать камеру из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                    break;
            }
        }
    }

    private void Cancel()
    {
        _isVisible = false;
        _model = null;
        UnsubscribeFromAuthStateChanged();
    }
    #endregion

    #region [Event Handlers]
    private async Task OnCreateHandler(CameraCreateEto eto)
    {
        bool isAcessGranted = await CheckIfPermissionGranted(eto.OrganizationId);
        if (!isAcessGranted)
        {
            Snackbar.Add("Недостаточно прав для создания камеры", MudBlazor.Severity.Warning);
            return;
        }

        _model = new(eto.OrganizationId, eto.FolderId);

        SubscribeToAuthStateChanged();
        _isVisible = true;
        await UpdateViewAsync();
    }

    private async void OnAuthenticationStateChanged(Task<AuthenticationState> authState)
    {
        if (_model is null)
        {
            Cancel();
            return;
        }

        bool isAcessGranted = await CheckIfPermissionGranted(authState, _model.OrganizationId);
        if (!isAcessGranted)
        {
            Snackbar.Add("Недостаточно прав для создания камеры", MudBlazor.Severity.Warning);
            Cancel();
            return;
        }
    }

    private void OnViewUriChanged(string uri)
    {
        if (_model is null)
        {
            return;
        }

        _model.ViewUri = uri;
        _archiveUriRef?.Validate();
        _publicUriRef?.Validate();
    }

    private void OnArchiveUriChanged(string uri)
    {
        if (_model is null)
        {
            return;
        }
        _model.ArchiveUri = uri;
        _viewUriRef?.Validate();
        _publicUriRef?.Validate();
    }

    private void OnPublicUriChanged(string uri)
    {
        if (_model is null)
        {
            return;
        }

        _model.PublicUri = uri;
        _viewUriRef?.Validate();
        _archiveUriRef?.Validate();
    }
    #endregion

    #region [Helpers]
    private Task<bool> CheckIfPermissionGranted(Guid organizationId)
    {
        var userAuthState = AuthenticationStateProvider.GetAuthenticationStateAsync();

        return CheckIfPermissionGranted(userAuthState, organizationId);
    }

    private async Task<bool> CheckIfPermissionGranted(Task<AuthenticationState> authState, Guid organizationId)
    {
        var userAuthState = await authState;
        var policyRequirementResource = new PolicyRequirementResource(organizationId, null);
        var authorizationResult = await AuthorizationService.AuthorizeAsync(
            userAuthState.User,  // Current user from AuthenticationStateProvider
            policyRequirementResource, // The resource being authorized
            AppPermissions.Main.Cameras.Create.GetEnumPermissionString() // The policy name
        );

        return authorizationResult.Succeeded;
    }

    private void SubscribeToAuthStateChanged()
    {
        AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
    }

    private void UnsubscribeFromAuthStateChanged()
    {
        AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
    }
    #endregion
}
