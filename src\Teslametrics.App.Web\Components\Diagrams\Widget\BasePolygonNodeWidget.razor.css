﻿::deep.polygon {
	/* fill: transparent;
	stroke: rgba(178, 194, 210, 1);
	opacity: 0.5; */
	stroke-width: 2;
	fill-rule: nonzero;
	z-index: 4;
	fill: var(--mud-palette-surface);
	opacity: 0.8;
	stroke: var(--mud-palette-secondary);
}

::deep.title {
	position: absolute;
	top: -32px;
}

::deep.node {
	min-width: 8px;
	min-height: 8px;
}

::deep .diagram-link-vertex {
	z-index: 5 !important;
}

::deep.active .polygon {
	fill: var(--mud-palette-primary-lighten);
	stroke: var(--mud-palette-error-darken);
}