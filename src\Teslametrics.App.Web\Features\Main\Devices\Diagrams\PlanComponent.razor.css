.plan-component-container {
    width: 100%;
    height: 100%;
    min-height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.plan-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    background-color: rgb(248 252 255);
    user-select: none;
}

.plan-content {
    position: absolute;
    transform-origin: 0 0;
    min-width: 100%;
    min-height: 100%;
}


::deep .plan-object {
    z-index: 3;
}

::deep .plan_room {
    z-index: 2;
}

.plan-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.plan-controls {
    position: absolute;
    bottom: 24px;
    left: 24px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 4px;
}

::deep .plan-zoom-button {
    background: var(--mud-palette-surface);
    width: 24px;
    height: 24px;
    transition: all 0.2s ease;
}
::deep .plan-controls button svg {
    width: 10px;
    height: 10px;
}

::deep .plan-zoom-button:hover {
    background-color: #f8f9fa;
}

::deep .plan-zoom-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

::deep .plan-zoom-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Прелоадер */
.plan-loader {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--mud-palette-surface);
    opacity: 0.9;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: opacity 0.3s ease-out;
}

.plan-loader.hidden {
    opacity: 0;
    pointer-events: none;
}

.plan-loader-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.plan-loader-text {
    font-size: 16px;
    color: #666;
    font-weight: 500;
}