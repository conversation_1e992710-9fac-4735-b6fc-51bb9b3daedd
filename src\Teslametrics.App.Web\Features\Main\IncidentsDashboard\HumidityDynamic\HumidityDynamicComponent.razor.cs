using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Teslametrics.App.Web.Components;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.HumidityDynamic;

public partial class HumidityDynamicComponent : InteractiveBaseComponent
{
	private IJSObjectReference? _jsModule;

	private GetHumidityDynamicUseCase.Response? _response;

	[Inject]
	private IJSRuntime JSRuntime { get; set; } = null!;

	public record SeriesData(DateTime Date, double? Value);

	[Parameter]
	public DateTimeOffset DateFrom { get; set; } = DateTime.Today.AddDays(-7);

	[Parameter]
	public DateTimeOffset DateTo { get; set; } = DateTime.Today;

	[Parameter]
	public Guid CityId { get; set; }

	[Parameter]
	public Guid BuildingId { get; set; }

	[Parameter]
	public Guid FloorId { get; set; }

	[Parameter]
	public Guid RoomId { get; set; }

	[Parameter]
	public Guid FridgeId { get; set; }

	protected override bool ShouldRender() => false;

	protected override async Task OnParametersSetAsync()
	{
		await LoadDataAsync();
		await BuildChartsAsync();
		await base.OnParametersSetAsync();
	}

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		await base.OnAfterRenderAsync(firstRender);

		if (firstRender)
		{
			try
			{
				_jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./Features/Main/IncidentsDashboard/HumidityDynamic/HumidityDynamicComponent.razor.js");
				await BuildChartsAsync();
			}
			catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
			{
			}
			catch (Exception ex)
			{
				Logger.LogError(ex, ex.Message);
				Snackbar.Add("Не удалось загрузить скрипт для построения графика", MudBlazor.Severity.Error);
			}
		}
	}

	private async Task LoadDataAsync()
	{
		await SetLoadingAsync(true);
		try
		{
			_response = await ScopeFactory.MediatorSend(new GetHumidityDynamicUseCase.Query(DateFrom, DateTo, CityId, BuildingId, FloorId, RoomId, FridgeId));
		}
		catch (Exception ex)
		{
			_response = null;
			Snackbar.Add("Ошибка при получении данных влажности. Обратитесь к администратору.", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetLoadingAsync(false);
	}

	private async Task BuildChartsAsync()
	{
		if (_jsModule == null) return;

		try
		{
			var (x, yCur, yPrev, refVals) = PreparePlotDataSmartStep(
				_response,
				DateFrom.DateTime, // reqFrom
				DateTo.DateTime
			);

			await _jsModule.InvokeVoidAsync("initHumidityChart", "humidity-chart", x, yCur, yPrev, refVals, new { dateFrom = DateFrom, dateTo = DateTo });
		}
		catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
		{
		}
		catch (Exception ex)
		{
			Logger.LogError(ex, ex.Message);
			Snackbar.Add("Не удалось построить график на полученных данных", MudBlazor.Severity.Error);
		}
	}

	private static (
		IReadOnlyList<string> x,
		IReadOnlyList<SeriesData> yCur,
		IReadOnlyList<SeriesData> yPrev,
		GetHumidityDynamicUseCase.Response.ReferenceValues? refVals)
	PreparePlotDataSmartStep(
		GetHumidityDynamicUseCase.Response? dto,
		DateTime reqFrom,
		DateTime reqTo)
	{
		// защита от null
		if (dto is null)
			return (
				Array.Empty<string>(),
				Array.Empty<SeriesData>(),
				Array.Empty<SeriesData>(),
				null);

		DateTime shiftTo = dto.CurrentPeriod.Count == 0 ? reqFrom : dto.CurrentPeriod.First().Date;
		DateTime shiftFrom = dto.PreviousPeriod.Count == 0 ? reqTo : dto.PreviousPeriod.First().Date;

		TimeSpan shift = shiftTo - shiftFrom;

		var shifted = dto.PreviousPeriod.Select(p => p.Date + shift).ToList();

		var allPoints = dto.CurrentPeriod
			.Select(p => p.Date)
			.Concat(shifted)
			.Distinct()
			.OrderBy(t => t)
			.ToList();

		var curDict = dto.CurrentPeriod.ToDictionary(p => p.Date, p => p.Humidity);
		var prevDict = dto.PreviousPeriod.ToDictionary(p => p.Date + shift, p => p.Humidity);

		var yCur = allPoints
			.Select(t => curDict.TryGetValue(t, out var v) ? new SeriesData(t, v) : new SeriesData(t, null))
			.ToList();

		var yPrev = allPoints
			.Select(t => prevDict.TryGetValue(t, out var v) ? new SeriesData(t + shift, v) : new SeriesData(t + shift, null))
			.ToList();

		return (allPoints.Select(t => t.ToString("o")).ToList(), yCur, yPrev, dto.Reference);
	}
}
