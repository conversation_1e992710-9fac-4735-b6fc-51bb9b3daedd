@inherits InteractiveBaseComponent

<div class="field-with-error-icon">
    <MudAutocomplete T="string"
                     Value="Value"
                     DebounceInterval="500"
                     ValueChanged="OnSelectedValudeChanged"
                     SearchFunc="@SearchAsync"
                     ToStringFunc="item => item ?? string.Empty"
                     MaxItems="25"
                     Required="true"
                     AdornmentIcon="@Icons.Material.Filled.Search"
                     Clearable="true"
                     ListItemClass="pa-0"
                     RequiredError="Поле обязательно к заполнению"
                     Placeholder="Введите название квоты *"
                     SelectOnActivation="false"
                     Immediate="true"
                     ShowProgressIndicator="true"
                     ProgressIndicatorColor="Color.Primary">
        <NoItemsTemplate>
            <NoItemsFoundComponent HasItems="false" />
        </NoItemsTemplate>
    </MudAutocomplete>
    <div class="error-icon-container">
        @if (Error)
        {
            <MudTooltip Arrow="true"
                        Placement="Placement.Start"
                        Class="validation-error-tooltip"
                        RootClass="error-tooltip">
                <ChildContent>
                    <MudIcon Icon="@Icons.Material.Filled.ErrorOutline"
                             Color="Color.Error"
                             Size="Size.Small"
                             Class="validation-error-icon" />
                </ChildContent>
                <TooltipContent>
                    @if (!string.IsNullOrEmpty(ErrorText))
                    {
                        <div class="validation-error-content">
                            <div class="validation-error-message">@ErrorText</div>
                        </div>
                    }
                </TooltipContent>
            </MudTooltip>
        }
    </div>
</div>