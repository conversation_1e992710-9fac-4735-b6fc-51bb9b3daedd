using Teslametrics.Shared;
using Teslametrics.Core.Abstractions;

namespace Teslametrics.Core.Domain.Incidents;

public class IncidentAggregate : IEntity
{
    public Guid Id { get; private set; }
    public IncidentType IncidentType { get; private set; }

    // Местоположение и идентификаторы
    public Guid CityId { get; private set; }
    public string City { get; private set; }

    public Guid BuildingId { get; private set; }
    public string Building { get; private set; }

    public Guid FloorId { get; private set; }
    public int Floor { get; private set; }

    public Guid RoomId { get; private set; }
    public string Room { get; private set; }

    public Guid? DeviceId { get; private set; }
    public string? Device { get; private set; }

    public Guid SensorId { get; private set; }
    public string Topic { get; private set; }  // Топик датчика

    public DateTimeOffset CreatedAt { get; private set; }
    public DateTimeOffset? ResolvedAt { get; private set; }

    private IncidentAggregate()
    {
        City = string.Empty;
        Building = string.Empty;
        Room = string.Empty;
        Device = string.Empty;
        Topic = string.Empty;
    }

    public static IncidentAggregate Create(Guid id,
                                           IncidentType incidentType,
                                           Guid cityId,
                                           string city,
                                           Guid buildingId,
                                           string building,
                                           Guid floorId,
                                           int floor,
                                           Guid roomId,
                                           string room,
                                           Guid? deviceId,
                                           string? device,
                                           Guid sensorId,
                                           string topic,
                                           DateTimeOffset createdAt)
    {
        return new IncidentAggregate
        {
            Id = id,
            IncidentType = incidentType,
            CityId = cityId,
            City = city,
            BuildingId = buildingId,
            Building = building,
            FloorId = floorId,
            Floor = floor,
            RoomId = roomId,
            Room = room,
            DeviceId = deviceId,
            Device = device,
            SensorId = sensorId,
            Topic = topic,
            CreatedAt = createdAt
        };
    }

    public void Resolve()
    {
        if (ResolvedAt.HasValue)
            return;

        ResolvedAt = DateTimeOffset.UtcNow;
    }
}