﻿﻿<MudDropContainer T="CsvHeader"
                  Items="@_allHeaderInstances"
                  ItemsSelector="@ItemSelector"
                  ItemDropped="OnDrop"
                  Class="d-flex flex-column gap-4"
                  @ref="_containerRef">
    <ChildContent>
        <!-- Верхняя зона - пул доступных CSV заголовков -->
        <div>
            <MudText Typo="Typo.h6" Class="my-2">@LocaleStrings.FileHeadersTitle</MudText>
            <MudDropZone T="CsvHeader"
                         Identifier="@(string.Empty)"
                         DraggingClass="mud-alert-text-info"
                         ItemDraggingClass="mud-alert-text-info"
                         Class="rounded-lg border-2 border-dashed mud-border-lines-default pa-2 headers align-center gap-4"
                         Style="min-height: 72px;">
                <ItemRenderer>
                    <MudPaper Elevation="3"
                              Class="pa-3 rounded-lg d-flex align-center header-item"
                              MinHeight="48px">
                        @context.Name
                    </MudPaper>
                </ItemRenderer>
            </MudDropZone>
        </div>

        <!-- Нижняя область - поля модели как зоны для сброса -->
        <MudStack>
            <MudText Typo="Typo.h6">@LocaleStrings.EntityFieldsTitle</MudText>
            <div class="fields_container">
                @foreach (var field in Fields)
                {
                    var isRequired = field.Required;
                    var hasMapping = Mapping.Any(m => m.ModelField.Equals(field));
                    var showWarning = isRequired && !hasMapping;

                    <MudBadge Icon="@Icons.Material.Filled.Warning"
                              Color="Color.Error"
                              Overlap="true"
                              Visible="@showWarning">
                        <MudDropZone T="CsvHeader"
                                     Identifier="@field.Field"
                                     DraggingClass="mud-alert-text-info"
                                     CanDrop="@(header => CanDrop(header, field.Field))"
                                     ItemDraggingClass="mud-alert-text-error"
                                     Class="rounded-lg border-2 border-dashed mud-border-lines-default pa-2 field-item">
                            <ItemRenderer>
                                <MudPaper Elevation="3"
                                          Class="pa-3 rounded-lg d-flex align-center my-2 mapped-header"
                                          MinHeight="48px">
                                    @context.Name
                                </MudPaper>
                            </ItemRenderer>
                            <ChildContent>
                                <div class="field-content">
                                    <MudText Typo="Typo.subtitle1"
                                             Class="field-label">
                                        @field.Label
                                        @if (field.Required)
                                        {
                                            <b>*</b>
                                        }
                                    </MudText>
                                    @if (isRequired)
                                    {
                                        <MudText Typo="Typo.caption"
                                                 Class="required-warning">
                                            Обязательное поле
                                        </MudText>
                                    }
                                </div>
                                @if (!hasMapping)
                                {
                                    <MudPaper Elevation="0"
                                            Class="pa-2 my-2 d-flex flex-column mud-background-gray rounded-lg mud-width-full"
                                            MinHeight="48px">
                                        <MudText Typo="Typo.overline">@LocaleStrings.DropZoneText</MudText>
                                    </MudPaper>
                                }
                            </ChildContent>
                        </MudDropZone>
                    </MudBadge>
                }
            </div>
        </MudStack>
    </ChildContent>
</MudDropContainer>
