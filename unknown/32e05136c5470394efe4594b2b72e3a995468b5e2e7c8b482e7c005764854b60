/* Контейнер для полей модели - сетка 4 колонки */
.fields_container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    grid-auto-rows: 1fr;
}

/* Стили для элементов полей */
::deep .fields_container .field-item {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 120px;
}

/* Контейнер для заголовков CSV - горизонтальная сетка */
::deep .headers {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-auto-rows: auto;
    gap: 8px;
}

/* Стили для элементов заголовков в пуле */
.header-item {
    cursor: grab;
    transition: all 0.2s ease;
    background-color: var(--mud-palette-surface);
    border: 1px solid var(--mud-palette-lines-default);
}

.header-item:hover {
    background-color: var(--mud-palette-action-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.header-item:active {
    cursor: grabbing;
    transform: translateY(0);
}

/* Стили для назначенных заголовков */
.mapped-header {
    background-color: var(--mud-palette-primary-lighten);
    border: 1px solid var(--mud-palette-primary);
    color: var(--mud-palette-primary-text);
}

/* Контент поля модели */
.field-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 48px;
    padding: 8px;
}

::deep .required-warning {
    color: var(--mud-palette-gray-dark);
}

.field-label {
    font-weight: 600;
    text-align: center;
    margin-bottom: 4px;
}

.field-label b {
    padding-left: 4px;
    color: var(--mud-palette-error-lighten);
}

/* Анимации для drag and drop */
::deep .mud-drop-zone {
    transition: all 0.3s ease;
}

::deep .mud-drop-zone.mud-drop-zone-drag-over {
    background-color: var(--mud-palette-info-lighten);
    border-color: var(--mud-palette-info);
    transform: scale(1.02);
}

/* Стили для обязательных полей */
::deep .mud-badge.mud-badge-error {
    z-index: 1;
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
    .fields_container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    ::deep .headers {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .fields_container {
        grid-template-columns: 1fr;
    }
    
    ::deep .headers {
        grid-template-columns: 1fr;
    }
}
