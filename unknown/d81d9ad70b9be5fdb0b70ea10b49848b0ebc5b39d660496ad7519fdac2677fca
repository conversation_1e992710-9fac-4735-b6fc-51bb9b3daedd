﻿using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.AspNetCore.Components;
using System.Globalization;
using System.Text;

namespace Teslametrics.App.Web.Components.CSVImport;

public class HeaderMapperLocales
{
    public string EntityFieldsTitle { get; set; } = "Поля сущности";
    public string FileHeadersTitle { get; set; } = "Заголовки файла импорта";
    public string DropZoneText { get; set; } = "Перенесите заголовок сюда";
}

public class CsvMapperLocales
{
    public string DelimeterSelectLabel { get; set; } = "Разделитель";
    public string UploadFileButtonText { get; set; } = "Выберите файл с расширением .csv";
    public string UploadFileContainerText { get; set; } = "или перенесите файл в эту область";
    public string ResetFileButtonText { get; set; } = "К выбору файла";
    public string ConfirmHeaderMapping { get; set; } = "Подтвердить заголовки";

    public HeaderMapperLocales Mapping { get; set; } = new();
}

public partial class CsvMapperComponent<TImportModel, TImportMap>
    where TImportModel : class
    where TImportMap : ClassMap<TImportModel>
{
    private byte[]? _fileContentBytes;
    private string? _delimeter = ",";

    private List<CsvHeaderMapper.CsvHeader> _headers { get; set; } = [];
    private IEnumerable<CsvHeaderMapper.HeaderMapping> _currentMapping = [];

    #region Parameters
    [Parameter]
    public EventCallback<Exception> OnParseException { get; set; }

    [Parameter]
    public EventCallback<IEnumerable<TImportModel>> OnImport { get; set; }

    [Parameter]
    public EventCallback OnImportCanceled { get; set; }

    [Parameter]
    public CsvMapperLocales LocaleStrings { get; set; } = new();

    [Parameter, EditorRequired]
    public IEnumerable<CsvHeaderMapper.ModelField> Fields { get; set; } = null!;

    [Parameter, EditorRequired]
    public Func<IEnumerable<CsvHeaderMapper.HeaderMapping>, TImportMap> GetImportMap { get; set; } = null!;

    /// <summary>
    /// Проверяет, валиден ли текущий маппинг (все обязательные поля назначены).
    /// </summary>
    public bool IsValid => Fields.Where(f => f.Required)
                                 .All(field => _currentMapping.Any(m => m.ModelField.Equals(field)));
    #endregion

    private void OnFileReset()
    {
        _headers.Clear();
        _currentMapping = [];
        _fileContentBytes = null;
    }

    private Task OnFileRead(Abstractions.File file)
    {
        _fileContentBytes = file.Bytes;
        return ParseFileHeaders();
    }

    private Task DelimeterChanged(string? delimeter)
    {
        _delimeter = delimeter;
        return ParseFileHeaders();
    }

    private async Task OnMappingChanged(IEnumerable<CsvHeaderMapper.HeaderMapping> newMapping)
    {
        _currentMapping = newMapping;

        if (OnImportCanceled.HasDelegate)
        {
            await OnImportCanceled.InvokeAsync();
        }
        StateHasChanged();
    }

    private async Task ParseFileHeaders()
    {
        try
        {
            _headers.Clear();
            if (_fileContentBytes is null || string.IsNullOrWhiteSpace(_delimeter))
            {
                if (OnImportCanceled.HasDelegate)
                {
                    await OnImportCanceled.InvokeAsync();
                }
                return;
            }

            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                Delimiter = _delimeter,
                IgnoreBlankLines = true,
                HasHeaderRecord = true
            };

            using var reader = new StreamReader(new MemoryStream(_fileContentBytes), Encoding.Default);
            using var csv = new CsvReader(reader, config);
            csv.Read();
            var headers = csv.ReadHeader();
            string[]? headerRow = csv.HeaderRecord;
            if (headerRow is not null)
            {
                _headers.AddRange(headerRow.Select(header => new CsvHeaderMapper.CsvHeader(header)));
            }
        }
        catch (Exception exc)
        {
            if (OnParseException.HasDelegate)
            {
                await OnParseException.InvokeAsync(exc);
            }
        }
    }

    private async Task Import()
    {
        try
        {
            if (_fileContentBytes is null || !OnImport.HasDelegate || string.IsNullOrWhiteSpace(_delimeter))
            {
                if (OnImportCanceled.HasDelegate)
                {
                    await OnImportCanceled.InvokeAsync();
                }
                return;
            }

            var map = GetImportMap.Invoke(_currentMapping);
            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                Delimiter = _delimeter,
                IgnoreBlankLines = true,
                HasHeaderRecord = true
            };

            using var reader = new StreamReader(new MemoryStream(_fileContentBytes), Encoding.Default);
            using var csv = new CsvReader(reader, config);
            csv.Context.RegisterClassMap(map);
            var records = csv.GetRecords<TImportModel>().ToList();
            await OnImport.InvokeAsync(records);
        }
        catch (Exception exc)
        {
            if (OnParseException.HasDelegate)
            {
                await OnParseException.InvokeAsync(exc);
            }
        }
    }
}
