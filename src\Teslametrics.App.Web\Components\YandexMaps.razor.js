const mapsStorage = new WeakMap();

export function Initialize(
	containerId,
	apiKey,
	locale,
	coordinates,
	readonly,
	singleMarkerMode,
	objRef
) {
	return new Promise((resolve, reject) => {
		const container = document.getElementById(containerId);
		if (!container) {
			reject(new Error("Map container not found"));
			return;
		}

		const existingConfig = mapsStorage.get(container);
		if (existingConfig && existingConfig.map) {
			existingConfig.map.destroy();
			mapsStorage.delete(container);
		}

		if (window.ymaps) {
			try {
				initMap(container, coordinates, readonly, singleMarkerMode, objRef);
				resolve();
			} catch (error) {
				reject(error);
			}
			return;
		}

		const script = document.createElement("script");
		script.src = `https://api-maps.yandex.ru/2.1/?apikey=${apiKey}&lang=${locale}`;
		script.async = true;
		script.onload = () => {
			ymaps.ready(() => {
				try {
					initMap(container, coordinates, readonly, singleMarkerMode, objRef);
					resolve();
				} catch (error) {
					reject(error);
				}
			});
		};
		script.onerror = (error) => reject(error);
		document.head.appendChild(script);
	});
}

function initMap(container, coordinates, readonly, singleMarkerMode, objRef) {
	// Определяем начальные параметры карты
	const defaultCenter = [55.76, 37.64];
	const defaultZoom = 10;

	// Создаем карту с временными параметрами (будут обновлены после добавления маркеров)
	const map = new ymaps.Map(
		container,
		{
			center: defaultCenter,
			zoom: defaultZoom,
			controls: [
				"zoomControl",
				"geolocationControl",
				"fullscreenControl",
				"searchControl",
			],
		},
		{
			suppressMapOpenBlock: true,
			yandexMapDisablePoiInteractivity: true,
			restrictMapArea: false,
		}
	);

	const markers = [];

	// Добавляем маркеры на карту
	if (coordinates && coordinates.length > 0) {
		for (const coord of coordinates) {
			const marker = createMarker(coord, readonly);
			map.geoObjects.add(marker);
			markers.push(marker);

			if (!readonly) {
				attachMarkerEvents(marker, container, objRef);
			}
		}

		// Автоматически масштабируем карту для отображения всех маркеров
		autoFitBounds(map, markers);
	}

	const mapConfig = {
		map,
		markers,
		objRef,
		readonly,
		singleMarkerMode,
	};
	mapsStorage.set(container, mapConfig);

	if (!readonly) {
		map.events.add("click", async function (e) {
			const coords = e.get("coords");
			const config = mapsStorage.get(container);

			if (config.singleMarkerMode) {
				config.markers.forEach((m) => config.map.geoObjects.remove(m));
				config.markers = [];
			}

			const newMarker = createMarker(
				{ latitude: coords[0], longitude: coords[1] },
				false
			);
			config.map.geoObjects.add(newMarker);
			config.markers.push(newMarker);

			attachMarkerEvents(newMarker, container, objRef);
			await updateMarkersList(config, objRef);
		});
	}
}

function createMarker(coord, readonly) {
	return new ymaps.Placemark(
		[coord.latitude, coord.longitude],
		{},
		{
			draggable: !readonly,
			iconLayout: "default#image",
			iconImageHref: "/map_pointer.svg", // путь к картинке, // путь к картинке
			iconImageSize: [30, 36],
			iconImageOffset: [-15, -36],
		}
	);
}

function attachMarkerEvents(marker, container, objRef) {
	marker.events.add("dragend", async function () {
		const config = mapsStorage.get(container);
		await updateMarkersList(config, objRef);
	});

	marker.events.add("click", async function (e) {
		e.stopPropagation();
		const config = mapsStorage.get(container);
		config.map.geoObjects.remove(marker);
		const index = config.markers.indexOf(marker);
		if (index !== -1) {
			config.markers.splice(index, 1);
		}
		await updateMarkersList(config, objRef);
	});
}

async function updateMarkersList(config, objRef) {
	if (config.singleMarkerMode) {
		if (config.markers.length > 0) {
			const marker = config.markers[0];
			const coords = marker.geometry.getCoordinates();

			// Пытаемся получить адрес через геокодирование
			try {
				const address = await reverseGeocode(coords);
				// Используем новый метод с адресом, если он доступен
				objRef.invokeMethodAsync(
					"OnMarkerPositionChangedWithAddress",
					coords[0],
					coords[1],
					address
				);
			} catch (error) {
				console.warn(
					"Geocoding failed, falling back to coordinates only:",
					error
				);
				// Fallback к старому методу без адреса
				objRef.invokeMethodAsync(
					"OnMarkerPositionChanged",
					coords[0],
					coords[1]
				);
			}
		} else {
			objRef.invokeMethodAsync("OnMarkerPositionChanged", null, null);
		}
	} else {
		const coordsList = config.markers.map((m) => {
			const coords = m.geometry.getCoordinates();
			return { latitude: coords[0], longitude: coords[1] };
		});
		objRef.invokeMethodAsync("OnMarkersUpdated", coordsList);
	}
}

async function reverseGeocode(coords) {
	return new Promise((resolve, reject) => {
		if (!window.ymaps || !window.ymaps.geocode) {
			reject(new Error("Yandex Maps geocoding API not available"));
			return;
		}

		// Выполняем обратное геокодирование
		ymaps
			.geocode(coords, {
				results: 1, // Получаем только первый результат
				kind: "house", // Приоритет домам, но может вернуть и другие типы
			})
			.then(function (res) {
				const firstGeoObject = res.geoObjects.get(0);

				if (firstGeoObject) {
					// Получаем наиболее подходящий адрес
					const address =
						firstGeoObject.getAddressLine() ||
						firstGeoObject.properties.get("text") ||
						firstGeoObject.properties.get("name");

					if (address && address.trim()) {
						resolve(address.trim());
					} else {
						reject(new Error("No address found for coordinates"));
					}
				} else {
					reject(new Error("No geocoding results found"));
				}
			})
			.catch(function (error) {
				reject(error);
			});
	});
}

/**
 * Автоматически масштабирует карту для отображения всех маркеров
 * @param {ymaps.Map} map - Экземпляр карты Yandex Maps
 * @param {Array} markers - Массив маркеров
 */
function autoFitBounds(map, markers) {
	if (!markers || markers.length === 0) {
		return;
	}

	try {
		if (markers.length === 1) {
			// Для одного маркера устанавливаем высокий зум и центрируем на маркере
			const coords = markers[0].geometry.getCoordinates();
			map.setCenter(coords, 16, {
				checkZoomRange: true,
				duration: 300,
			});
		} else {
			// Для нескольких маркеров вычисляем границы
			const bounds = [];

			markers.forEach((marker) => {
				const coords = marker.geometry.getCoordinates();
				bounds.push(coords);
			});

			// Устанавливаем границы карты с отступами
			map.setBounds(bounds, {
				checkZoomRange: true,
				zoomMargin: [20, 20, 20, 20], // Отступы в пикселях: [top, right, bottom, left]
				duration: 300,
			});
		}
	} catch (error) {
		console.warn("Error auto-fitting map bounds:", error);
	}
}

export function setMarkers(containerId, coordinates) {
	const container = document.getElementById(containerId);
	if (!container) throw new Error("Map container not found");

	const config = mapsStorage.get(container);
	if (!config) throw new Error("Map is not initialized");

	// Удаляем старые маркеры
	config.markers.forEach((marker) => {
		config.map.geoObjects.remove(marker);
	});
	config.markers = [];

	if (!coordinates || coordinates.length === 0) {
		return;
	}

	if (config.singleMarkerMode && coordinates.length > 1) {
		// В режиме одной метки игнорируем все кроме первой
		coordinates = [coordinates[0]];
	}

	for (const coord of coordinates) {
		const marker = createMarker(coord, config.readonly);
		config.map.geoObjects.add(marker);
		config.markers.push(marker);

		if (!config.readonly) {
			attachMarkerEvents(marker, container, config.objRef);
		}
	}

	// Автоматически масштабируем карту для отображения всех новых маркеров
	if (config.markers.length > 0) {
		autoFitBounds(config.map, config.markers);
	}
}

/**
 * Экспортируемая функция для ручного вызова автоматического масштабирования карты
 * @param {string} containerId - ID контейнера карты
 */
export function fitBounds(containerId) {
	const container = document.getElementById(containerId);
	if (!container) throw new Error("Map container not found");

	const config = mapsStorage.get(container);
	if (!config) throw new Error("Map is not initialized");

	if (config.markers.length > 0) {
		autoFitBounds(config.map, config.markers);
	}
}
