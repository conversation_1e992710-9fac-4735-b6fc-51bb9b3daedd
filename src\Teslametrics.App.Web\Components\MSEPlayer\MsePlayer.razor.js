import { createPlayer } from "./Core/index.js";

/**
 * Хранилище активных плееров.
 *
 * Ключом служит `cameraId`, значением — объект ядра («core»), возвращаемый
 * `createPlayer`. Карта не экспортируется наружу и предназначена только для
 * внутреннего управления жизненным циклом плееров.
 *
 * @type {Map<string|number, import('@company/player-core').Core>}
 * @private
 */
const players = new Map();

/**
 * Хранилище C# callback'ов для уведомления об изменении состояния плеера.
 *
 * @type {Map<string|number, any>}
 * @private
 */
const statusCallbacks = new Map();

/**
 * Определяет текущее состояние плеера на основе состояния feed и video элемента.
 *
 * @param {PlayerCore} core - Экземпляр PlayerCore
 * @returns {string|null} Текущее состояние плеера ("playing", "paused", "stopped") или null
 */
function getCurrentPlayerState(core) {
  if (!core || !core.feed || !core.video) {
    return null;
  }

  // Проверяем состояние feed
  const feedState = core.feed.state;

  // Если feed остановлен или в состоянии idle, плеер остановлен
  if (feedState === "stopped" || feedState === "idle") {
    return "stopped";
  }

  // Если feed на паузе, плеер на паузе
  if (feedState === "paused") {
    return "paused";
  }

  // Если feed готов, проверяем состояние video элемента
  if (feedState === "ready") {
    // Если video элемент не на паузе, значит играет
    if (!core.video.paused) {
      return "playing";
    } else {
      return "paused";
    }
  }

  // Если feed подключается, считаем что плеер пытается играть
  if (feedState === "connecting") {
    return "playing";
  }

  // По умолчанию возвращаем stopped
  return "stopped";
}

/**
 * Инициализирует (или переинициализирует) видеоплеер для указанной камеры.
 *
 * > **Blazor:** метод вызывается из C#-кода как `initializePlayer(element, id, mode, startIso, endIso, plugins)`.
 *
 * Если для той же камеры уже существует активный плеер, он будет корректно
 * остановлен и удалён перед созданием нового экземпляра.
 *
 * @param {HTMLVideoElement}   videoRef  DOM-элемент `<video>` (или контейнер),
 *                                       где будет отрисовываться видео.
 * @param {string|number}      cameraId  Уникальный идентификатор камеры —
 *                                       используется как ключ в `players`.
 * @param {'live'|'vod'|'replay'} mode   Режим воспроизведения:
 *                                       * `live`   — прямая трансляция;
 *                                       * `vod`    — видео по запросу;
 *                                       * `replay` — воспроизведение
 *                                                   архива по времени.
 * @param {string}             start     Время начала в формате ISO 8601
 *                                       (UTC) — используется в режимах
 *                                       `vod` и `replay`.
 * @param {string}             end       Время окончания в формате ISO 8601
 *                                       (UTC). В режиме `live` можно
 *                                       передать `null`.
 * @param {Array<Function>}    [plugins=[]] Массив плагинов (middleware),
 *                                          совместимых с `createPlayer`,
 *                                          применяемых при создании плеера.
 *
 * @returns {void}
 *
 * @example
 * // Прямая трансляция:
 * initializePlayer(videoEl, 42, 'live');
 *
 * @example
 * // Запрос архива за указанный интервал:
 * initializePlayer(videoEl, 42, 'replay',
 *                  '2025-05-22T09:30:00Z',
 *                  '2025-05-22T10:00:00Z');
 */
export function initializePlayer(
  videoRef,
  cameraId,
  mode,
  start,
  end,
  plugins = []
) {
  if (players.has(cameraId)) stopPlayer(cameraId);

  const { core } = createPlayer(
    videoRef,
    { cameraId, mode, start, end },
    plugins
  );
  players.set(cameraId, core);

  // Подписываемся на все события плеера для передачи в C#
  const callback = statusCallbacks.get(cameraId);
  if (callback) {
    // Обработка изменений состояния
    core.bus.on("state", (state) => {
      console.log(
        `[MSE Player] State changed for camera ${cameraId}: ${state}`
      );
      try {
        callback.invokeMethodAsync("OnCameraStatusChanged", state);
      } catch (error) {
        console.warn(
          `Failed to invoke status callback for camera ${cameraId}:`,
          error
        );
      }
    });

    // Обработка ошибок
    core.bus.on("error", (error) => {
      try {
        const errorMessage =
          error?.message || error?.toString() || "Unknown error";
        callback.invokeMethodAsync("OnCameraError", errorMessage);
      } catch (err) {
        console.warn(
          `Failed to invoke error callback for camera ${cameraId}:`,
          err
        );
      }
    });

    // Обработка обновлений времени (для timeline и других компонентов)
    core.bus.on("timeupdate", (absTime) => {
      try {
        const timeString = absTime?.toISOString() || null;
        callback.invokeMethodAsync("OnTimeUpdate", timeString);
      } catch (error) {
        console.warn(
          `Failed to invoke timeupdate callback for camera ${cameraId}:`,
          error
        );
      }
    });

    // Синхронизируем начальное состояние плеера
    // Определяем текущее состояние на основе состояния feed и video элемента
    const currentState = getCurrentPlayerState(core);
    console.log(
      `[MSE Player] Initial state for camera ${cameraId}: ${currentState} (feed: ${core.feed.state}, video paused: ${core.video.paused})`
    );

    if (currentState) {
      try {
        callback.invokeMethodAsync("OnCameraStatusChanged", currentState);
      } catch (error) {
        console.warn(
          `Failed to invoke initial status callback for camera ${cameraId}:`,
          error
        );
      }
    }
  }
}

/* ------------------------------------------------------------------ */
/* Обёртки, вызываемые из C# через JS-Interop                         */
/* ------------------------------------------------------------------ */

/**
 * Приостанавливает воспроизведение потока.
 *
 * @param {string|number} id  Идентификатор камеры (`cameraId`).
 * @returns {void}
 */
export const pauseStream = (id) => players.get(id)?.pause();

/**
 * Возобновляет воспроизведение после паузы.
 *
 * @param {string|number} id  Идентификатор камеры.
 * @returns {void}
 */
export const resumeStream = (id) => players.get(id)?.resume();

/**
 * Перематывает воспроизведение к текущему «живому» моменту.
 *
 * @param {string|number} id  Идентификатор камеры.
 * @returns {void}
 */
export const seekToLive = (id) => players.get(id)?.live();

/**
 * Полностью останавливает поток и удаляет плеер из карты `players`.
 *
 * > Важно: после вызова функции `stopStream` плеер считается уничтоженным.
 * > Для повторного воспроизведения необходимо заново вызвать
 * > `initializePlayer`.
 *
 * @param {string|number} id  Идентификатор камеры.
 * @returns {void}
 */
export const stopStream = (id) => {
  const player = players.get(id);
  if (player) {
    // Очищаем все обработчики событий перед удалением
    player.bus.all.clear();
    player.dispose();
  }
  players.delete(id);
  // Удаляем callback при остановке плеера
  statusCallbacks.delete(id);
};

/**
 * Возвращает абсолютное (серверное) время кадра, который
 * в данный момент отображается плеером.
 *
 * @param {string|number} id  Идентификатор камеры.
 * @returns {string|undefined} Абсолютное время в формате ISO 8601 UTC
 *                             или `undefined`, если плеер не найден.
 */
export const getAbsTime = (id) => players.get(id)?.pipe?.currentAbsTime;

/**
 * Возвращает время записи текущего сегмента видео.
 * Это время соответствует моменту, когда сегмент был записан камерой.
 *
 * @param {string|number} id  Идентификатор камеры.
 * @returns {string|null} Время в формате ISO 8601 или null, если время не определено.
 */
export const getCurrentSegmentTimestamp = (id) => {
  const absTime = players.get(id)?.pipe?.currentAbsTime;
  return absTime ? absTime.toISOString() : null;
};

/**
 * Возвращает текущее абсолютное время воспроизведения видео.
 * Это время соответствует реальному времени записи кадра, который отображается в данный момент.
 *
 * @param {string|number} id  Идентификатор камеры.
 * @returns {string|null} Время в формате ISO 8601 или null, если время не определено.
 */
export const getCurrentAbsoluteTime = (id) => {
  const absTime = players.get(id)?.pipe?.currentAbsTime;
  return absTime ? absTime.toISOString() : null;
};

/**
 * Регистрирует C# callback для уведомления об изменении состояния плеера.
 *
 * @param {string|number} cameraId  Идентификатор камеры.
 * @param {any} dotNetObjectRef     DotNetObjectReference для вызова C# методов.
 * @returns {void}
 */
export const registerStatusCallback = (cameraId, dotNetObjectRef) => {
  statusCallbacks.set(cameraId, dotNetObjectRef);
};

/**
 * Отменяет регистрацию C# callback для указанной камеры.
 *
 * @param {string|number} cameraId  Идентификатор камеры.
 * @returns {void}
 */
export const unregisterStatusCallback = (cameraId) => {
  statusCallbacks.delete(cameraId);
};

/**
 * Возвращает текущее состояние плеера для указанной камеры.
 *
 * @param {string|number} cameraId  Идентификатор камеры.
 * @returns {string|null} Текущее состояние плеера ("playing", "paused", "stopped") или null, если плеер не найден.
 */
export const getPlayerState = (cameraId) => {
  const core = players.get(cameraId);
  return core ? getCurrentPlayerState(core) : null;
};

/* ------------------------------------------------------------------ */
/* Fullscreen Management                                              */
/* ------------------------------------------------------------------ */

/**
 * Переключает полноэкранный режим для видеоэлемента указанной камеры.
 *
 * @param {string|number} cameraId  Идентификатор камеры.
 * @returns {void}
 */
export const toggleFullscreen = (cameraId) => {
  const core = players.get(cameraId);
  if (!core || !core.video) {
    console.warn(
      `[MsePlayer] Плеер или видеоэлемент не найден для камеры ${cameraId}`
    );
    return;
  }

  const video = core.video;

  try {
    if (isVideoFullscreen(video)) {
      exitFullscreen();
    } else {
      enterVideoFullscreen(video);
    }
  } catch (error) {
    console.error(
      `[MsePlayer] Ошибка при переключении полноэкранного режима для камеры ${cameraId}:`,
      error
    );

    // Уведомляем C# об ошибке
    const callback = statusCallbacks.get(cameraId);
    if (callback) {
      try {
        callback.invokeMethodAsync(
          "OnCameraError",
          `Ошибка полноэкранного режима: ${error.message}`
        );
      } catch (callbackError) {
        console.warn(
          `[MsePlayer] Не удалось уведомить C# об ошибке полноэкранного режима:`,
          callbackError
        );
      }
    }
  }
};

/**
 * Переводит видеоэлемент в полноэкранный режим.
 *
 * @param {HTMLVideoElement} video  Видеоэлемент для перевода в полноэкранный режим.
 * @returns {void}
 */
function enterVideoFullscreen(video) {
  if (video.requestFullscreen) {
    video.requestFullscreen();
  } else if (video.mozRequestFullScreen) {
    // Firefox
    video.mozRequestFullScreen();
  } else if (video.webkitRequestFullscreen) {
    // Chrome, Safari, Edge
    video.webkitRequestFullscreen();
  } else if (video.msRequestFullscreen) {
    // IE/Edge
    video.msRequestFullscreen();
  } else {
    throw new Error("Полноэкранный режим не поддерживается браузером");
  }
}

/**
 * Выходит из полноэкранного режима.
 *
 * @returns {void}
 */
function exitFullscreen() {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  } else if (document.mozCancelFullScreen) {
    // Firefox
    document.mozCancelFullScreen();
  } else if (document.webkitExitFullscreen) {
    // Chrome, Safari, Edge
    document.webkitExitFullscreen();
  } else if (document.msExitFullscreen) {
    // IE/Edge
    document.msExitFullscreen();
  } else {
    throw new Error(
      "Выход из полноэкранного режима не поддерживается браузером"
    );
  }
}

/**
 * Проверяет, находится ли видеоэлемент в полноэкранном режиме.
 *
 * @param {HTMLVideoElement} video  Видеоэлемент для проверки.
 * @returns {boolean} true, если видеоэлемент в полноэкранном режиме.
 */
function isVideoFullscreen(video) {
  return !!(
    document.fullscreenElement === video ||
    document.mozFullScreenElement === video ||
    document.webkitFullscreenElement === video ||
    document.msFullscreenElement === video
  );
}

/**
 * Инициализирует отслеживание изменений полноэкранного режима для всех плееров.
 * Вызывается автоматически при загрузке модуля.
 */
function initializeFullscreenTracking() {
  // Обработчики событий изменения полноэкранного режима
  const fullscreenEvents = [
    "fullscreenchange",
    "mozfullscreenchange",
    "webkitfullscreenchange",
    "msfullscreenchange",
  ];

  fullscreenEvents.forEach((eventName) => {
    document.addEventListener(eventName, handleFullscreenChange, false);
  });

  console.log(
    "[MsePlayer] Отслеживание полноэкранного режима инициализировано"
  );
}

/**
 * Обрабатывает изменения полноэкранного режима и уведомляет соответствующие плееры.
 *
 * @returns {void}
 */
function handleFullscreenChange() {
  const fullscreenElement =
    document.fullscreenElement ||
    document.mozFullScreenElement ||
    document.webkitFullscreenElement ||
    document.msFullscreenElement;

  // Проходим по всем активным плеерам и проверяем их состояние
  for (const [cameraId, core] of players) {
    if (core && core.video) {
      const isFullscreen = fullscreenElement === core.video;

      // Уведомляем C# о изменении состояния полноэкранного режима
      const callback = statusCallbacks.get(cameraId);
      if (callback) {
        try {
          callback.invokeMethodAsync("OnFullscreenChanged", isFullscreen);
        } catch (error) {
          // Игнорируем JSDisconnectedException - это нормально при навигации
          if (!error.message?.includes("JSDisconnectedException")) {
            console.warn(
              `[MsePlayer] Не удалось уведомить C# об изменении полноэкранного режима для камеры ${cameraId}:`,
              error
            );
          }
        }
      }
    }
  }
}

// Инициализируем отслеживание полноэкранного режима при загрузке модуля
initializeFullscreenTracking();
