using System.Runtime.InteropServices;
using System.Threading.Channels;
using FFmpeg.AutoGen;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace FFMpegNET;

/// <summary>
/// Класс для накопления микросегментов и преобразования их в более длинные сегменты по указанному времени.
/// Используется в CameraStreamRecorderGrain для создания архивных сегментов.
/// Реализует потоковую обработку с использованием делегата для уведомления о готовых сегментах.
/// </summary>
public unsafe sealed class MicroSegmentAccumulator : IDisposable
{
    public enum State
    {
        Initialized,
        Running,
        Stopped,
        Faulted,
        Disposed
    }

    public record Options
    {
        /// <summary>
        /// Длительность сегмента в секундах
        /// </summary>
        public int SegmentDuration { get; set; } = 15;

        /// <summary>
        /// Включить строгую проверку временных меток (может помочь с поврежденными потоками)
        /// </summary>
        public bool StrictTimestampValidation { get; set; } = true;

        public static Options Default => new Options();
    }

    #region Настройки и зависимости
    private readonly ILogger<MicroSegmentAccumulator> _logger;
    private readonly Options _options;
    private State _state;
    #endregion

    #region Потоковая обработка сегментов
    private AVFormatContext* _outputFormatContext;
    private MemoryOutputBuffer? _outputBuffer;
    private bool _headerWritten;
    private bool _trailerWritten;
    private DateTimeOffset? _segmentStartTime;
    private double _accumulatedDuration;
    private int _segmentNumber;
    private long _basePts; // Базовая временная метка для текущего сегмента
    private long _maxPtsInCurrentMicroSegment; // Максимальная PTS в текущем микросегменте
    private long _segmentStartPts; // PTS начала сегмента для вычисления продолжительности
    private long _segmentEndPts; // PTS конца сегмента для вычисления продолжительности
    private AVRational _videoTimeBase; // Временная база видеопотока
    private int _setupFailureCount; // Счетчик неудачных попыток настройки
    #endregion

    #region Управление асинхронной обработкой
    private CancellationTokenSource? _runCts;
    private Task? _inputTask;
    private Task? _outputTask;
    private Channel<(MemoryOutputBuffer, DateTimeOffset, double)>? _streamChannel;
    private Channel<MicroSegment>? _inputChannel;
    private readonly EventWaitHandle _stoppedEvent;
    #endregion

    /// <summary>
    /// Инициализирует новый экземпляр аккумулятора микросегментов
    /// </summary>
    public MicroSegmentAccumulator(ILogger<MicroSegmentAccumulator> logger, IOptions<Options>? options = null)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? Options.Default;

        _stoppedEvent = new EventWaitHandle(false, EventResetMode.ManualReset);

        _state = State.Initialized;
    }

    public void ResetFields()
    {
        _outputBuffer = null;
        _segmentStartTime = null;
        _accumulatedDuration = 0;
        _segmentNumber = 0;
        _headerWritten = false;
        _trailerWritten = false;
        _basePts = 0;
        _maxPtsInCurrentMicroSegment = 0;
        _segmentStartPts = -1;
        _segmentEndPts = 0;
        _videoTimeBase = new AVRational();
        _setupFailureCount = 0;

        _stoppedEvent.Reset();
    }

    /// <summary>
    /// Основной метод накопления микросегментов и создания длинных сегментов.
    /// Принимает микросегменты через делегат и уведомляет о готовых сегментах через onNextSegment.
    /// </summary>
    public Task Run(Func<Segment, Task> onNextSegment, CancellationToken cancellationToken)
    {
        ThrowIfDisposed();

        if (_state is State.Running)
        {
            throw new InvalidOperationException("Accumulator already running");
        }

        ResetFields();

        _runCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

        _inputChannel = Channel.CreateBounded<MicroSegment>(new BoundedChannelOptions(128)
        {
            SingleWriter = false,
            SingleReader = true
        });

        _streamChannel = Channel.CreateBounded<(MemoryOutputBuffer, DateTimeOffset, double)>(new BoundedChannelOptions(16)
        {
            SingleWriter = true,
            SingleReader = true
        });

        _inputTask = MicroSegmentAccumulatorHelper.ProcessInputMicroSegments(
            _inputChannel.Reader,
            _streamChannel.Writer,
            CreateOutputContext,
            ProcessSingleMicroSegment,
            CloseCurrentSegment,
            CleanupOutputContext,
            _runCts.Token);

        _outputTask = MicroSegmentAccumulatorHelper.ProcessOutputStream(_streamChannel, onNextSegment, _runCts.Token);

        _state = State.Running;

        return Task.WhenAll(_inputTask, _outputTask)
            .ContinueWith(task =>
            {
                _inputTask.Dispose();
                _outputTask.Dispose();
                _runCts.Dispose();
                _runCts = null;

                _state = task.IsFaulted ? State.Faulted : State.Stopped;

                _stoppedEvent.Set();
            }, cancellationToken);
    }

    /// <summary>
    /// Запрашивает плавную остановку обработки после завершения текущего сегмента
    /// </summary>
    public void RequestGracefulStop()
    {
        ThrowIfDisposed();

        if (_state is State.Running)
        {
            _inputChannel!.Writer.Complete();
            _runCts!.Cancel();
        }
    }

    /// <summary>
    /// Создает выходной контекст для потоковой записи
    /// </summary>
    private void CreateOutputContext()
    {
        // Создаем выходной контекст для MPEGTS
        fixed (AVFormatContext** ppOutputContext = &_outputFormatContext)
        {
            if (ffmpeg.avformat_alloc_output_context2(ppOutputContext, null, "mpegts", null) < 0)
            {
                throw new ApplicationException("Failed to create output context for TS file");
            }
        }

        // Инициализируем буфер в памяти
        _outputBuffer = new MemoryOutputBuffer();
        _outputFormatContext->pb = _outputBuffer.AvioContext;

        // Устанавливаем флаги для выходного формата
        _outputFormatContext->flags |= ffmpeg.AVFMT_FLAG_IGNDTS;
        _outputFormatContext->flags |= ffmpeg.AVFMT_FLAG_GENPTS;
        _outputFormatContext->flags &= ~ffmpeg.AVFMT_FLAG_NOBUFFER;
    }

    /// <summary>
    /// Освобождает ресурсы выходного контекста
    /// </summary>
    private void CleanupOutputContext()
    {
        try
        {
            if (_outputFormatContext != null)
            {
                if (_headerWritten && !_trailerWritten)
                {
                    try
                    {
                        ffmpeg.av_write_trailer(_outputFormatContext);
                        _trailerWritten = true;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error writing trailer during cleanup");
                    }
                }

                ffmpeg.avformat_free_context(_outputFormatContext);
                _outputFormatContext = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during output context cleanup");
        }
        finally
        {
            _outputBuffer?.Dispose();
            _outputBuffer = null;
            _headerWritten = false;
            _trailerWritten = false;
        }
    }

    /// <summary>
    /// Добавляет микросегмент в очередь для обработки
    /// </summary>
    public void AddMicroSegment(MicroSegment microSegment)
    {
        ThrowIfDisposed();

        if (_state is not State.Running)
        {
            throw new InvalidOperationException("Accumulator is not running");
        }

        if (microSegment.Payload == null)
        {
            _logger.LogWarning("Received micro segment with null payload");
            return;
        }

        _logger.LogDebug("Adding micro segment to queue: Start time: {StartTime}, Duration: {Duration:F3} sec, Size: {Size} bytes",
            microSegment.StartTime.ToString("yyyy-MM-dd HH:mm:ss.fff"),
            microSegment.Duration,
            microSegment.Payload.Length);

        if (!_inputChannel!.Writer.TryWrite(microSegment))
        {
            _logger.LogError("Failed to write micro segment to input channel");
        }
    }

    /// <summary>
    /// Принудительно создает сегмент из накопленных данных
    /// </summary>
    public void FlushSegment()
    {
        ThrowIfDisposed();

        if (_state is State.Running)
        {
            _inputChannel!.Writer.Complete();
        }
    }

    /// <summary>
    /// Обрабатывает один микросегмент
    /// </summary>
    private void ProcessSingleMicroSegment(MicroSegment microSegment)
    {
        try
        {
            // Устанавливаем время начала сегмента при получении первого микросегмента
            if (_segmentStartTime == null)
            {
                _segmentStartTime = microSegment.StartTime;

                _logger.LogDebug("Setting up output streams from first micro segment");

                // Настраиваем выходные потоки на основе первого микросегмента
                bool setupSuccess = SetupOutputStreamsFromMicroSegment(microSegment);

                // Проверяем, что контекст был правильно настроен
                if (!setupSuccess || _outputFormatContext == null || _outputFormatContext->nb_streams == 0)
                {
                    _setupFailureCount++;
                    _logger.LogWarning("Failed to setup output context from micro segment (attempt {Attempt}), resetting segment start time", _setupFailureCount);
                    _segmentStartTime = null; // Сбрасываем, чтобы попробовать со следующим микросегментом

                    // Если слишком много неудач подряд, возможно поток полностью поврежден
                    if (_setupFailureCount >= 10)
                    {
                        _logger.LogError("Too many consecutive setup failures ({Count}), stream may be corrupted", _setupFailureCount);
                    }

                    return;
                }

                _logger.LogDebug("Successfully set up {StreamCount} output streams", _outputFormatContext->nb_streams);
                _setupFailureCount = 0; // Сбрасываем счетчик при успешной настройке
                WriteHeader();
            }

            // Проверяем, что контекст готов для записи
            if (_outputFormatContext == null || !_headerWritten)
            {
                _logger.LogWarning("Output context not ready, skipping micro segment");
                return;
            }

            // Сразу обрабатываем микросегмент
            _maxPtsInCurrentMicroSegment = 0; // Сбрасываем перед обработкой
            ProcessMicroSegmentDirectly(microSegment);

            // Обновляем базовую временную метку для следующего микросегмента
            if (_maxPtsInCurrentMicroSegment > 0)
            {
                var oldBasePts = _basePts;
                // Вычисляем продолжительность одного кадра (при 25 fps = 90000/25 = 3600 единиц)
                var frameDuration = _videoTimeBase.den > 0 ? _videoTimeBase.den / 25 : 3600; // Предполагаем 25 fps

                // Устанавливаем базовую PTS для следующего микросегмента
                // Добавляем продолжительность кадра для плавного перехода
                _basePts = _maxPtsInCurrentMicroSegment + frameDuration;
                _logger.LogDebug("Updated base PTS: {OldBasePts} -> {NewBasePts} (max PTS: {MaxPts}, frame duration: {FrameDuration})",
                    oldBasePts, _basePts, _maxPtsInCurrentMicroSegment, frameDuration);
            }

            // Сбрасываем _segmentStartPts для следующего микросегмента
            _segmentStartPts = -1;

            // Вычисляем продолжительность на основе PTS, а не суммируем продолжительности микросегментов
            if (_segmentEndPts > 0 && _videoTimeBase.den > 0)
            {
                _accumulatedDuration = (double)_segmentEndPts * _videoTimeBase.num / _videoTimeBase.den;
            }
            else
            {
                // Fallback: используем продолжительность микросегмента, если PTS недоступна
                _accumulatedDuration += microSegment.Duration;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing micro segment");
            return;
        }

        _logger.LogDebug("Processed micro segment. Accumulated duration: {Duration:F3} sec, Target: {Target} sec",
            _accumulatedDuration, _options.SegmentDuration);

        // Проверяем, достигли ли мы целевой длительности
        if (_accumulatedDuration >= _options.SegmentDuration)
        {
            // Закрываем текущий сегмент (передача в канал происходит здесь)
            CloseCurrentSegment();

            // Если запрошена остановка, завершаем после закрытия сегмента
            if (_runCts!.IsCancellationRequested)
            {
                return;
            }

            // Сбрасываем аккумулятор для следующего сегмента
            // (теперь безопасно, так как сегмент уже передан в канал)
            ResetAccumulator();

            // Создаем новый контекст только если предыдущий был правильно очищен
            if (_outputFormatContext == null)
            {
                CreateOutputContext();
            }
        }
    }

    /// <summary>
    /// Настраивает выходные потоки на основе микросегмента
    /// </summary>
    /// <returns>true если настройка прошла успешно, false в противном случае</returns>
    private bool SetupOutputStreamsFromMicroSegment(MicroSegment microSegment)
    {
        AVFormatContext* inputFormatContext = null;
        MemoryInputBuffer? inputBuffer = null;

        try
        {
            // Создаем буфер в памяти для анализа микросегмента
            inputBuffer = new MemoryInputBuffer(microSegment.Payload!);

            // Создаем входной контекст
            inputFormatContext = ffmpeg.avformat_alloc_context();
            if (inputFormatContext == null)
            {
                _logger.LogWarning("Failed to allocate input format context");
                return false;
            }

            // Устанавливаем кастомный AVIO контекст
            inputFormatContext->pb = inputBuffer.AvioContext;

            // Открываем входной контекст из буфера памяти
            if (ffmpeg.avformat_open_input(&inputFormatContext, null, null, null) < 0)
            {
                _logger.LogWarning("Failed to open input context from micro segment, skipping");
                return false;
            }

            if (ffmpeg.avformat_find_stream_info(inputFormatContext, null) < 0)
            {
                _logger.LogWarning("Failed to find stream info in micro segment, skipping");
                return false;
            }

            // Проверяем, что есть хотя бы один поток
            if (inputFormatContext->nb_streams == 0)
            {
                _logger.LogWarning("No streams found in micro segment, skipping");
                return false;
            }

            // Проверяем, что первый поток (видео) имеет валидные параметры
            var firstStream = inputFormatContext->streams[0];
            if (firstStream->codecpar->codec_type != AVMediaType.AVMEDIA_TYPE_VIDEO)
            {
                _logger.LogWarning("First stream is not video, skipping micro segment");
                return false;
            }

            // Сохраняем временную базу видеопотока для правильного вычисления продолжительности
            _videoTimeBase = firstStream->time_base;

            // Копируем потоки из входного контекста в выходной
            for (int i = 0; i < inputFormatContext->nb_streams; i++)
            {
                var inputStream = inputFormatContext->streams[i];
                var outputStream = ffmpeg.avformat_new_stream(_outputFormatContext, null);

                if (outputStream == null)
                {
                    throw new ApplicationException($"Failed to create output stream {i}");
                }

                // Копируем параметры кодека
                if (ffmpeg.avcodec_parameters_copy(outputStream->codecpar, inputStream->codecpar) < 0)
                {
                    throw new ApplicationException($"Error copying codec parameters for stream {i}");
                }

                outputStream->time_base = inputStream->time_base;
                outputStream->codecpar->codec_tag = 0;
            }

            return true; // Успешно настроили потоки
        }
        finally
        {
            if (inputFormatContext != null)
            {
                ffmpeg.avformat_close_input(&inputFormatContext);
            }

            inputBuffer?.Dispose();
        }
    }

    /// <summary>
    /// Записывает заголовок выходного потока
    /// </summary>
    private void WriteHeader()
    {
        if (_outputFormatContext == null)
        {
            throw new InvalidOperationException("Output format context is null");
        }

        if (_outputFormatContext->nb_streams == 0)
        {
            throw new InvalidOperationException("No streams configured in output context");
        }

        try
        {
            if (ffmpeg.avformat_write_header(_outputFormatContext, null) < 0)
            {
                throw new ApplicationException("Error writing TS header");
            }
            _headerWritten = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to write header");
            throw;
        }
    }

    /// <summary>
    /// Обрабатывает микросегмент напрямую, записывая его пакеты в выходной поток
    /// </summary>
    private void ProcessMicroSegmentDirectly(MicroSegment microSegment)
    {
        AVFormatContext* inputFormatContext = null;
        MemoryInputBuffer? inputBuffer = null;

        try
        {
            // Создаем буфер в памяти для микросегмента
            inputBuffer = new MemoryInputBuffer(microSegment.Payload!);

            // Создаем входной контекст
            inputFormatContext = ffmpeg.avformat_alloc_context();
            if (inputFormatContext == null)
            {
                _logger.LogWarning("Failed to allocate input format context");
                return;
            }

            // Устанавливаем кастомный AVIO контекст
            inputFormatContext->pb = inputBuffer.AvioContext;

            if (ffmpeg.avformat_open_input(&inputFormatContext, null, null, null) < 0)
            {
                _logger.LogWarning("Failed to open micro segment for processing, skipping");
                return;
            }

            if (ffmpeg.avformat_find_stream_info(inputFormatContext, null) < 0)
            {
                _logger.LogWarning("Failed to find stream info in micro segment, skipping");
                return;
            }

            // Читаем и переписываем пакеты
            var packet = ffmpeg.av_packet_alloc();
            try
            {
                while (ffmpeg.av_read_frame(inputFormatContext, packet) >= 0)
                {
                    // Создаем копию пакета для выходного потока
                    var outPacket = ffmpeg.av_packet_alloc();
                    try
                    {
                        if (ffmpeg.av_packet_ref(outPacket, packet) < 0)
                        {
                            _logger.LogWarning("Error copying packet");
                            continue;
                        }

                        // Проверяем валидность временных меток (если включена строгая проверка)
                        if (_options.StrictTimestampValidation)
                        {
                            if (packet->pts != ffmpeg.AV_NOPTS_VALUE && packet->pts < 0)
                            {
                                _logger.LogWarning("Invalid PTS value: {PTS}, skipping packet", packet->pts);
                                continue;
                            }
                            if (packet->dts != ffmpeg.AV_NOPTS_VALUE && packet->dts < 0)
                            {
                                _logger.LogWarning("Invalid DTS value: {DTS}, skipping packet", packet->dts);
                                continue;
                            }
                        }

                        // Устанавливаем правильный индекс потока
                        outPacket->stream_index = packet->stream_index;

                        // Правильно масштабируем временные метки
                        if (packet->stream_index < inputFormatContext->nb_streams &&
                            packet->stream_index < _outputFormatContext->nb_streams)
                        {
                            var inputStream = inputFormatContext->streams[packet->stream_index];
                            var outputStream = _outputFormatContext->streams[packet->stream_index];

                            // Сначала масштабируем временные метки
                            ffmpeg.av_packet_rescale_ts(outPacket, inputStream->time_base, outputStream->time_base);

                            // Обрабатываем временные метки для обеспечения непрерывности
                            if (outPacket->pts != ffmpeg.AV_NOPTS_VALUE)
                            {
                                // Для первого микросегмента в сегменте запоминаем начальную PTS
                                if (_segmentStartPts == -1)
                                {
                                    _segmentStartPts = outPacket->pts;
                                }

                                // Нормализуем PTS: убираем начальное смещение и добавляем базовую PTS
                                var normalizedPts = outPacket->pts - _segmentStartPts + _basePts;
                                outPacket->pts = normalizedPts;

                                // Для видеопотока отслеживаем максимальную PTS
                                if (packet->stream_index == 0)
                                {
                                    _maxPtsInCurrentMicroSegment = Math.Max(_maxPtsInCurrentMicroSegment, normalizedPts);
                                    _segmentEndPts = normalizedPts;
                                }
                            }

                            if (outPacket->dts != ffmpeg.AV_NOPTS_VALUE)
                            {
                                // Аналогично для DTS
                                var normalizedDts = outPacket->dts - _segmentStartPts + _basePts;
                                outPacket->dts = normalizedDts;
                            }
                        }

                        // Записываем пакет в выходной контекст
                        if (ffmpeg.av_interleaved_write_frame(_outputFormatContext, outPacket) < 0)
                        {
                            _logger.LogWarning("Error writing packet to output");
                        }
                    }
                    finally
                    {
                        ffmpeg.av_packet_free(&outPacket);
                    }

                    ffmpeg.av_packet_unref(packet);
                }
            }
            finally
            {
                ffmpeg.av_packet_free(&packet);
            }
        }
        finally
        {
            if (inputFormatContext != null)
            {
                ffmpeg.avformat_close_input(&inputFormatContext);
            }

            inputBuffer?.Dispose();
        }
    }

    /// <summary>
    /// Закрывает текущий сегмент и отправляет его в выходной канал
    /// </summary>
    private void CloseCurrentSegment()
    {
        ThrowIfDisposed();

        if (_outputFormatContext != null && _outputBuffer != null && _segmentStartTime != null && _headerWritten && !_trailerWritten)
        {
            try
            {
                // Записываем завершающие данные сегмента
                ffmpeg.av_write_trailer(_outputFormatContext);
                _trailerWritten = true;

                _outputBuffer.Stream.Position = 0;

                // Сохраняем текущие значения перед передачей в канал
                var segmentStartTime = _segmentStartTime.Value;
                var segmentDuration = _accumulatedDuration;

                // Создаем копию буфера для передачи в канал, чтобы избежать проблем с освобождением ресурсов
                var bufferCopy = new MemoryOutputBuffer();
                _outputBuffer.Stream.Position = 0;
                _outputBuffer.Stream.CopyTo(bufferCopy.Stream);

                WriteSegment(bufferCopy, segmentStartTime, segmentDuration);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error closing current segment");
            }
        }
    }

    /// <summary>
    /// Обработка готового сегмента
    /// </summary>
    /// <param name="outputBuffer">Буфер памяти с данными сегмента в формате MPEGTS</param>
    /// <param name="startTime">Время начала сегмента</param>
    /// <param name="duration">Продолжительность сегмента в секундах</param>
    private void WriteSegment(MemoryOutputBuffer outputBuffer, DateTimeOffset startTime, double duration)
    {
        ThrowIfDisposed();

        _logger.LogInformation(
            "Segment {SegmentNumber:D3} | Start time: {Time} | Duration: {Duration:F3} sec | Size: {Size} bytes",
            _segmentNumber,
            startTime.ToString("yyyy-MM-dd HH:mm:ss.fff"),
            duration,
            outputBuffer.Stream.Length
        );

        outputBuffer.Stream.Position = 0;

        if (!_streamChannel!.Writer.TryWrite((outputBuffer, startTime, duration)))
        {
            _logger.LogError("Failed to write segment to channel");
        }
    }
    private void ResetAccumulator()
    {
        CleanupOutputContext();
        _segmentStartTime = null;
        _accumulatedDuration = 0;
        _segmentNumber++;
        _headerWritten = false;
        _trailerWritten = false;
        _basePts = 0;
        _maxPtsInCurrentMicroSegment = 0;
        _segmentStartPts = -1;
        _segmentEndPts = 0;
        _videoTimeBase = new AVRational();
    }

    private void ThrowIfDisposed()
    {
        ObjectDisposedException.ThrowIf(_state is State.Disposed, nameof(MicroSegmentAccumulator));
    }

    ~MicroSegmentAccumulator()
    {
        Dispose(false);
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    private void Dispose(bool disposing)
    {
        if (_state is State.Disposed)
            return;

        if (disposing)
        {
            if (_state is State.Running)
            {
                _runCts?.Cancel();

                _stoppedEvent.WaitOne();
            }

            // Освобождаем управляемые ресурсы
            CleanupOutputContext();
            _stoppedEvent.Dispose();
        }

        _state = State.Disposed;
    }
}
