namespace Teslametrics.App.Web.Services.UserDevice;

public class UserAgentMiddleware
{
    private readonly RequestDelegate _next;

    public UserAgentMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public Task Invoke(HttpContext context, IUserDeviceService userDeviceService)
    {
        if (context.Request.Headers.TryGetValue("User-Agent", out var userAgent) && !string.IsNullOrEmpty(userAgent))
        {
            userDeviceService.SetUserAgent(userAgent!);
        }

        return _next(context);
    }
}