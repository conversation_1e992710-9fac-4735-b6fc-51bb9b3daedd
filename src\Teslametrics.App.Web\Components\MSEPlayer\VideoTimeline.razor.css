.video-timeline {
    width: 100%;
    background: #2a2a2a;
    border-radius: 8px;
    padding: 16px;
    color: #ffffff;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #444;
}

.timeline-title {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
}

.timeline-range {
    font-size: 12px;
    color: #cccccc;
    font-family: 'Courier New', monospace;
}

.timeline-container {
    position: relative;
    height: 60px;
    margin: 16px 0;
}

.timeline-track {
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, #1a1a1a, #333333, #1a1a1a);
    border-radius: 4px;
    border: 1px solid #555;
}

.timeline-marks {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.timeline-mark {
    position: absolute;
    top: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 4px 0;
    transform: translateX(-50%);
}

.mark-line {
    width: 2px;
    height: 20px;
    background: #666;
    border-radius: 1px;
}

.mark-label {
    font-size: 10px;
    color: #aaa;
    font-family: 'Courier New', monospace;
    white-space: nowrap;
    margin-top: 2px;
}

.timeline-position {
    position: absolute;
    top: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transform: translateX(-50%);
}

.position-indicator {
    width: 4px;
    height: 40px;
    background: linear-gradient(to bottom, #ff4444, #cc0000);
    border-radius: 2px;
    box-shadow: 0 0 8px rgba(255, 68, 68, 0.6);
    animation: pulse 2s infinite;
}

.position-tooltip {
    position: absolute;
    top: -30px;
    background: #000;
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-family: 'Courier New', monospace;
    white-space: nowrap;
    border: 1px solid #ff4444;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.position-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #ff4444;
}

.timeline-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid #444;
}

.current-time-display {
    font-size: 13px;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    background: #1a1a1a;
    padding: 6px 12px;
    border-radius: 4px;
    border: 1px solid #555;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 8px rgba(255, 68, 68, 0.6);
    }
    50% {
        box-shadow: 0 0 16px rgba(255, 68, 68, 0.9);
    }
    100% {
        box-shadow: 0 0 8px rgba(255, 68, 68, 0.6);
    }
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
    .video-timeline {
        padding: 12px;
    }
    
    .timeline-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .timeline-container {
        height: 50px;
    }
    
    .mark-label {
        font-size: 9px;
    }
    
    .position-tooltip {
        font-size: 10px;
        padding: 3px 6px;
    }
    
    .current-time-display {
        font-size: 12px;
        padding: 4px 8px;
    }
}
