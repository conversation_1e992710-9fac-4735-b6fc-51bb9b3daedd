@using Teslametrics.App.Web.Components.DragAndDrop
@using Teslametrics.Shared
@using Teslametrics.App.Web.Features.Main.Cameras.TreeView.Organization
@using Teslametrics.App.Web.Features.Main.Cameras.TreeView.Folder
@using Teslametrics.App.Web.Features.Main.Cameras.TreeView.Camera
@attribute [StreamRendering(true)]
@inherits InteractiveBaseComponent
<div class="mud-height-full sidebar">
    @if (Items.Any())
    {
        <DragAndDropContainer TValue="TreeItemData<Guid>"
                              CanDropAsync="CanDropAsync"
                              CanDragAsync="CanDragAsync"
                              OnDrop="OnDrop">
            <MudTreeView T="Guid"
                         Items="@Items"
                         SelectionMode="SelectionMode.ToggleSelection"
                         Class="tree overflow-auto px-2 pt-2"
                         AutoExpand="true"
                         SelectedValue="@_selected">
                <ItemTemplate>
                    @{
                        var presenter = (TreeItemPresenter)context;
                        switch (presenter.Type)
                        {
                            case ItemType.Organization:
                                <OrganizationItemComponent Item="presenter"
                                                           OnSelect="SelectOrganizationAsync"
                                                           SelectedOrganizationId="OrganizationId"
                                                           @key="presenter.Value" />
                                break;
                            case ItemType.Folder:
                                <FolderItemComponent Item="presenter"
                                                     SelectedFolderId="FolderId"
                                                     OnSelect="SelectFolderAsync"
                                                     @key="presenter.Value" />
                                break;
                            case ItemType.Camera:
                                <CameraItemComponent Item="presenter"
                                                     @key="presenter.Value" />
                                break;
                        }
                    }
                </ItemTemplate>
            </MudTreeView>
        </DragAndDropContainer>
    }
    <TreeViewNoItemsFound HasItems="IsLoading || Items.Any()"
                          LastRefreshTime="_lastRefreshTime"
                          Refresh="RefreshAsync" />
    <TreeViewPreloaderComponent IsLoading="@IsLoading" />
    @if (_totalPages > 1)
    {
        <MudPagination Color="Color.Primary"
                       Count="@_totalPages"
                       Selected="@_currentPage"
                       SelectedChanged="PageChanged"
                       Class="justify-center" />
    }
    else
    {
        <div style="height: 4px;"></div>
    }
    <MudStack Row="true"
              Class="mb-4 pl-4 pt-2"
              Style="min-height: 36px;"
              AlignItems="AlignItems.Center">
        <div class="d-flex flex-row align-center bg3 mud-width-full mud-height-full justify-center br_8">
            <MudText Typo="Typo.caption">Последнее обновление: @_lastRefreshTime.ToLocalTime()</MudText>
        </div>
        <div class="d-flex flex-row align-center gap-3">
            <MudIconButton OnClick="RefreshAsync"
                           Icon="@Icons.Material.Outlined.Refresh"
                           Disabled="IsLoading"
                           Color="Color.Primary"
                           Size="Size.Medium"
                           Variant="Variant.Filled" />
            <SubscriptionErrorComponent Show="@(!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))"
                                        RetrySubscribe="SubscribeAsync" />
        </div>
    </MudStack>
</div>