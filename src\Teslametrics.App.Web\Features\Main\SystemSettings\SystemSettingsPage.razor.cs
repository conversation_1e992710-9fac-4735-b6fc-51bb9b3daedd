using Blazor.Diagrams;
using Blazor.Diagrams.Core.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.JSInterop;
using System.Collections.ObjectModel;
using System.Reactive.Linq;
using Teslametrics.App.Web.Components.Diagrams.Behaivor;
using Teslametrics.App.Web.Components.Diagrams.Models;
using Teslametrics.App.Web.Components.Diagrams.Widget;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.SystemSettings;

public partial class SystemSettingsPage : IAsyncDisposable
{
	#region [Type Definitions]
	public class SystemSettingsPageModel
	{
		public class Point
		{
			public double X { get; set; }
			public double Y { get; set; }

			public Point(double x = 0, double y = 0)
			{
				X = x;
				Y = y;
			}
		}

		public class Size
		{
			public double Width { get; set; }
			public double Height { get; set; }

			public Size(double width = 1920, double height = 1080)
			{
				Width = width;
				Height = height;
			}
		}

		public List<CityModel> Cities { get; set; } = [];
	}

	public class CityModel
	{
		public Guid Id { get; set; } = Guid.NewGuid();
		public string Name { get; set; } = string.Empty;
		public List<BuildingModel> Buildings { get; set; } = [];
	}

	public class BuildingModel
	{
		public Guid Id { get; set; } = Guid.NewGuid();
		public string Address { get; set; } = string.Empty;
		public List<FloorModel> Floors { get; set; } = [];
		public Coordinates? Coordinates { get; set; }
	}

	public class FloorModel
	{
		public Guid Id { get; set; } = Guid.NewGuid();
		public int Number { get; set; }
		public List<RoomModel> Rooms { get; set; } = [];

		public PlanModel? Plan { get; set; }

		public class PlanModel
		{
			public SystemSettingsPageModel.Point Position { get; set; }
			public SystemSettingsPageModel.Size Size { get; set; }

			public PlanModel(SystemSettingsPageModel.Point point, SystemSettingsPageModel.Size size)
			{
				Position = point;
				Size = size;
			}
		}
	}

	public class RoomModel
	{
		public Guid Id { get; set; } = Guid.NewGuid();
		public string Name { get; set; } = string.Empty;
		public List<FreezerModel> Freezers { get; set; } = [];
		public List<ISensorModel> Sensors { get; set; } = [];
		public CamModel? Camera { get; set; }
		public SystemSettingsPageModel.Point Position { get; set; } = new();
		public IEnumerable<SystemSettingsPageModel.Point> ZonePoints { get; set; } = [];
	}

	public class CamModel
	{
		public Guid Id { get; set; }
		public string Name { get; set; } = string.Empty;
		public SystemSettingsPageModel.Point Position { get; set; } = new();
	}

	public class FreezerModel
	{
		public Guid Id { get; set; } = Guid.NewGuid();
		public string Name { get; set; } = string.Empty;
		public ObservableCollection<ISensorModel> Sensors { get; set; } = [];
		public SystemSettingsPageModel.Point Position { get; set; } = new();
	}

	public class FreezerNode : NodeModel
	{
		public FreezerModel Freezer { get; set; } = null!;
	}

	public class CameraNode : NodeModel
	{
		public CamModel Camera { get; set; } = null!;
	}

	public static SystemSettingsPageModel ToPageModel(PageModel model)
	{
		return new SystemSettingsPageModel
		{
			Cities = model.Cities.Select(city => new CityModel
			{
				Id = city.Id,
				Name = city.Name,
				Buildings = city.Buildings.Select(building => new BuildingModel
				{
					Id = building.Id,
					Address = building.Address,
					Coordinates = building.Coordinates,
					Floors = building.Floors.Select(floor => new FloorModel
					{
						Id = floor.Id,
						Number = floor.Number,
						Plan = floor.Plan is null ? null : new FloorModel.PlanModel(new SystemSettingsPageModel.Point(floor.Plan.Position.X, floor.Plan.Position.Y), new SystemSettingsPageModel.Size(floor.Plan.Size.Width, floor.Plan.Size.Height)),
						Rooms = floor.Rooms.Select(room => new RoomModel
						{
							Id = room.Id,
							Name = room.Name,
							Freezers = room.Fridges.Select(f => new FreezerModel
							{
								Id = f.Id,
								Name = f.Name,
								Sensors = new(f.Sensors),
								Position = new SystemSettingsPageModel.Point(f.Position.X, f.Position.Y)
							}).ToList(),
							Sensors = room.Sensors,
							Camera = room.Camera == null ? null : new CamModel
							{
								Id = room.Camera.Id,
								Name = room.Camera.Name,
								Position = new SystemSettingsPageModel.Point(room.Camera.Position.X, room.Camera.Position.Y)
							},
							Position = new SystemSettingsPageModel.Point(room.Position.X, room.Position.Y),
							ZonePoints = room.ZonePoints.Select(p => new SystemSettingsPageModel.Point(p.X, p.Y))
						}).ToList()
					}).ToList()
				}).ToList()
			}).ToList()
		};
	}

	public static PageModel ToUseCaseModel(SystemSettingsPageModel model)
	{
		return new PageModel
		{
			Cities = model.Cities.Select(city => new City
			{
				Id = city.Id,
				Name = city.Name,
				Buildings = city.Buildings.Select(building => new Building
				{
					Id = building.Id,
					Address = building.Address,
					Coordinates = building.Coordinates,
					Floors = building.Floors.Select(floor => new Floor
					{
						Id = floor.Id,
						Number = floor.Number,
						Plan = floor.Plan is null ? null : new Floor.PlanModel(new Teslametrics.Shared.Point(floor.Plan.Position.X, floor.Plan.Position.Y), new Teslametrics.Shared.Size(floor.Plan.Size.Width, floor.Plan.Size.Height)),
						Rooms = floor.Rooms.Select(room => new Room
						{
							Id = room.Id,
							Name = room.Name,
							Fridges = room.Freezers.Select(f => new Teslametrics.Shared.Fridge
							{
								Id = f.Id,
								Name = f.Name,
								Sensors = f.Sensors.ToList(),
								Position = new Teslametrics.Shared.Point(f.Position.X, f.Position.Y)
							}).ToList(),
							Position = new Teslametrics.Shared.Point(room.Position.X, room.Position.Y),
							ZonePoints = room.ZonePoints.Select(p => new Teslametrics.Shared.Point(p.X, p.Y)),
							Sensors = room.Sensors,
							Camera = room.Camera == null ? null : new Teslametrics.Shared.Cam(room.Camera.Id, room.Camera.Name, new Teslametrics.Shared.Point(room.Camera.Position.X, room.Camera.Position.Y))
						}).ToList()
					}).ToList()
				}).ToList()
			}).ToList()
		};
	}

	#endregion [Type Definitions]

	private PolygonNodeBehaivor _polygonBehaivor;
	private object? _currentSelection;
	private CityModel? _selectedCity;
	private BuildingModel? _selectedBuilding;
	private FloorModel? _selectedFloor;
	private FreezerModel? _selectedFreezer;
	private RoomModel? _selectedRoom;

	private CamModel? _camera;

	[Inject]
	private IJSRuntime _jsRuntime { get; set; } = null!;

	private BlazorDiagram Diagram { get; set; }
	SystemSettingsPageModel _page = new();

	// Отдельный список для хранения изображений этажей
	private List<SaveRoomSettingsUseCase.Command.FloorImage> _floorImages = new();

	// Словарь для хранения временных URL изображений (ключ - Id этажа, значение - временный URL)
	private Dictionary<Guid, string> _tempImageUrls = new();

	public SystemSettingsPage()
	{
		var options = new Blazor.Diagrams.Options.BlazorDiagramOptions()
		{
			AllowMultiSelection = true,
			Zoom =
				{
					Enabled = true,
				}
		};

		Diagram = new BlazorDiagram(options);
		Diagram.RegisterComponent<FreezerNode, FreezerNodeWidget>();
		Diagram.RegisterComponent<CameraNode, CameraNodeWidget>();
		Diagram.RegisterComponent<ImgNodeWidget.ImgNodeModel, ImgNodeWidget>();

		_polygonBehaivor = new PolygonNodeBehaivor(Diagram);
		Diagram.RegisterBehavior(new VertexDragBehavior(Diagram));
		Diagram.RegisterBehavior(_polygonBehaivor);
		Diagram.RegisterComponent<BasePolygonNodeModel, BasePolygonNodeWidget>();
	}

	protected override async Task OnInitializedAsync()
	{
		await FetchAsync();

		await base.OnInitializedAsync();
	}

	private async Task FetchAsync()
	{
		GetRoomSettingsUseCase.Response? response = null;
		try
		{
			response = await ScopeFactory.MediatorSend(new GetRoomSettingsUseCase.Query());
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось загрузить настройки из-за ошибки сообщения с сервером.", MudBlazor.Severity.Error);
		}

		if (response is null) return;

		switch (response.Result)
		{
			case GetRoomSettingsUseCase.Result.Success:
				_page = ToPageModel(response.Page);

				// Загружаем изображения из ответа
				_floorImages.Clear();

				// Преобразуем изображения из ответа в формат SaveRoomSettingsUseCase.Command.FloorImage
				foreach (var floorImage in response.FloorImages)
				{
					_floorImages.Add(new SaveRoomSettingsUseCase.Command.FloorImage(
						floorImage.FloorId,
						floorImage.Image,
						floorImage.ContentType));
				}

				// Создаем временные URL для всех изображений
				foreach (var floorImage in _floorImages)
				{
					// Создаем временный URL только если его еще нет
					if (!_tempImageUrls.ContainsKey(floorImage.FloorId))
					{
						var tempUrl = await CreateTempImageUrlAsync(floorImage.Image, floorImage.ContentType);
						if (!string.IsNullOrEmpty(tempUrl))
						{
							_tempImageUrls[floorImage.FloorId] = tempUrl;
						}
					}
				}
				break;

			case GetRoomSettingsUseCase.Result.PlanNotFound:
				Snackbar.Add($"Не удалось получить настройки. Вероятно они ещё не были заданы", MudBlazor.Severity.Warning);
				break;
			case GetRoomSettingsUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(SystemSettingsPage), nameof(GetRoomSettingsUseCase));
				Snackbar.Add($"Не удалось получить настройки из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(SystemSettingsPage), nameof(GetRoomSettingsUseCase), response.Result);
				Snackbar.Add($"Не удалось получить настройки из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	public void AddRoom()
	{
		if (_selectedFloor is null) return;
		var room = new RoomModel()
		{
			Name = "Комната без названия " + (_selectedFloor.Rooms.Count + 1)
		};

		var node = new RoomZoneNodeModel(room.Id, [], new Blazor.Diagrams.Core.Geometry.Point(0, 0));
		Diagram.Nodes.Add(node);
		node.Locked = true;

		_selectedFloor.Rooms.Add(room);
		OnSelectedRoomChanged(room);
	}

	public void OnSelectedRoomChanged(RoomModel? room)
	{
		// Синхронизируем позиции всех узлов при изменении выбора
		SyncAllNodePositions();

		_selectedRoom = room;
		_currentSelection = room is null ? _selectedFloor : room;

		if (room is not null)
		{
			_camera = room.Camera;
		}
		_selectedFreezer = null;

		// Вызываем метод обработки изменения выбора
		OnCurrentSelectionChanged();
	}

	public void OnSelectedFreezerChanged(FreezerModel? freezer)
	{
		// Синхронизируем позиции всех узлов при изменении выбора
		SyncAllNodePositions();

		_selectedFreezer = freezer;
		_currentSelection = freezer is null ? _selectedRoom : freezer;

		// Вызываем метод обработки изменения выбора
		OnCurrentSelectionChanged();
	}

	private void AddCamera()
	{
		if (_selectedRoom is null || _camera is null) return;
		_selectedRoom.Camera = _camera;
		Diagram.Nodes.Add(new CameraNode()
		{
			Camera = _selectedRoom.Camera,
			Position = new Blazor.Diagrams.Core.Geometry.Point(0, 0)
		});

		// Синхронизируем позиции после добавления нового узла
		SyncAllNodePositions();

		//_camera = null;
	}

	public void AddFreezer()
	{
		if (_selectedRoom is null) return;

		var freezer = new FreezerModel()
		{
			Name = "Холодильник без названия " + (_selectedRoom.Freezers.Count + 1)
		};

		_selectedRoom.Freezers.Add(freezer);
		_selectedFreezer = freezer;
		_currentSelection = freezer;

		Diagram.Nodes.Add(new FreezerNode()
		{
			Freezer = freezer,
			Position = new Blazor.Diagrams.Core.Geometry.Point(freezer.Position.X, freezer.Position.Y)
		});

		// Синхронизируем позиции после добавления нового узла
		SyncAllNodePositions();

		// Вызываем метод обработки изменения выбора
		OnCurrentSelectionChanged();
	}

	// Получение URL для изображения этажа
	private string GetImageUrl(Guid floorId)
	{
		// Если есть временный URL, используем его
		if (_tempImageUrls.TryGetValue(floorId, out var tempUrl))
		{
			return tempUrl;
		}

		// Если изображение есть в списке, но для него нет временного URL,
		// создаем его асинхронно (в следующем цикле обновления)
		if (_floorImages.Any(img => img.FloorId == floorId))
		{
			// Запускаем асинхронную задачу для создания временного URL
			_ = Task.Run(async () =>
			{
				var floorImage = _floorImages.First(img => img.FloorId == floorId);
				var newTempUrl = await CreateTempImageUrlAsync(floorImage.Image, floorImage.ContentType);
				if (!string.IsNullOrEmpty(newTempUrl))
				{
					_tempImageUrls[floorId] = newTempUrl;
					// Вызываем StateHasChanged для обновления UI
					await InvokeAsync(StateHasChanged);
				}
			});
		}

		// Пока временный URL не создан, используем URL для загрузки из базы данных
		return $"/planimages/{floorId}";
	}

	// Создание временного URL для изображения с помощью JSInterop
	private async Task<string> CreateTempImageUrlAsync(byte[] imageBytes, string contentType)
	{
		try
		{
			// Вызываем JavaScript-функцию для создания временного URL
			return await _jsRuntime.InvokeAsync<string>("imageHelper.createObjectURL", imageBytes, contentType);
		}
		catch (JSDisconnectedException)
		{
			// Если соединение с JS уже разорвано, ничего делать не нужно
			return string.Empty;
		}
		catch (Exception ex)
		{
			Logger.LogError(ex, "Ошибка при создании временного URL: {Message}", ex.Message);
			// В случае ошибки возвращаем пустую строку
			return string.Empty;
		}
	}

	// Освобождение временного URL
	private async Task RevokeTempImageUrlAsync(string url)
	{
		try
		{
			// Вызываем JavaScript-функцию для освобождения временного URL
			await _jsRuntime.InvokeVoidAsync("imageHelper.revokeObjectURL", url);
		}
		catch (JSDisconnectedException)
		{
			// Если соединение с JS уже разорвано, ничего делать не нужно
		}
		catch (Exception ex)
		{
			Logger.LogError(ex, "Ошибка при освобождении временного URL: {Message}", ex.Message);
		}
	}

	// Загрузка файла изображения
	private async Task OnInputFileChanged(IBrowserFile file)
	{
		if (_selectedFloor is null) return;

		if (!file.ContentType.StartsWith("image/"))
		{
			await _jsRuntime.InvokeVoidAsync("alert", "Пожалуйста, выберите файл изображения");
			return;
		}

		// Читаем файл в память
		using var memoryStream = new MemoryStream();
		await file.OpenReadStream().CopyToAsync(memoryStream);
		byte[] imageBytes = memoryStream.ToArray();

		// Создаем план этажа, если его нет
		if (_selectedFloor.Plan is null)
		{
			_selectedFloor.Plan = new FloorModel.PlanModel(new SystemSettingsPageModel.Point(), new SystemSettingsPageModel.Size());
		}

		// Добавляем изображение в список
		var floorImage = new SaveRoomSettingsUseCase.Command.FloorImage(_selectedFloor.Id, imageBytes, file.ContentType);

		// Удаляем предыдущие изображения этого этажа, если они были
		_floorImages.RemoveAll(img => img.FloorId == _selectedFloor.Id);

		// Добавляем новое изображение
		_floorImages.Add(floorImage);

		// Освобождаем предыдущий временный URL, если он был
		if (_tempImageUrls.TryGetValue(_selectedFloor.Id, out var oldUrl))
		{
			await RevokeTempImageUrlAsync(oldUrl);
		}

		// Создаем временный URL для предварительного просмотра
		var tempUrl = await CreateTempImageUrlAsync(imageBytes, file.ContentType);
		if (!string.IsNullOrEmpty(tempUrl))
		{
			_tempImageUrls[_selectedFloor.Id] = tempUrl;
		}

		// Добавляем изображение на диаграмму
		Diagram.Nodes.Add(new ImgNodeWidget.ImgNodeModel()
		{
			Src = GetImageUrl(_selectedFloor.Id),
			Position = new Blazor.Diagrams.Core.Geometry.Point(0, 0),
			Order = 0,
			Locked = true
		});

		// Синхронизируем позиции после добавления нового узла
		SyncAllNodePositions();
	}

	private async Task DeleteImage()
	{
		if (_selectedFloor is null) return;

		// Удаляем узел изображения из диаграммы
		var imgNode = Diagram.Nodes.FirstOrDefault(x => x is ImgNodeWidget.ImgNodeModel);
		if (imgNode != null)
		{
			Diagram.Nodes.Remove(imgNode);
		}

		// Удаляем изображения этого этажа из списка
		_floorImages.RemoveAll(img => img.FloorId == _selectedFloor.Id);

		// Освобождаем временный URL, если он был
		if (_tempImageUrls.TryGetValue(_selectedFloor.Id, out var tempUrl))
		{
			await RevokeTempImageUrlAsync(tempUrl);
			_tempImageUrls.Remove(_selectedFloor.Id);
		}

		// Удаляем план этажа, если он есть
		if (_selectedFloor.Plan != null)
		{
			_selectedFloor.Plan = null;
		}
	}

	private async Task SaveAsync()
	{
		SaveRoomSettingsUseCase.Response? response = null;
		try
		{
			// Синхронизируем позиции всех узлов перед сохранением
			SyncAllNodePositions();

			var commandModel = ToUseCaseModel(_page);
			response = await ScopeFactory.MediatorSend(new SaveRoomSettingsUseCase.Command(commandModel, _floorImages));
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось сохранить настройки из-за ошибки сообщения с сервером.", MudBlazor.Severity.Error);
		}

		if (response is null) return;

		switch (response.Result)
		{
			case SaveRoomSettingsUseCase.Result.Success:
				Snackbar.Add($"Сохранено", MudBlazor.Severity.Success);
				break;
			case SaveRoomSettingsUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(SystemSettingsPage), nameof(SaveRoomSettingsUseCase));
				Snackbar.Add($"Не удалось сохранить настройки из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(SystemSettingsPage), nameof(SaveRoomSettingsUseCase), response.Result);
				Snackbar.Add($"Не удалось сохранить настройки из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;

		}
	}

	private async Task<IEnumerable<CamModel>> SearchAsync(string value, CancellationToken token)
	{
		GetCameraListUseCase.Response? response = null;
		try
		{
			await SetLoadingAsync(true);
			var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
			response = await ScopeFactory.MediatorSend(new GetCameraListUseCase.Query(userId, value), cancellationToken: token);
		}
		catch (Exception exc)
		{
			await SetLoadingAsync(false);
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось получить список камер из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
			return Enumerable.Empty<CamModel>();
		}

		switch (response.Result)
		{
			case GetCameraListUseCase.Result.Success:
				return response.Items.Select(x => new CamModel() { Id = x.Id, Name = x.Name });
			case GetCameraListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(SystemSettingsPage), nameof(GetCameraListUseCase));
				Snackbar.Add($"Не удалось получить список камер из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(SystemSettingsPage), nameof(GetCameraListUseCase), response.Result);
				Snackbar.Add($"Не удалось получить список камер из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;

		}
		return [];
	}

	private void OnSelectedCityChanged(CityModel? city)
	{
		// Синхронизируем позиции всех узлов при изменении выбора
		SyncAllNodePositions();

		_selectedCity = city;
		_currentSelection = city;

		_selectedFloor = null;
		_selectedFreezer = null;

		_selectedRoom = null;

		_selectedBuilding = null;

		// Вызываем метод обработки изменения выбора
		OnCurrentSelectionChanged();
	}

	private void AddCity()
	{
		CityModel city = new();
		_page.Cities.Add(city);
		OnSelectedCityChanged(city);
	}

	private void AddBuilding()
	{
		if (_selectedCity is null) return;
		BuildingModel buildingModel = new();
		_selectedCity.Buildings.Add(buildingModel);
		OnSelectedBuildingChanged(buildingModel);
	}

	private void OnSelectedBuildingChanged(BuildingModel? building)
	{
		// Синхронизируем позиции всех узлов при изменении выбора
		SyncAllNodePositions();

		_selectedBuilding = building;
		_currentSelection = building is null ? _selectedCity : building;

		_selectedFloor = null;
		_selectedFreezer = null;
		_selectedRoom = null;

		// Вызываем метод обработки изменения выбора
		OnCurrentSelectionChanged();
	}

	private void AddFloor()
	{
		if (_selectedBuilding is null) return;
		FloorModel floorModel = new();
		_selectedBuilding.Floors.Add(floorModel);
		OnSelectedFloorChanged(floorModel);

		if (_selectedFloor is not null)
		{
			// Проверяем, есть ли изображения для этого этажа
			if (_floorImages.Any(img => img.FloorId == _selectedFloor.Id))
			{
				Diagram.Nodes.Add(new ImgNodeWidget.ImgNodeModel()
				{
					Src = GetImageUrl(_selectedFloor.Id),
					Position = new Blazor.Diagrams.Core.Geometry.Point(0, 0),
					Locked = true
				});
			}

			foreach (var room in _selectedFloor.Rooms)
			{
				foreach (var freezer in room.Freezers)
				{
					Diagram.Nodes.Add(new FreezerNode()
					{
						Freezer = freezer,
						Position = new Blazor.Diagrams.Core.Geometry.Point(freezer.Position.X, freezer.Position.Y),
						Order = 1,
					});
				}
			}

			// Синхронизируем позиции после добавления новых узлов
			SyncAllNodePositions();
		}
	}

	private void OnSelectedFloorChanged(FloorModel? floor)
	{
		// Синхронизируем позиции всех узлов при изменении выбора
		SyncAllNodePositions();

		_selectedFloor = floor;
		_currentSelection = floor is null ? _selectedBuilding : floor;

		_selectedFreezer = null;
		_selectedRoom = null;

		Diagram.Nodes.Clear();
		if (floor is not null)
		{
			// Проверяем, есть ли изображения для этого этажа
			if (_floorImages.Any(img => img.FloorId == floor.Id))
			{
				Diagram.Nodes.Add(new ImgNodeWidget.ImgNodeModel()
				{
					Src = GetImageUrl(floor.Id),
					Position = new Blazor.Diagrams.Core.Geometry.Point(0, 0),
					Locked = true
				});
			}
			foreach (var room in floor.Rooms)
			{
				var zonePoints = room.ZonePoints.Select(p => new Blazor.Diagrams.Core.Geometry.Point(p.X, p.Y));
				var node = new RoomZoneNodeModel(room.Id, zonePoints, new Blazor.Diagrams.Core.Geometry.Point(room.Position.X, room.Position.Y));
				Diagram.Nodes.Add(node);

				foreach (var fridge in room.Freezers)
				{
					Diagram.Nodes.Add(new FreezerNode()
					{
						Freezer = fridge,
						Position = new Blazor.Diagrams.Core.Geometry.Point(fridge.Position.X, fridge.Position.Y),
						Order = 1,
					});
				}

				if (room.Camera is not null)
				{
					Diagram.Nodes.Add(new CameraNode()
					{
						Camera = room.Camera,
						Position = new Blazor.Diagrams.Core.Geometry.Point(room.Camera.Position.X, room.Camera.Position.Y),
						Order = 1,
					});
				}
			}
		}

		// Вызываем метод обработки изменения выбора
		OnCurrentSelectionChanged();
	}

	private void RemoveCity(CityModel city)
	{
		_page.Cities.Remove(city);
	}

	private void RemoveBuilding(BuildingModel building)
	{
		_selectedCity?.Buildings.Remove(building);
	}

	private void RemoveFloor(FloorModel floor)
	{
		_selectedBuilding?.Floors.Remove(floor);
	}


	private void RemoveRoom(RoomModel room)
	{
		if (Diagram.Nodes.FirstOrDefault(x => x is RoomZoneNodeModel roomZone && roomZone.Id == room.Id) is RoomZoneNodeModel node)
		{
			Diagram.Nodes.Remove(node);
		}
		if (room.Camera is not null)
		{
			if (Diagram.Nodes.FirstOrDefault(x => x is CameraNode cameraNode && cameraNode.Camera.Id == room.Camera.Id) is CameraNode cameraNode)
			{
				Diagram.Nodes.Remove(cameraNode);
			}
		}
		foreach (var freezer in room.Freezers)
		{
			if (Diagram.Nodes.FirstOrDefault(x => x is FreezerNode freezerNode && freezerNode.Freezer.Id == freezer.Id) is FreezerNode freezerNode)
			{
				Diagram.Nodes.Remove(freezerNode);
			}
		}
		_selectedFloor?.Rooms.Remove(room);
	}

	public void RemoveFridge(FreezerModel fridge)
	{
		if (Diagram.Nodes.FirstOrDefault(x => x is FreezerNode freezerNode && freezerNode.Freezer.Id == fridge.Id) is FreezerNode freezerNode)
		{
			Diagram.Nodes.Remove(freezerNode);
		}
		_selectedRoom?.Freezers.Remove(fridge);
	}


	/// <summary>
	/// Метод, вызываемый при изменении _currentSelection
	/// </summary>
	private void OnCurrentSelectionChanged()
	{
		EndRoomZoneEdit();
	}

	private void StartRoomZoneEdit(RoomModel room)
	{
		if (_currentSelection is not RoomModel selectedRoom) return;
		if (room.Id != selectedRoom.Id) return;

		if (Diagram.Nodes.FirstOrDefault(x => x is RoomZoneNodeModel roomZone && roomZone.Id == room.Id) is not RoomZoneNodeModel node) return;

		_polygonBehaivor.StartZoneEdit(node);
	}

	private void EndRoomZoneEdit()
	{
		var previousNode = _polygonBehaivor.ActiveNode;
		_polygonBehaivor.EndZoneEdit();
		if (previousNode is not null)
		{
			previousNode.Locked = true;
		}
	}

	private void OnCoordinatesWithAddressChanged(Components.YandexMaps.CoordinatesWithAddress? coordinatesWithAddress)
	{
		if (_currentSelection is not BuildingModel building) return;
		if (coordinatesWithAddress is null) return;
		if (coordinatesWithAddress.Address is null) return;

		building.Address = coordinatesWithAddress.Address;
	}

	#region [Diagram Data Proxy]
	/// <summary>
	/// Синхронизирует позиции всех узлов диаграммы с моделями страницы
	/// </summary>
	private void SyncAllNodePositions()
	{
		// Синхронизация позиций камер
		var cameras = Diagram.Nodes.Where(node => node is CameraNode).Cast<CameraNode>();
		foreach (var cameraNode in cameras)
		{
			cameraNode.Camera.Position = new SystemSettingsPageModel.Point(cameraNode.Position.X, cameraNode.Position.Y);
		}

		// Синхронизация позиций холодильников
		var fridgeNodes = Diagram.Nodes.Where(node => node is FreezerNode).Cast<FreezerNode>();
		foreach (var fridgeNode in fridgeNodes)
		{
			fridgeNode.Freezer.Position = new SystemSettingsPageModel.Point(fridgeNode.Position.X, fridgeNode.Position.Y);
		}

		// Синхронизация позиций и точек зон комнат
		var roomNodes = Diagram.Nodes.Where(node => node is RoomZoneNodeModel).Cast<RoomZoneNodeModel>();
		if (_selectedFloor is not null)
		{
			foreach (var roomNode in roomNodes)
			{
				var room = _selectedFloor.Rooms.FirstOrDefault(r => r.Id == roomNode.Id);
				if (room is not null)
				{
					room.Position = new SystemSettingsPageModel.Point(roomNode.Position.X, roomNode.Position.Y);
					room.ZonePoints = roomNode.VertexPoints.Select(p => new SystemSettingsPageModel.Point(p.Position.X, p.Position.Y));
				}
			}
		}
	}
	#endregion

	public async ValueTask DisposeAsync()
	{
		// Освобождаем все временные URL при уничтожении компонента
		foreach (var url in _tempImageUrls.Values)
		{
			await RevokeTempImageUrlAsync(url);
		}
		_tempImageUrls.Clear();

		GC.SuppressFinalize(this);
	}
}
