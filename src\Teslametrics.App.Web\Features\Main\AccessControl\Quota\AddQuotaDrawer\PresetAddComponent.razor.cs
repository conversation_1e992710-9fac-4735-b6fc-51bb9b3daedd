using System.Reactive;
using FluentValidation;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Quota.AddQuotaDrawer;

public partial class PresetAddComponent
{
	private class PresetItem
	{
		public string Name { get; set; } = string.Empty;
		public GetCameraPresetListUseCase.Response.Item? Preset { get; set; }
		public int TotalQuota { get; set; } = 10;
		public int RetentionPeriodDays { get; set; } = 1;
		public int StorageLimitMb { get; set; } = 1024;
	}


	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	private bool _subscribing;
	private readonly FluentValueValidator<int> _validator = new(x => x.Must(limit => limit == -1 || limit >= 1).WithMessage("Значение не должно быть равно нулю"));

	private bool _isValid => _items.Count > 0 && _items.All(x => x.TotalQuota > 0 || x.TotalQuota == -1);

	private HashSet<PresetItem> _items = [];
	private SubscribePresetListUseCase.Response? _subscriptionResult;

	#region Parameters
	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }
	#endregion [Parameter]

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();

		_items.Add(new());

		await SubscribeAsync();
	}

	private async Task<IEnumerable<GetCameraPresetListUseCase.Response.Item>> SearchAsync(string search, CancellationToken token)
	{
		GetCameraPresetListUseCase.Response? response = null;
		try
		{
			await SetLoadingAsync(true);
			response = await ScopeFactory.MediatorSend(new GetCameraPresetListUseCase.Query(OrganizationId, 0, 25, search), token);
		}
		catch (TaskCanceledException)
		{
			response = null;
			return [];
		}
		catch (Exception ex)
		{
			response = null;
			Snackbar.Add("Не удалось получить список пресетов. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
			return [];
		}

		if (response is null) return [];
		switch (response.Result)
		{
			case GetCameraPresetListUseCase.Result.Success:
				break;

			case GetCameraPresetListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при получении списка пресетов", MudBlazor.Severity.Error);
				return [];

			case GetCameraPresetListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(PresetAddComponent), nameof(GetCameraPresetListUseCase));
				Snackbar.Add($"Не удалось получить список пресетов из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;

			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(PresetAddComponent), nameof(GetCameraPresetListUseCase), response.Result);
				Snackbar.Add($"Не удалось получить список пресетов из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;
		}
		return response.Items;
	}

	private async Task SubscribeAsync()
	{
		try
		{
			await SetSubscribingAsync(true);
			Unsubscribe();
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribePresetListUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError)));
			switch (_subscriptionResult.Result)
			{
				case SubscribePresetListUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;
				case SubscribePresetListUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
					break;
				case SubscribePresetListUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribePresetListUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось подписаться на события из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult is not null && _subscriptionResult.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	private Task SetSubscribingAsync(bool isSubscribing)
	{
		return InvokeAsync(() => _subscribing = isSubscribing);
	}

	#region  [Actions]
	private async Task CancelAsync() => await Drawer.HideAsync();

	private async Task SubmitAsync()
	{
		try
		{
			await SetLoadingAsync(true);
			var response = await ScopeFactory.MediatorSend(new CreateCameraQuotaUseCase.Command(OrganizationId, _items.Select(x => new CreateCameraQuotaUseCase.Quota(x.Preset?.Id, x.Name, x.TotalQuota, x.RetentionPeriodDays, x.StorageLimitMb)).ToList()));
			switch (response.Result)
			{
				case CreateCameraQuotaUseCase.Result.Success:
					Snackbar.Add("Квоты успешно назначены", MudBlazor.Severity.Success);
					await Drawer.HideAsync();
					break;
				case CreateCameraQuotaUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при назначении квоты", MudBlazor.Severity.Error);
					break;
				case CreateCameraQuotaUseCase.Result.OrganizationNotFound:
					Snackbar.Add("Указанной организации не существует", MudBlazor.Severity.Error);
					break;
				case CreateCameraQuotaUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(CreateCameraQuotaUseCase)}: {response.Result}");
			}
		}
		catch (Exception exc)
		{
			Snackbar.Add("Не удалось создать список квот для организации. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(exc, exc.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	#endregion

	#region [Event Handlers]

	protected async void OnAppEventHandler(object appEvent)
	{
		switch (appEvent)
		{
			case SubscribePresetListUseCase.UpdatedEvent updatedEto:
				//if (_gridRef is not null)
				//{
				//	await _gridRef.ReloadServerData();
				//	await UpdateViewAsync();
				//}
				break;
			case SubscribePresetListUseCase.DeletedEvent deletedEto:
				var toRemove = _items.Where(x => x.Preset?.Id == deletedEto.PresetId);
				foreach (var item in toRemove)
				{
					item.Preset = null;
				}
				await UpdateViewAsync();
				break;
			default:
				break;
		}
	}

	protected void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
	}
	#endregion

	private void AddNewQuota() => _items.Add(new());

	private void RemoveQuota(PresetItem context)
	{
		if (_items.Count > 1)
			_items.Remove(context);
	}
}