using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.Cameras;
using Teslametrics.Core.Domain.Folders;
using Teslametrics.Core.Domain.Folders.Events;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.TransactionManager;

namespace Teslametrics.App.Web.Features.Main.Cameras.FolderDeleteDialog;

public static class DeleteFolderUseCase
{
    public record Command(Guid Id) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; init; }
        public bool IsSuccess => Result == Result.Success;

        public Response(Result result)
        {
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        CannotDeleteNotEmptyFolder
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Id).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IFolderRepository _folderRepository;
        private readonly ICameraRepository _cameraRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IFolderRepository folderRepository,
                       ICameraRepository cameraRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {

            _validator = validator;
            _folderRepository = folderRepository;
            _cameraRepository = cameraRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            if (await _cameraRepository.HasCamerasInFolderAsync(request.Id, cancellationToken))
            {
                return new Response(Result.CannotDeleteNotEmptyFolder);
            }

            var folder = await _folderRepository.FindAsync(request.Id, cancellationToken);
            if (folder is null)
            {
                return new Response(Result.Success);
            }

            await _folderRepository.DeleteAsync(request.Id, cancellationToken);
            await _folderRepository.SaveChangesAsync(cancellationToken);

            List<object> events = [new FolderDeletedEvent(folder.Id, folder.OrganizationId)];

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            return new Response(Result.Success);
        }
    }
}