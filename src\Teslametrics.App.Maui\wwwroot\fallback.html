<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <title>Connection Error - Teslametrics</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .container {
            max-width: 400px;
            padding: 40px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        h1 {
            font-size: 24px;
            margin-bottom: 16px;
            font-weight: 600;
        }

        p {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .status {
            margin-top: 20px;
            font-size: 14px;
            opacity: 0.7;
        }

        .loading {
            display: none;
            margin-top: 20px;
        }

        .spinner {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 2px solid white;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📡</div>
        <h1>Connection Error</h1>
        <p>Unable to connect to Teslametrics server. Please check your internet connection and try again.</p>
        
        <button class="retry-btn" onclick="retryConnection()">
            Retry Connection
        </button>
        
        <div class="loading">
            <div class="spinner"></div>
            <div style="margin-top: 10px;">Connecting...</div>
        </div>
        
        <div class="status" id="status">
            Tap retry to attempt reconnection
        </div>
    </div>

    <script>
        function retryConnection() {
            // Show loading state
            document.querySelector('.retry-btn').style.display = 'none';
            document.querySelector('.loading').style.display = 'block';
            document.getElementById('status').textContent = 'Attempting to reconnect...';
            
            // Notify the native app to retry
            if (window.chrome && window.chrome.webview) {
                // For WebView2 on Windows
                window.chrome.webview.postMessage('retry');
            } else if (window.webkit && window.webkit.messageHandlers) {
                // For iOS/macOS
                window.webkit.messageHandlers.retry.postMessage('retry');
            } else {
                // Fallback - try to reload after a delay
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            }
        }

        // Reset UI state
        function resetUI() {
            document.querySelector('.retry-btn').style.display = 'inline-block';
            document.querySelector('.loading').style.display = 'none';
            document.getElementById('status').textContent = 'Tap retry to attempt reconnection';
        }

        // Auto-retry after 30 seconds
        setTimeout(() => {
            if (document.querySelector('.retry-btn').style.display !== 'none') {
                retryConnection();
            }
        }, 30000);
    </script>
</body>
</html>
