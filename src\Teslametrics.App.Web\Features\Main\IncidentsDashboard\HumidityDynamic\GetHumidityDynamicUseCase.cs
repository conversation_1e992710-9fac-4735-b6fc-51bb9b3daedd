using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.HumidityDynamic;

public static class GetHumidityDynamicUseCase
{
    public record Query(DateTimeOffset DateFrom, DateTimeOffset DateTo, Guid CityId, Guid BuildingId, Guid FloorId, Guid RoomId, Guid FridgeId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<PeriodData> CurrentPeriod { get; init; } // Значения в текущем периоде (Множество точек значений)
        public List<PeriodData> PreviousPeriod { get; init; } // Значения в предыдущем периоде (Множество точек значений)
        public ReferenceValues? Reference { get; init; }
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<PeriodData> currentPeriod, List<PeriodData> previousPeriod, ReferenceValues? reference)
        {
            CurrentPeriod = currentPeriod;
            PreviousPeriod = previousPeriod;
            Reference = reference;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            CurrentPeriod = [];
            PreviousPeriod = [];
            Result = result;
        }

        /// <summary>
        /// Представляет информацию о влажности за определенный период
        /// </summary>
        /// <param name="Date">Дата и время значения в часовом поясе пользователя</param>
        /// <param name="Humidity">Влажность в этот момент</param>
        public record PeriodData(DateTime Date, double Humidity);

        /// <summary>
        /// Информация о референсных значениях влажности (минимальных/максимальных) для датчика. Предполагается, что в оборудовании только 1 датчик влажности.
        /// </summary>
        /// <param name="MinHumidity"></param>
        /// <param name="MaxHumidity"></param>
        public record ReferenceValues(double MinHumidity, double MaxHumidity);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        PlanNotFound,
        CityNotFound,
        BuildingNotFound,
        FloorNotFound,
        RoomNotFound,
        FridgeNotFound,
        HumiditySensorNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(x => x.DateFrom)
                .LessThan(x => x.DateTo)
                .WithMessage("DateFrom must be less than DateTo");

            RuleFor(x => x.DateTo)
                .GreaterThan(x => x.DateFrom)
                .WithMessage("DateTo must be greater than DateFrom");

            RuleFor(x => x.CityId)
                .NotEmpty()
                .WithMessage("CityId is required");

            RuleFor(x => x.BuildingId)
                .NotEmpty()
                .WithMessage("BuildingId is required");

            RuleFor(x => x.FloorId)
                .NotEmpty()
                .WithMessage("FloorId is required");

            RuleFor(x => x.RoomId)
                .NotEmpty()
                .WithMessage("RoomId is required");

            RuleFor(x => x.FridgeId)
                .NotEmpty()
                .WithMessage("FridgeId is required");
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            if (!await CheckTableExistsAsync())
            {
                return new Response(Result.PlanNotFound);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Plans.Props.Page)
                .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

            var pageJson = await _dbConnection.ExecuteScalarAsync<string?>(template.RawSql);

            if (string.IsNullOrEmpty(pageJson))
            {
                return new Response(Result.PlanNotFound);
            }

            var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

            var city = page.Cities.FirstOrDefault(c => c.Id == request.CityId);

            if (city is null)
            {
                return new Response(Result.CityNotFound);
            }

            var building = city.Buildings.FirstOrDefault(b => b.Id == request.BuildingId);

            if (building is null)
            {
                return new Response(Result.BuildingNotFound);
            }

            var floor = building.Floors.FirstOrDefault(f => f.Id == request.FloorId);

            if (floor is null)
            {
                return new Response(Result.FloorNotFound);
            }

            var room = floor.Rooms.FirstOrDefault(r => r.Id == request.RoomId);

            if (room is null)
            {
                return new Response(Result.RoomNotFound);
            }

            var fridge = room.Fridges.FirstOrDefault(f => f.Id == request.FridgeId);

            if (fridge is null)
            {
                return new Response(Result.FridgeNotFound);
            }

            var sensor = fridge.Sensors.FirstOrDefault(s => s is HumidityModel);

            if (sensor is null)
            {
                return new Response(Result.HumiditySensorNotFound);
            }

            var humiditySensor = (HumidityModel)sensor;

            var tableName = $"{Db.SensorHistory.Table}_{GuidUtility.Create(Guid.Parse("A4ED63A9-698B-4B11-A0A6-E13118E3C34D"), humiditySensor.Name).ToString("N")}";

            template = SqlQueryBuilder.Create()
                .Select(Db.SensorHistory.Columns.Timestamp)
                .Select(Db.SensorHistory.Columns.Value)
                .Where(Db.SensorHistory.Columns.Timestamp, ":DateFrom", SqlOperator.GreaterThanOrEqual, new { DateFrom = request.DateFrom.UtcDateTime })
                .Where(Db.SensorHistory.Columns.Timestamp, ":DateTo", SqlOperator.LessThanOrEqual, new { DateTo = request.DateTo.UtcDateTime })
                .Build(QueryType.Standard, tableName, RowSelection.AllRows);

            var currentPeriodHistory = await _dbConnection.QueryAsync<History>(template.RawSql, template.Parameters);

            // Вычисляем предыдущий период с сохранением времени суток
            // Например, если текущий период: 01:00 первого дня - 23:00 третьего дня (3 дня)
            // то предыдущий период будет: 01:00 за 3 дня до первого дня - 23:00 за 3 дня до третьего дня
            var periodDuration = request.DateTo - request.DateFrom;
            var daysInPeriod = (int)Math.Ceiling(periodDuration.TotalDays);

            var previousPeriodStart = request.DateFrom.AddDays(-daysInPeriod);
            var previousPeriodEnd = request.DateTo.AddDays(-daysInPeriod);

            // Получаем данные за предыдущий период
            template = SqlQueryBuilder.Create()
                .Select(Db.SensorHistory.Columns.Timestamp)
                .Select(Db.SensorHistory.Columns.Value)
                .Where(Db.SensorHistory.Columns.Timestamp, ":DateFrom", SqlOperator.GreaterThanOrEqual, new { DateFrom = previousPeriodStart.UtcDateTime })
                .Where(Db.SensorHistory.Columns.Timestamp, ":DateTo", SqlOperator.LessThanOrEqual, new { DateTo = previousPeriodEnd.UtcDateTime })
                .Build(QueryType.Standard, tableName, RowSelection.AllRows);

            var previousPeriodHistory = await _dbConnection.QueryAsync<History>(template.RawSql, template.Parameters);

            // Получаем offset пользователя из запроса для корректного отображения времени
            // Это гарантирует, что пользователь увидит данные в своем часовом поясе
            var userOffset = request.DateFrom.Offset;

            // Преобразуем данные в формат PeriodData с учетом часового пояса пользователя
            // ToOffset(userOffset) конвертирует UTC время из БД в часовой пояс пользователя
            var currentPeriodData = currentPeriodHistory
                .Select(h => new Response.PeriodData(h.TimeStamp.ToOffset(userOffset).DateTime, h.Value))
                .OrderBy(p => p.Date)
                .ToList();

            var previousPeriodData = previousPeriodHistory
                .Select(h => new Response.PeriodData(h.TimeStamp.ToOffset(userOffset).DateTime, h.Value))
                .OrderBy(p => p.Date)
                .ToList();

            // Создаем референсные значения из настроек датчика
            var reference = new Response.ReferenceValues(humiditySensor.MinHumidity, humiditySensor.MaxHumidity);

            return new Response(currentPeriodData, previousPeriodData, reference);
        }

        private async Task<bool> CheckTableExistsAsync()
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = Db.Plans.Table });

            return tableExists > 0;
        }
    }

    public record History(DateTimeOffset TimeStamp, double Value);
}