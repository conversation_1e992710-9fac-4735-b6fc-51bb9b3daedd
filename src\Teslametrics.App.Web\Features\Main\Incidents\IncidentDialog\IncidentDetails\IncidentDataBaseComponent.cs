using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Teslametrics.App.Web.Features.Main.Incidents.IncidentDialog.IncidentDetails;

public abstract class IncidentDataBaseComponent : ComponentBase, IAsyncDisposable
{
    private PeriodicTimer? _timer;
    private readonly CancellationTokenSource _cts = new();

    // фиксированный пояс UTC+3
    private static readonly TimeSpan Gmt3Offset = TimeSpan.FromHours(3);

    protected TimeSpan Duration;
    protected bool IsResolved => ResolvedAd is not null;

    [Inject]
    protected IJSRuntime JS { get; set; } = null!;

    [Parameter]
    public DateTimeOffset FiredAt { get; set; }

    [Parameter]
    public DateTimeOffset? ResolvedAd { get; set; }

    public async ValueTask DisposeAsync()
    {
        _cts.Cancel();
        _timer?.Dispose();
        _cts.Dispose();
        await Task.CompletedTask;
    }

    protected override void OnInitialized()
    {
        // первая калькуляция
        CalculateDuration();

        // запускаем тиканье, если нужно
        if (ResolvedAd is null)
        {
            _timer = new(TimeSpan.FromSeconds(1));
            _ = TickAsync();
        }
    }

    protected static string FormatGmt3(DateTimeOffset dto)
    {
        var local = dto.ToOffset(Gmt3Offset);
        return local.ToString("dd.MM.yyyy, HH:mm");
    }

    private async Task TickAsync()
    {
        while (await _timer!.WaitForNextTickAsync(_cts.Token))
        {
            CalculateDuration();
            await InvokeAsync(StateHasChanged);
        }
    }

    private void CalculateDuration()
    {
        var until = ResolvedAd ?? DateTimeOffset.UtcNow;
        Duration = until - FiredAt;
    }
}
