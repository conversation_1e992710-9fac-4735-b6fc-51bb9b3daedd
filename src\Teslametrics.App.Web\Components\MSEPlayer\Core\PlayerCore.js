import SignalRFeed from "./SignalRFeed.js";
import MediaPipeline from "./MediaPipeline.js";
import { MODES } from "./modes.js";
// mitt доступен глобально через CDN

/**
 * Основной класс видеоплеера, управляющий получением потока, обработкой медиаданных и взаимодействием с DOM-элементом `<video>`.
 */
export default class PlayerCore {
  /** @type {'live'|'point'|'range'} Режим работы плеера */
  mode;

  /** @type {MittEmitter} Событийная шина: 'timeupdate', 'state', 'error' и т.п. */
  bus = mitt();

  /**
   * Создаёт экземпляр `PlayerCore`.
   * @param {HTMLVideoElement} videoEl - DOM-элемент `<video>`, в который выводится видео.
   * @param {Object} options - Настройки плеера.
   * @param {string} options.cameraId - Идентификатор камеры.
   * @param {'live'|'point'|'range'} [options.mode='live'] - Режим воспроизведения.
   * @param {Date} [options.start] - Время начала (для режимов point и range).
   * @param {Date} [options.end] - Время окончания (только для range).
   * @param {boolean} [options.autoplay=true] - Автоматически начать воспроизведение.
   */
  constructor(
    videoEl,
    { cameraId, mode = "live", start, end, autoplay = true }
  ) {
    this.video = videoEl;
    this.camera = cameraId;
    this.mode = mode;
    this.config = MODES[mode]({ start, end });

    this.feed = new SignalRFeed(
      cameraId,
      this.config.wsPath,
      this.config.autoreconnect
    );
    this.pipe = new MediaPipeline(videoEl, this.config.mime);

    // Подключаем события
    this.feed.on("chunk", ({ data, ts }) => this.pipe.push(data, ts));
    this.feed.on("error", (err) => this.bus.emit("error", err));
    this.feed.on("state", (state) => this.bus.emit("state", state));
    this.pipe.ev.on("timeupdate", (t) => this.bus.emit("timeupdate", t));
    this.pipe.ev.on("error", (err) => this.bus.emit("error", err));

    if (autoplay) this.resume();

    setInterval(() => {
      const v = this.video; // или this.video
      const b = v.buffered.length
        ? {
            start: v.buffered.start(0).toFixed(2),
            end: v.buffered.end(v.buffered.length - 1).toFixed(2),
          }
        : null;
      console.log(
        "t =",
        v.currentTime.toFixed(2),
        "buf =",
        b?.start,
        "…",
        b?.end
      );
    }, 1000); // TODO: Remove after debug
  }

  /**
   * Приостанавливает воспроизведение и получение потока.
   */
  pause() {
    this.feed.pause();
    this.video.pause();
    this.bus.emit("state", "paused");
  }

  /**
   * Возобновляет воспроизведение и получение потока.
   */
  resume() {
    if (this.feed.state === "paused") {
      this.feed.resume();
    } else {
      this.feed.start();
    }
    this.video.play();
    this.bus.emit("state", "playing");
  }

  /**
   * Переключает воспроизведение в режим live (реального времени).
   */
  live() {
    this.pipe.seekLive();
  }

  /**
   * Устанавливает абсолютную позицию воспроизведения.
   * @param {Date} time - Абсолютное время, на которое нужно переместиться.
   */
  seek(time) {
    this.pipe.seekAbs(time);
  }

  /**
   * Освобождает ресурсы и очищает обработчики событий.
   */
  dispose() {
    this.feed.dispose();
    this.pipe.dispose();
    this.bus.all.clear();
  }
}
