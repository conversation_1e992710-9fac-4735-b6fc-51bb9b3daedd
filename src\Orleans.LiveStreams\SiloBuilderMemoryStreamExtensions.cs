using Orleans.Providers;

namespace Orleans.Hosting
{
    /// <summary>
    /// <see cref="ISiloBuilder"/> extension methods for configuring in-memory streams.
    /// </summary>
    public static class SiloBuilderLiveStreamExtensions
    {

        /// <summary>
        /// Configure silo to use memory streams, using the default message serializer
        /// (<see cref="DefaultLiveMessageBodySerializer"/>).
        /// </summary>
        /// using the default built-in serializer
        /// <param name="builder">The builder.</param>
        /// <param name="name">The stream provider name.</param>
        /// <param name="configure">The configuration delegate.</param>
        /// <returns>The silo builder.</returns>
        public static ISiloBuilder AddLiveStreams(this ISiloBuilder builder, string name,
                Action<ISiloLiveStreamConfigurator>? configure = null)
        {
            return AddLiveStreams<DefaultLiveMessageBodySerializer>(builder, name, configure);
        }

        /// <summary>
        /// Configure silo to use memory streams.
        /// </summary>
        /// <typeparam name="TSerializer">The message serializer type, which must implement <see cref="ILiveMessageBodySerializer"/>.</typeparam>
        /// <param name="builder">The builder.</param>
        /// <param name="name">The stream provider name.</param>
        /// <param name="configure">The configuration delegate.</param>
        /// <returns>The silo builder.</returns>
        public static ISiloBuilder AddLiveStreams<TSerializer>(this ISiloBuilder builder, string name,
            Action<ISiloLiveStreamConfigurator>? configure = null)
             where TSerializer : class, ILiveMessageBodySerializer
        {
            //the constructor wire up DI with all default components of the streams , so need to be called regardless of configureStream null or not
            var liveStreamConfigurator = new SiloLiveStreamConfigurator<TSerializer>(name,
                configureDelegate => builder.ConfigureServices(configureDelegate)
            );
            configure?.Invoke(liveStreamConfigurator);
            return builder;
        }
    }
}
