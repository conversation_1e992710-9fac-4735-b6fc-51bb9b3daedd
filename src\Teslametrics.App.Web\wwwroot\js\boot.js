(() => {
  const showClassName = "components-reconnect-show";
  const hideClassName = "components-reconnect-hide";
  const failedClassName = "components-reconnect-failed";
  const rejectedClassName = "components-reconnect-rejected";
  const maxRetriesId = "components-reconnect-max-retries";
  const currentAttemptId = "components-reconnect-current-attempt";

  const maximumRetryCount = 8;
  const baseIntervalMilliseconds = 5000;
  const maxRetryIntervalMilliseconds = 15000;

  let reconnectModal = null;

  const startReconnectionProcess = () => {
    reconnectModal = document.getElementById("components-reconnect-modal");

    window.dispatchEvent(new CustomEvent("blazor-connection-down")); // Соединение упало
    reconnectModal.classList.add(showClassName);

    let isCanceled = false;

    (async () => {
      const currentAttemptEl = document.getElementById(currentAttemptId);
      const maxRetriesEl = document.getElementById(maxRetriesId);

      if (currentAttemptEl && maxRetriesEl) {
        currentAttemptEl.innerText = "0";
        maxRetriesEl.innerText = maximumRetryCount.toString();
      }

      for (let i = 0; i < maximumRetryCount; i++) {
        currentAttemptEl.innerText = `${i + 1}`;

        const retryIntervalMilliseconds = Math.round(
          baseIntervalMilliseconds +
            (maxRetryIntervalMilliseconds - baseIntervalMilliseconds) *
              (i / (maximumRetryCount - 1))
        ); // Нарастающая задержка повторения

        await new Promise((resolve) =>
          setTimeout(resolve, retryIntervalMilliseconds)
        );

        if (isCanceled) {
          return;
        }

        try {
          const result = await Blazor.reconnect();
          if (!result) {
            reconnectModal.classList.add(rejectedClassName);
            // The server was reached, but the connection was rejected; reload the page.
            window.dispatchEvent(new CustomEvent("blazor-connection-reload")); // Соединение восстановить не удалось
            location.reload();
            return;
          }

          // Successfully reconnected to the server.
          window.dispatchEvent(new CustomEvent("blazor-connection-up")); // Соединение восстановлено
          return;
        } catch {
          // Didn't reach the server; try again.
        }
      }

      // Retried too many times; reload the page.
      location.reload();
    })();

    return {
      cancel: () => {
        isCanceled = true;
        if (reconnectModal) reconnectModal.classList.remove(showClassName);
      },
    };
  };

  let currentReconnectionProcess = null;

  Blazor.start({
    circuit: {
      reconnectionHandler: {
        onConnectionDown: () =>
          (currentReconnectionProcess ??= startReconnectionProcess()),
        onConnectionUp: () => {
          currentReconnectionProcess?.cancel();
          currentReconnectionProcess = null;
        },
      },
    },
  });
})();
