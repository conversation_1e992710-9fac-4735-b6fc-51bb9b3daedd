.sensor-item {
    display: flex;
    width: 100%;
    align-items: center;
    height: 36px;
    padding: 10px 16px;
    gap: 4px;
    border-radius: 4px;
}

::deep.sensor-item svg {
    color: var(--color-neutral-10);
}

::deep .sensor-icon {
    width: 16px;
    height: 16px;
}

.badge {
    display: block;
    border-radius: 2px;
    min-width: 4px;
    width: 4px;
    height: 4px;
    background: var(--mud-palette-success);
}

.sensor-item.error {
    background: #FFF3F4;
}

::deep.sensor-item.error svg,
::deep.sensor-item.error .text {
    color: var(--mud-palette-error);
}

::deep.sensor-item.no_data .badge {
    background: var(--color-text-placeholder);
}

::deep.sensor-item.no_data svg,
::deep.sensor-item.no_data .text {
    color: var(--color-text-placeholder);
}

.mud_theme_dark ::deep.sensor-item.error {
    color: #6f0000;
}
    