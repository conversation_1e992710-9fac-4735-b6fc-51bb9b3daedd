import PlayerCore from "./PlayerCore.js";

export function createPlayer(videoEl, opts, pluginList = []) {
  const core = new PlayerCore(videoEl, opts);

  if (pluginList == null) return { core, plugins: [] };

  if (!Array.isArray(pluginList)) {
    // Убедимся, что pluginList — массив
    throw new TypeError("pluginList must be an array");
  }

  const plugins = pluginList.map((Plug) => new Plug(core));

  if (plugins.length === 0) return { core, plugins: [] };

  core.bus.on("*", (ev, d) => {
    plugins.forEach((p) => {
      if (typeof p.on === "function") {
        p.on(ev, d);
      }
    });
  });

  return { core, plugins };
}
