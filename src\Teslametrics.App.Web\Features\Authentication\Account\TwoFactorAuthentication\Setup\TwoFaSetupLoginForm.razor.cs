using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using Teslametrics.App.Web.Events.Users;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authentication;
using Teslametrics.App.Web.Services.Cookies;
namespace Teslametrics.App.Web.Features.Authentication.Account.TwoFactorAuthentication.Setup;

public partial class TwoFaSetupLoginForm
{
    private User2faSetupUseCase.Response? _response;
    private MudTextField<string>? _codeFieldRef;
    private string _code = string.Empty;

    [Inject]
    protected CookieStorageAccessor _cookieStorageAccessor { get; set; } = null!;

    [Inject]
    protected IHostEnvironmentAuthenticationStateProvider _authStateProvider { get; set; } = null!;

    [Inject]
    protected NavigationManager NavigationManager { get; set; } = null!;

    [SupplyParameterFromQuery(Name = "returnUrl")]
    public string? ReturnUrl { get; set; }

    [Parameter]
    [EditorRequired]
    public string UserName { get; set; } = null!;

    [Parameter]
    [EditorRequired]
    public string Password { get; set; } = null!;

    protected override async Task OnInitializedAsync()
    {
        await FetchQrCodeAsync();
        await base.OnInitializedAsync();
    }

    private async Task SubmitAsync()
    {
        if (string.IsNullOrWhiteSpace(UserName) || _code.Length < 6 || string.IsNullOrEmpty(Password)) return;
        UserLoginWith2faUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new UserLoginWith2faUseCase.Command(UserName, _code));
        }
        catch (Exception ex)
        {
            response = null;
            Snackbar.Add("Произошла ошибка при смене пароля", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        if (response is null) return;
        switch (response.Result)
        {
            case UserLoginWith2faUseCase.Result.Success:
                await _cookieStorageAccessor.SetValueAsync(AuthenticationStorageNames.SessionId, response.SessionId!); ;
                _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(response.ClaimsPrincipal!)));
                NavigationManager.NavigateTo(ReturnUrl ?? "/");
                break;
            case UserLoginWith2faUseCase.Result.FailedToCreateSession:
                Snackbar.Add("Не удалось получить сессию. Попробуйте снова. При повторении ошибки входа - свяжитесь с администратором.", MudBlazor.Severity.Error);
                break;

            case UserLoginWith2faUseCase.Result.UserLockedout:
                EventSystem.Publish(new UserBannedEto());
                _code = string.Empty;
                break;

            case UserLoginWith2faUseCase.Result.WrongCode:
                Snackbar.Add("Неверный код", MudBlazor.Severity.Error);
                await _codeFieldRef.SetErrorAsync("Неверный код");
                break;

            case UserLoginWith2faUseCase.Result.ForceChangePassword:
                EventSystem.Publish(new LoginForceChangePasswordEto(UserName, Password));
                _code = string.Empty;
                break;

            case UserLoginWith2faUseCase.Result.ValidationError:
                await _codeFieldRef.SetErrorAsync("Проверьте правильность ввода данных");
                break;

            case UserLoginWith2faUseCase.Result.UserNotFound:
                Snackbar.Add("Пользователь не найден", MudBlazor.Severity.Error);
                _code = string.Empty;
                break;

            case UserLoginWith2faUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(LoginPage), nameof(UserLoginWith2faUseCase));
                Snackbar.Add($"Не удалось выполнить вход из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(LoginPage), nameof(UserLoginWith2faUseCase), response.Result);
                Snackbar.Add($"Не удалось выполнить вход из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task FetchQrCodeAsync()
    {
        if (string.IsNullOrWhiteSpace(UserName) || string.IsNullOrEmpty(Password)) return;
        _response = null;
        try
        {
            _response = await ScopeFactory.MediatorSend(new User2faSetupUseCase.Command(UserName));
        }
        catch (Exception ex)
        {
            _response = null;
            Snackbar.Add("Произошла ошибка при смене пароля", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        if (_response is null) return;
        switch (_response.Result)
        {
            case User2faSetupUseCase.Result.Success:
                _code = string.Empty;
                break;

            case User2faSetupUseCase.Result.UserLockedout:
                EventSystem.Publish(new ShowLoginFormEto());
                EventSystem.Publish(new UserBannedEto());
                break;

            case User2faSetupUseCase.Result.ForceChangePassword:
                EventSystem.Publish(new LoginForceChangePasswordEto(UserName, Password));
                break;

            case User2faSetupUseCase.Result.ValidationError:
                EventSystem.Publish(new ShowLoginFormEto());
                await _codeFieldRef.SetErrorAsync("Проверьте правильность ввода данных");
                break;

            case User2faSetupUseCase.Result.UserNotFound:
                EventSystem.Publish(new ShowLoginFormEto());
                Snackbar.Add("Пользователь не найден", MudBlazor.Severity.Error);
                break;

            case User2faSetupUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(LoginPage), nameof(User2faSetupUseCase));
                Snackbar.Add($"Не удалось выполнить вход из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;

            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(LoginPage), nameof(User2faSetupUseCase), _response.Result);
                Snackbar.Add($"Не удалось выполнить вход из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
}

