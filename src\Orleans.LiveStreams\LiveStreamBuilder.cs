using Microsoft.Extensions.DependencyInjection;
using Orleans.Configuration;
using Orleans.Providers;

namespace Orleans.Hosting
{
    /// <summary>
    /// Configuration builder for memory streams.
    /// </summary>
    public interface ILiveStreamConfigurator : INamedServiceConfigurator { }

    /// <summary>
    /// Configuration extensions for memory streams.
    /// </summary>
    public static class LiveStreamConfiguratorExtensions
    {
        /// <summary>
        /// Configures partitioning for memory streams.
        /// </summary>
        /// <param name="configurator">The configuration builder.</param>
        /// <param name="numOfQueues">The number of queues.</param>
        public static void ConfigurePartitioning(this ILiveStreamConfigurator configurator, int numOfQueues = HashRingStreamQueueMapperOptions.DEFAULT_NUM_QUEUES)
        {
            configurator.Configure<HashRingStreamQueueMapperOptions>(ob => ob.Configure(options => options.TotalQueueCount = numOfQueues));
        }
    }

    /// <summary>
    /// Silo-specific configuration builder for memory streams.
    /// </summary>
    public interface ISiloLiveStreamConfigurator : ILiveStreamConfigurator, ISiloRecoverableStreamConfigurator
    {
        public ISiloLiveStreamConfigurator SetMaxQueueLength(int maxQueueLength);
        public ISiloLiveStreamConfigurator SetTimeInCache(TimeSpan dataMinTimeInCache, TimeSpan dataMaxAgeInCache);
    }

    /// <summary>
    /// Configures memory streams.
    /// </summary>
    /// <typeparam name="TSerializer">The message body serializer type, which must implement <see cref="ILiveMessageBodySerializer"/>.</typeparam>
    public class SiloLiveStreamConfigurator<TSerializer> : SiloRecoverableStreamConfigurator, ISiloLiveStreamConfigurator
          where TSerializer : class, ILiveMessageBodySerializer
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SiloLiveStreamConfigurator{TSerializer}"/> class.
        /// </summary>
        /// <param name="name">The stream provider name.</param>
        /// <param name="configureServicesDelegate">The services configuration delegate.</param>
        public SiloLiveStreamConfigurator(
            string name, Action<Action<IServiceCollection>> configureServicesDelegate)
            : base(name, configureServicesDelegate, LiveAdapterFactory<TSerializer>.Create)
        {
            this.ConfigureDelegate(services => services.ConfigureNamedOptionForLogging<HashRingStreamQueueMapperOptions>(name));
        }

        public ISiloLiveStreamConfigurator SetMaxQueueLength(int maxQueueLength)
        {
            ConfigureDelegate(services => services.Configure<StreamQueueOptions>(Name, options => options.MaxQueueLength = maxQueueLength));
            return this;
        }

        public ISiloLiveStreamConfigurator SetTimeInCache(TimeSpan dataMinTimeInCache, TimeSpan dataMaxAgeInCache)
        {
            ConfigureDelegate(services => services.Configure<StreamCacheEvictionOptions>(Name, options =>
            {
                options.DataMinTimeInCache = dataMinTimeInCache;
                options.DataMaxAgeInCache = dataMaxAgeInCache;
            }));
            return this;
        }
    }

    /// <summary>
    /// Client-specific configuration builder for memory streams.
    /// </summary>
    public interface IClusterClientLiveStreamConfigurator : ILiveStreamConfigurator, IClusterClientPersistentStreamConfigurator { }

    /// <summary>
    /// Configures memory streams.
    /// </summary>
    /// <typeparam name="TSerializer">The message body serializer type, which must implement <see cref="ILiveMessageBodySerializer"/>.</typeparam>
    public class ClusterClientLiveStreamConfigurator<TSerializer> : ClusterClientPersistentStreamConfigurator, IClusterClientLiveStreamConfigurator
          where TSerializer : class, ILiveMessageBodySerializer
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ClusterClientLiveStreamConfigurator{TSerializer}"/> class.
        /// </summary>
        /// <param name="name">The stream provider name.</param>
        /// <param name="builder">The builder.</param>
        public ClusterClientLiveStreamConfigurator(string name, IClientBuilder builder)
         : base(name, builder, LiveAdapterFactory<TSerializer>.Create)
        {
        }
    }
}
