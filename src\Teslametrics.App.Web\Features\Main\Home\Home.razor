@using Teslametrics.App.Web.Features.Main.Home.Map
@using Teslametrics.App.Web.Features.Main.Home.Incidents
@using Teslametrics.App.Web.Features.Main.Home.AddressList
@using Teslametrics.App.Web.Features.Main.Home.IncidentsCount
@using Teslametrics.App.Web.Features.Main.Home.IncidentTypes

@page "/"

@attribute [Authorize]
@using Microsoft.AspNetCore.Authorization
@using MudBlazor

<PageTitle>Multimonitor : Главная</PageTitle>

<div class="mud-height-full mr-n3 overflow-auto layout gap-8 pl-2 pr-5 pb-4">
    <div class="incident_boards">
        <IncidentBoardsComponent Buildings="@_selectedBuildings" />
    </div>
    <MudPaper Elevation="0"
              Outlined="false"
              Class="pa-4 mud-width-full address br_16">
        <IncidentsCountComponent />
        @* <AddressListComponent @bind-Selected="@_selectedBuildings" /> *@
    </MudPaper>
    <MudPaper Elevation="0"
              Outlined="false"
              Class="pa-4 mud-width-full map br_16">
        @* <MapComponent Buildings="_selectedBuildings" /> *@
        <IncidentTypesComponent />
    </MudPaper>

</div>

@code {
    private IEnumerable<Guid> _selectedBuildings = [];
}