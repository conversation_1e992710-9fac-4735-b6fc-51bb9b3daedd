using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Components.MSEPlayer;

namespace Teslametrics.App.Web.Features.DetailView.ViewFrame.Camera;

public partial class MsePlayerTimelineDemo : IDisposable
{
    private string CameraId { get; set; } = string.Empty;
    private int StreamType { get; set; } = 2; // View по умолчанию
    private string AppliedCameraId { get; set; } = string.Empty;
    private MsePlayer? msePlayer;
    private VideoTimeline? videoTimeline;
    private string? CurrentSegmentTime { get; set; }
    private string? CurrentAbsoluteTime { get; set; }
    private TimelineDiagnostics? Diagnostics { get; set; }
    private Timer? diagnosticsTimer;

    private void ApplySettings()
    {
        if (Guid.TryParse(CameraId, out _))
        {
            AppliedCameraId = CameraId;
            StartDiagnosticsTimer();
            StateHasChanged();
        }
        else
        {
            Snackbar.Add("Введите корректный GUID камеры", MudBlazor.Severity.Warning);
        }
    }

    private async Task RefreshTimeInfo()
    {
        if (msePlayer == null)
        {
            Snackbar.Add("Плеер не инициализирован", MudBlazor.Severity.Warning);
            return;
        }

        try
        {
            CurrentSegmentTime = await msePlayer.GetCurrentSegmentTimestampAsync();
            CurrentAbsoluteTime = await msePlayer.GetCurrentAbsoluteTimeAsync();

            if (videoTimeline != null)
            {
                Diagnostics = await videoTimeline.GetDiagnosticsAsync();
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при получении информации о времени: {Message}", ex.Message);
            Snackbar.Add("Ошибка при получении информации о времени", MudBlazor.Severity.Error);
        }
    }

    private void StartDiagnosticsTimer()
    {
        diagnosticsTimer?.Dispose();
        diagnosticsTimer = new Timer(async _ => await UpdateDiagnostics(), null, TimeSpan.Zero, TimeSpan.FromSeconds(5));
    }

    private async Task UpdateDiagnostics()
    {
        if (videoTimeline != null)
        {
            try
            {
                Diagnostics = await videoTimeline.GetDiagnosticsAsync();
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Ошибка при обновлении диагностики: {Message}", ex.Message);
            }
        }
    }

    public void Dispose()
    {
        diagnosticsTimer?.Dispose();
    }
}
