namespace Teslametrics.Core.Abstractions;

public interface IRepository<TEntity>
    where TEntity : class, IEntity
{
    public Task<TEntity?> FindAsync(Guid id,
                                     CancellationToken cancellationToken = default);

    public Task AddAsync(TEntity entity,
                         CancellationToken cancellationToken = default);

    public Task DeleteAsync(Guid id,
                            CancellationToken cancellationToken = default);

    public Task SaveChangesAsync(CancellationToken cancellationToken = default);
}