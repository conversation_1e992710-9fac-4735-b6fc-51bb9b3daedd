using System.Runtime.InteropServices;
using FFmpeg.AutoGen;

namespace FFMpegNET;

/// <summary>
/// Буфер для чтения данных из памяти через FFmpeg AVIO
/// </summary>
internal unsafe class MemoryInputBuffer : IDisposable
{
    private const int BUFFER_SIZE = 8192;

    private readonly byte[] _data;
    private int _position;
    private GCHandle _gcHandle;
    private readonly avio_alloc_context_read_packet _readPacket;
    private readonly avio_alloc_context_seek _seek;
    private AVIOContext* _avioContext;

    public AVIOContext* AvioContext => _avioContext;

    public MemoryInputBuffer(byte[] data)
    {
        _data = data ?? throw new ArgumentNullException(nameof(data));
        _position = 0;

        _readPacket = ReadPacket;
        _seek = Seek;
        _gcHandle = GCHandle.Alloc(this, GCHandleType.Normal);

        var buffer = (byte*)ffmpeg.av_malloc(BUFFER_SIZE);
        _avioContext = ffmpeg.avio_alloc_context(
            buffer,
            BUFFER_SIZE,
            0, // read mode
            (void*)GCHandle.ToIntPtr(_gcHandle),
            _readPacket,
            null,
            _seek
        );

        if (_avioContext == null)
        {
            ffmpeg.av_free(buffer);
            _gcHandle.Free();
            throw new ApplicationException("Failed to create input I/O context");
        }
    }

    private int ReadPacket(void* opaque, byte* buf, int bufSize)
    {
        if (buf == null || bufSize <= 0)
            return 0;

        try
        {
            var handle = GCHandle.FromIntPtr((IntPtr)opaque);
            var buffer = (MemoryInputBuffer)handle.Target!;

            var remainingBytes = buffer._data.Length - buffer._position;
            var bytesToRead = Math.Min(bufSize, remainingBytes);

            if (bytesToRead <= 0)
                return ffmpeg.AVERROR_EOF;

            fixed (byte* src = &buffer._data[buffer._position])
            {
                Buffer.MemoryCopy(src, buf, bytesToRead, bytesToRead);
            }

            buffer._position += bytesToRead;
            return bytesToRead;
        }
        catch
        {
            return ffmpeg.AVERROR(5); // EIO
        }
    }

    private long Seek(void* opaque, long offset, int whence)
    {
        try
        {
            var handle = GCHandle.FromIntPtr((IntPtr)opaque);
            var buffer = (MemoryInputBuffer)handle.Target!;

            switch (whence)
            {
                case 0: // SEEK_SET
                    buffer._position = (int)Math.Max(0, Math.Min(offset, buffer._data.Length));
                    break;
                case 1: // SEEK_CUR
                    buffer._position = (int)Math.Max(0, Math.Min(buffer._position + offset, buffer._data.Length));
                    break;
                case 2: // SEEK_END
                    buffer._position = (int)Math.Max(0, Math.Min(buffer._data.Length + offset, buffer._data.Length));
                    break;
                case ffmpeg.AVSEEK_SIZE:
                    return buffer._data.Length;
                default:
                    return ffmpeg.AVERROR(22); // EINVAL
            }

            return buffer._position;
        }
        catch
        {
            return ffmpeg.AVERROR(5); // EIO
        }
    }

    public void Dispose()
    {
        if (_gcHandle.IsAllocated)
            _gcHandle.Free();

        if (_avioContext != null)
        {
            var buffer = _avioContext->buffer;

            fixed (AVIOContext** pContext = &_avioContext)
            {
                ffmpeg.avio_context_free(pContext);
            }

            if (buffer != null)
            {
                ffmpeg.av_free(buffer);
            }
        }
    }
}
