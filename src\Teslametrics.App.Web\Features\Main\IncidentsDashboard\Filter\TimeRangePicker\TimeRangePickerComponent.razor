@using Teslametrics.App.Web.Components
@inherits InteractiveBaseComponent

<div class="time-range-container pr-4">
    <!-- Иконка часов -->
    <MudIcon Icon="@Icons.Material.Filled.AccessTime"
             Size="Size.Small"
             Class="time_icon py-3 px-4" />

    <!-- Блок "От" -->
    <div class="time-section">
        <span class="time-label pl-4 pr-3">От</span>
        <div class="time-display">
            <MudNumericField T="int"
                             Value="@_fromHours"
                             ValueChanged="OnFromHoursChanged"
                             Variant="Variant.Text"
                             Margin="Margin.None"
                             Class="time-input px-2"
                             Immediate="true"
                             Adornment="Adornment.Start"
                             AdornmentText="ч"
                             Max="23"
                             Min="1" />
            <MudNumericField T="int"
                             Value="@_fromMinutes"
                             ValueChanged="OnFromMinutesChanged"
                             Variant="Variant.Text"
                             Margin="Margin.None"
                             Class="time-input px-2"
                             Immediate="true"
                             Adornment="Adornment.Start"
                             AdornmentText="м"
                             Max="59"
                             Min="0" />
        </div>
    </div>

    <!-- Блок "До" -->
    <div class="time-section">
        <span class="time-label pl-4 pr-3">До</span>
        <div class="time-display">
            <MudNumericField T="int"
                             Value="@_toHours"
                             ValueChanged="OnToHoursChanged"
                             Variant="Variant.Text"
                             Margin="Margin.None"
                             Class="time-input px-2"
                             Immediate="true"
                             Adornment="Adornment.Start"
                             AdornmentText="ч"
                             Max="23"
                             Min="1" />
            <MudNumericField T="int"
                             Value="@_toMinutes"
                             ValueChanged="OnToMinutesChanged"
                             Variant="Variant.Text"
                             Margin="Margin.None"
                             Class="time-input px-2"
                             Immediate="true"
                             Adornment="Adornment.Start"
                             AdornmentText="м"
                             Max="59"
                             Min="0" />
        </div>
    </div>
</div>
