# UserDeviceService

Сервис для определения типа устройства пользователя и операционной системы на основе User-Agent строки браузера.

## Возможности

- Определение типа устройства: Desktop (ПК), Mobile (телефон), Tablet (планшет)
- Определение операционной системы: Windows, Android, iOS, macOS, Linux
- Автоматическая инициализация для Blazor Server через CircuitHandler
- Автоматическая инициализация для контроллеров через middleware (fallback)
- Доступ к информации об устройстве из любого компонента Blazor или контроллера
- Поддержка множественных Blazor circuit с изолированной информацией об устройствах

## Использование

### В Blazor компонентах

```csharp
@inject IUserDeviceService UserDeviceService

@code {
    protected override void OnInitialized()
    {
        var deviceType = UserDeviceService.DeviceType;
        var operatingSystem = UserDeviceService.OperatingSystem;
        var isMobile = UserDeviceService.IsMobile;
        
        // Логика в зависимости от типа устройства
        if (deviceType == DeviceType.Mobile)
        {
            // Мобильная версия интерфейса
        }
        else if (deviceType == DeviceType.Desktop)
        {
            // Десктопная версия интерфейса
        }
    }
}
```

### В контроллерах

```csharp
[ApiController]
public class DeviceController : ControllerBase
{
    private readonly IUserDeviceService _userDeviceService;

    public DeviceController(IUserDeviceService userDeviceService)
    {
        _userDeviceService = userDeviceService;
    }

    [HttpGet("device-info")]
    public IActionResult GetDeviceInfo()
    {
        return Ok(new
        {
            DeviceType = _userDeviceService.DeviceType.ToString(),
            OperatingSystem = _userDeviceService.OperatingSystem.ToString(),
            IsMobile = _userDeviceService.IsMobile,
            UserAgent = _userDeviceService.UserAgent
        });
    }
}
```

### Ручная инициализация

```csharp
public void SomeMethod(HttpContext httpContext, IUserDeviceService userDeviceService)
{
    // Ручная инициализация из HttpContext
    userDeviceService.InitializeFromHttpContext(httpContext);
    
    // Или установка User-Agent напрямую
    userDeviceService.SetUserAgent("Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)");
}
```

## Типы устройств

- `DeviceType.Unknown` - Неизвестный тип устройства
- `DeviceType.Desktop` - Настольный компьютер или ноутбук
- `DeviceType.Mobile` - Мобильный телефон
- `DeviceType.Tablet` - Планшет

## Операционные системы

- `OperatingSystem.Unknown` - Неизвестная операционная система
- `OperatingSystem.Windows` - Microsoft Windows
- `OperatingSystem.Android` - Google Android
- `OperatingSystem.iOS` - Apple iOS
- `OperatingSystem.macOS` - Apple macOS
- `OperatingSystem.Linux` - Linux

## Автоматическая инициализация

### Для Blazor Server компонентов

Сервис автоматически инициализируется через `UserDeviceCircuitHandler`, который анализирует User-Agent заголовок при установке Blazor SignalR соединения (circuit). Это обеспечивает корректную работу с Blazor Server архитектурой, где компоненты работают в scope SignalR соединения.

### Для контроллеров (fallback)

Для контроллеров и других HTTP-запросов сервис может использовать `UserDeviceMiddleware` как fallback механизм. Однако основной механизм работы - через CircuitHandler.

## Регистрация в DI

Сервис зарегистрирован в DI контейнере как Scoped через `UserDeviceModule.Install(builder.Services)` в Program.cs. Модуль автоматически регистрирует:

- `IUserDeviceService` и `UserDeviceService` как Scoped
- `UserDeviceCircuitHandler` как `CircuitHandler`
- `UserDeviceMiddleware` для обратной совместимости

## Архитектура

### Blazor Server + Scoped DI

В Blazor Server каждый circuit (SignalR соединение) имеет свой собственный scoped DI контейнер. Это означает, что каждое подключение получает свой экземпляр `UserDeviceService`, что обеспечивает естественную изоляцию данных.

Решение использует стандартный Blazor Server lifecycle:

1. **CircuitHandler** перехватывает установку Blazor SignalR соединения
2. Извлекает User-Agent из HttpContext при создании circuit
3. Инициализирует scoped экземпляр `UserDeviceService` с информацией об устройстве
4. Компоненты Blazor получают тот же scoped экземпляр с уже инициализированными данными
5. При закрытии circuit scoped сервис автоматически уничтожается

### Преимущества упрощенной архитектуры

- **Простота**: Использует стандартный DI lifecycle Blazor Server
- **Производительность**: Нет накладных расходов на ConcurrentDictionary и поиск по circuit ID
- **Надежность**: Автоматическое управление памятью через DI контейнер
- **Изоляция**: Каждый circuit имеет свой экземпляр сервиса
- **Совместимость**: Сохраняет тот же API для компонентов
- **Минимализм**: Убраны избыточные методы и свойства состояния

### Логика инициализации

**Защита от повторной инициализации:**
- Проверяется наличие уже установленного User-Agent
- При попытке повторной инициализации логируется предупреждение
- Первое значение сохраняется (принцип "первый выигрывает")

**Обработка ошибок:**
- Если User-Agent пустой, устанавливаются значения по умолчанию (Unknown)
- Все ошибки логируются для диагностики
- Сервис остается в рабочем состоянии даже при ошибках инициализации

## Тестирование

Для тестирования функциональности созданы unit тесты в `UserDeviceServiceTests.cs`, которые проверяют корректность определения различных типов устройств и операционных систем.

## Примеры User-Agent строк

### Desktop
- Windows: `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36`
- macOS: `Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36`
- Linux: `Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36`

### Mobile
- iPhone: `Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15`
- Android Phone: `Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36`

### Tablet
- iPad: `Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15`
- Android Tablet: `Mozilla/5.0 (Linux; Android 11; SM-T870) AppleWebKit/537.36`
