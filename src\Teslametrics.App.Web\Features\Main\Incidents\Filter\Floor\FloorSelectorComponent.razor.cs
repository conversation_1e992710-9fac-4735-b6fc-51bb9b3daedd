using System;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Incidents.Filter.Floor;

public partial class FloorSelectorComponent
{
    private GetFloorListUseCase.Response.Item? _selected;

    [Parameter]
    public Guid? CityId { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }

    [Parameter]
    public Guid? Floor { get; set; }
    [Parameter]
    public EventCallback<Guid?> FloorChanged { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        if (_selected?.Id != Floor)
        {
            _selected = null;
            await GetDataAsync();
        }
        await base.OnParametersSetAsync();
    }

    private async Task GetDataAsync()
    {
        if (!CityId.HasValue || !BuildingId.HasValue || !Floor.HasValue) return;

        GetFloorUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetFloorUseCase.Query(CityId.Value, BuildingId.Value, Floor.Value));
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching cities");
            Snackbar.Add("Не удалось получить этаж из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case GetFloorUseCase.Result.Success:
                _selected = new(CityId.Value, response.Number);
                break;
            case GetFloorUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении этажа", MudBlazor.Severity.Error);
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FloorSelectorComponent), nameof(GetFloorUseCase));
                break;
            case GetFloorUseCase.Result.CityNotFound:
                Snackbar.Add("Город не найден", MudBlazor.Severity.Error);

                Floor = null;
                if (FloorChanged.HasDelegate)
                    await FloorChanged.InvokeAsync(BuildingId);

                break;

            case GetFloorUseCase.Result.BuildingNotFound:
                Snackbar.Add("Здание не найдено", MudBlazor.Severity.Error);

                Floor = null;
                if (FloorChanged.HasDelegate)
                    await FloorChanged.InvokeAsync(BuildingId);
                break;

            case GetFloorUseCase.Result.FloorNotFound:
                Snackbar.Add("Этаж не найден", MudBlazor.Severity.Error);

                Floor = null;
                if (FloorChanged.HasDelegate)
                    await FloorChanged.InvokeAsync(BuildingId);
                break;

            case GetFloorUseCase.Result.Unknown:
                Snackbar.Add("Не удалось получить этаж из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FloorSelectorComponent), nameof(GetFloorUseCase));
                break;

            default:
                Snackbar.Add("Неизвестная ошибка при получении этажа", MudBlazor.Severity.Error);
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(FloorSelectorComponent), nameof(GetFloorUseCase), response.Result);
                break;
        }
    }

    private async Task ValueChanged(GetFloorListUseCase.Response.Item? item)
    {
        _selected = item;
        await FloorChanged.InvokeAsync(item?.Id);
    }

    private async Task<IEnumerable<GetFloorListUseCase.Response.Item>> SearchAsync(string value, CancellationToken token)
    {
        if (!CityId.HasValue || !BuildingId.HasValue) return [];

        GetFloorListUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetFloorListUseCase.Query(CityId.Value, BuildingId.Value, value is null ? null : int.Parse(value)), token);
        }
        catch (TaskCanceledException)
        {
            // Search was canceled, just return empty result
            return Enumerable.Empty<GetFloorListUseCase.Response.Item>();
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching cities");
            Snackbar.Add("Не удалось получить список этажей из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
            return Enumerable.Empty<GetFloorListUseCase.Response.Item>();
        }

        if (response.Result == GetFloorListUseCase.Result.Success)
            return response.Items;

        if (response.Result == GetFloorListUseCase.Result.ValidationError)
            Snackbar.Add("Ошибка валидации при получении списка городов", MudBlazor.Severity.Error);
        else
            Snackbar.Add("Не удалось получить список городов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);

        return Enumerable.Empty<GetFloorListUseCase.Response.Item>();
    }
}
