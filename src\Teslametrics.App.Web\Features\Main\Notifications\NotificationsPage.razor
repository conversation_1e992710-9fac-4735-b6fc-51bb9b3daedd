﻿@using System.Data
@attribute [Authorize]
@attribute [Route("/notifications")]
@inherits InteractiveBaseComponent

<PageTitle>Multimonitor | Уведомления</PageTitle>
<MudStack Spacing="5"
          Class="mud-height-full px-2 pb-4 overflow-auto layout"
          Row="true">
    <div class="d_contents">
        <MudPaper Elevation="0"
                  Outlined="true"
                  Class="mud-height-full mud-width-full container">
            @if (_response is not null && _response.IsSuccess && _response.Incidents.Count != 0)
            {
                <!-- header ------------------------------------------------------------->
                <MudStack Row="true"
                          Class="notif-header px-6 py-5"
                          AlignItems="AlignItems.Center"
                          Justify="Justify.SpaceBetween">
                    <MudText Typo="Typo.subtitle2"
                             Class="pa-0"
                             @onclick="MarkAllReadAsync">
                        Отметить все, как прочитанные
                    </MudText>
                </MudStack>
            }
            else
            {
                <div></div>
            }
            <!-- list --------------------------------------------------------------->
            <MudStack Class="list overflow-auto"
                      Spacing="0">
                @if (_response is not null && _response.IsSuccess && _response.Incidents.Count != 0)
                {
                    @foreach (var row in _response.Incidents)
                    {
                        <MudStack Row="true"
                                  Class="notif-item py-3 px-6"
                                  Justify="Justify.SpaceBetween"
                                  AlignItems="AlignItems.Center"
                                  @key="row">
                            <!-- icon -->
                            <MudIcon Icon="@TeslaIcons.Notifications.Bell"
                                     Class="icon"
                                     Size="Size.Medium" />

                            <!-- text block -->
                            <div class="flex-grow-1 lh-1">
                                @switch (row.IncidentType)
                                {
                                    case Teslametrics.Shared.IncidentType.Door:
                                        <MudText Typo="Typo.subtitle1"
                                                 Class="fw-500">Превышено допустимое время с открытой дверью</MudText>
                                        break;
                                    case Teslametrics.Shared.IncidentType.Temperature:
                                        <MudText Typo="Typo.subtitle1"
                                                 Class="fw-500">Температура вышла за пределы допустимого диапазона</MudText>
                                        break;
                                    case Teslametrics.Shared.IncidentType.Humidity:
                                        <MudText Typo="Typo.subtitle1"
                                                 Class="fw-500">Влажность вышла за пределы допустимого диапазона</MudText>
                                        break;
                                    case Teslametrics.Shared.IncidentType.Leak:
                                        <MudText Typo="Typo.subtitle1"
                                                 Class="fw-500">Зафиксирована протечка</MudText>
                                        break;
                                    case Teslametrics.Shared.IncidentType.Power:
                                        <MudText Typo="Typo.subtitle1"
                                                 Class="fw-500">Пропало питание</MudText>
                                        break;
                                }

                                <MudText Typo="Typo.subtitle2">
                                    @row.Device @row.Room @row.Floor @row.Building @row.City
                                </MudText>
                            </div>

                            <MudStack Justify="Justify.FlexEnd"
                                      Spacing="3">
                                <MudText Typo="Typo.caption"
                                         Class="pr-4 dotted color_neutral_40">
                                    @row.Date.ToString("dd.MM.yyyy HH:mm")
                                </MudText>

                                <MudButton Variant="Variant.Outlined"
                                           Size="Size.Small"
                                           Class="py-3 px-4 show_details"
                                           OnClick="@(() => ShowDetails(row))">
                                    Подробнее
                                </MudButton>
                            </MudStack>
                        </MudStack>
                    }
                }
                else
                {
                    <MudStack AlignItems="AlignItems.Center"
                              Justify="Justify.Center"
                              Class="mud-height-full px-4 pb-4">
                        <div class="no_items_icon">
                            <MudIcon Icon="@TeslaIcons.Notifications.Bell"
                                     Size="Size.Small" />
                        </div>
                        <MudText Typo="Typo.subtitle2"
                                 Class="no_items_subtitle">Уведомлений нет</MudText>
                    </MudStack>
                }
            </MudStack>
        </MudPaper>
    </div>
</MudStack>