@inherits BaseComponent
@implements IVideoTimeline

<div class="video-timeline">
    <div class="timeline-header">
        <span class="timeline-title">Временная шкала</span>
        @if (WindowStart.HasValue && WindowEnd.HasValue)
        {
            <span class="timeline-range">
                @WindowStart.Value.ToString("HH:mm:ss") - @WindowEnd.Value.ToString("HH:mm:ss")
            </span>
        }
    </div>

    <div class="timeline-container">
        <div class="timeline-track">
            <!-- Временные метки -->
            <div class="timeline-marks">
                @if (WindowStart.HasValue && WindowEnd.HasValue)
                {
                    @for (int i = 0; i <= 12; i++) // 12 меток для 3 часов (каждые 15 минут)
                    {
                        var markTime = WindowStart.Value.AddMinutes(i * 15);
                        var markPosition = (double)i / 12 * 100;

                        <div class="timeline-mark"
                             style="left: @(markPosition.ToString("F1", System.Globalization.CultureInfo.InvariantCulture))%">
                            <div class="mark-line"></div>
                            <div class="mark-label">@markTime.ToString("HH:mm")</div>
                        </div>
                    }
                }
            </div>

            <!-- Индикатор текущей позиции -->
            @if (CurrentTime.HasValue && WindowStart.HasValue && WindowEnd.HasValue)
            {
                <div class="timeline-position"
                     style="left: @(CurrentPositionPercent.ToString("F1", System.Globalization.CultureInfo.InvariantCulture))%">
                    <div class="position-indicator"></div>
                    <div class="position-tooltip">
                        @CurrentTime.Value.ToString("HH:mm:ss")
                    </div>
                </div>
            }
        </div>
    </div>

    @if (CurrentTime.HasValue)
    {
        <div class="timeline-footer">
            <span class="current-time-display">
                Текущее время: @CurrentTime.Value.ToString("dd.MM.yyyy HH:mm:ss")
            </span>
        </div>
    }
</div>
