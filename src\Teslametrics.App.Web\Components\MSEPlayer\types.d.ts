/**
 * types.d.ts ― типы для Components/MSEPlayer
 * generated: 2025-05-23
 */

import type { Emitter } from 'mitt';

/* ------------------------------------------------- *
 *                   CORE: modes                     *
 * ------------------------------------------------- */

export type ViewMode = 'live' | 'point' | 'range';

export interface ModeConfig {
  /** Web-socket endpoint для SignalR */
  wsPath: string;
  /** MIME-тип для MediaSource */
  mime: string;
  /** Сколько секунд буфер держим в памяти */
  keep: number;
}

export declare const MODES: {
  live(): ModeConfig;
  point(opts: { start: Date | string }): ModeConfig;
  range(opts: { start: Date | string; end: Date | string }): ModeConfig;
};

/* ------------------------------------------------- *
 *               CORE: SignalRFeed                   *
 * ------------------------------------------------- */

export type FeedState = 'idle' | 'connecting' | 'ready' | 'paused' | 'stopped';

export interface SignalRFeedOptions {
  /** задержка перед повторным .start(), мс */
  backOffMs?: number;
  /** AbortController для внешнего «вырубить всё» */
  abortSignal?: AbortSignal;
}

export declare class SignalRFeed {
  constructor(
    cameraId: string,
    wsPath?: string,
    autoReconnect?: boolean,
    opts?: SignalRFeedOptions,
  );

  readonly state: FeedState;
  /** mitt-шина: события `chunk`, `state`, `error` */
  readonly ev: Emitter<Record<string, unknown>>;

  start(): Promise<void>;
  pause(): void;
  resume(): void;
  dispose(): Promise<void>;

  on(type: string, handler: (...args: any[]) => void): void;
  off(type: string, handler: (...args: any[]) => void): void;
}

/* ------------------------------------------------- *
 *              CORE: MediaPipeline                  *
 * ------------------------------------------------- */

export interface MediaPipelineOptions {
  mime: string;
  keep: number;
}

export declare class MediaPipeline {
  constructor(
    video: HTMLVideoElement,
    mime: string,
    opts?: Partial<MediaPipelineOptions>,
  );

  push(chunk: Uint8Array, absTime?: Date): void;
  /** абсолютный seek по времени записи */
  seekAbs(time: Date): void;
  /** прыжок в live-край */
  seekLive(): void;
  dispose(): void;

  /** текущее абсолютное время в потоке или null */
  readonly currentAbsTime: Date | null;

  /** пользовательский callback на каждое обновление времени */
  onTime?: (t: Date) => void;
}

/* ------------------------------------------------- *
 *                CORE: PlayerCore                   *
 * ------------------------------------------------- */

export interface PlayerCoreOptions {
  cameraId: string;
  mode?: ViewMode;
  start?: Date | string | null;
  end?: Date | string | null;
  autoplay?: boolean;
}

export declare class PlayerCore {
  constructor(video: HTMLVideoElement, opts: PlayerCoreOptions);

  readonly bus: Emitter<Record<string, unknown>>;
  readonly feed: SignalRFeed;
  readonly pipe: MediaPipeline;

  pause(): void;
  resume(): void;
  live(): void;
  seek(time: Date): void;
  dispose(): void;
}

/* ------------------------------------------------- *
 *              PLUG-IN  BASE  CLASS                 *
 * ------------------------------------------------- */

export declare abstract class PluginBase {
  protected constructor(core: PlayerCore);

  /** вызывается сразу после конструктора */
  init(): void | Promise<void>;
  /** все события от core.bus стекаются сюда */
  on(event: string, data: unknown): void;
  /** финальная очистка ресурсов */
  destroy(): void;
}

/* ------------------------------------------------- *
 *                PUBLIC  FACADE  API                *
 * ------------------------------------------------- */

/** возврат из createPlayer: ядро + подключённые плагины */
export interface PlayerInstance {
  core: PlayerCore;
  plugins: PluginBase[];
}

/**
 * Собрать новый плеер.
 * @param pluginList  массив классов-плагинов (`class … extends PluginBase`)
 */
export function createPlayer(
  video: HTMLVideoElement,
  opts: PlayerCoreOptions,
  pluginList?: Array<new (core: PlayerCore) => PluginBase>,
): PlayerInstance;

/* функции, которые вызывает Blazor-interop */

export function initializePlayer(
  video: HTMLVideoElement,
  cameraId: string,
  mode: ViewMode,
  start?: string | Date | null,
  end?: string | Date | null,
  plugins?: Array<any>,
): void;

export function pauseStream(id: string): void;
export function resumeStream(id: string): void;
export function seekToLive(id: string): void;
export function stopStream(id: string): void;
export function getAbsTime(id: string): Date | null;
