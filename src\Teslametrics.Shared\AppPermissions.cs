using Teslametrics.Shared.Exceptions;

namespace Teslametrics.Shared;

public abstract class AppPermissions
{
    public static IEnumerable<string> GetAll() =>
        Fqdn<AppPermissions>.GetNames();

    public static IEnumerable<string> GetAdmin() =>
        Fqdn<AppPermissions>.GetNames((t, m) =>
            Attribute.IsDefined(t, typeof(AdminAttribute)) || Attribute.IsDefined(m, typeof(AdminAttribute)));

    public static IEnumerable<string> GetUser() =>
        Fqdn<AppPermissions>.GetNames((t, m) =>
            !Attribute.IsDefined(t, typeof(AdminAttribute)) && !Attribute.IsDefined(m, typeof(AdminAttribute)));

    public static void Validate(IEnumerable<string> permissions, bool isAdmin)
    {
        var hashSet = permissions.ToHashSet();

        if (!hashSet.IsSubsetOf(GetAll()))
        {
            throw new AppException("Invalid permissions");
        }

        if (!isAdmin)
        {
            if (hashSet.Intersect(GetAdmin()).Any())
            {
                throw new AppException("Invalid permissions");
            }
        }
    }

    [FqdnRoot]
    public abstract class Main
    {
        public abstract class AccessControl
        {
            [Flags]
            [Admin]
            public enum Organizations : ulong
            {
                Invalid = 0,
                Read = 1,
                Create = Read | 2,
                Update = Read | 4,
                Delete = Read | 8
            }

            [Flags]
            [Admin]
            public enum Users : ulong
            {
                Invalid = 0,
                Read = 1,
                Create = Read | 2,
                Update = Read | 4,
                Delete = Read | 8,
                Lock = Read | 16,
                Unlock = Read | 32,
                ForceChangePassword = Read | 64
            }

            [Flags]
            [Admin]
            public enum Roles : ulong
            {
                Invalid = 0,
                Read = 1,
                Create = Read | 2,
                Update = Read | 4,
                Delete = Read | 8
            }
        }

        [Flags]
        public enum Cameras : ulong
        {
            Invalid = 0,
            Read = 1,
            [Admin]
            Create = Read | 2,
            [Admin]
            Update = Read | 4,
            [Admin]
            Delete = Read | 8,
            [Admin]
            Connect = Read | 16,
            [Admin]
            Disconnect = Read | 32,
            [Admin]
            Move = Read | 64
        }

        [Flags]
        public enum Folders : ulong
        {
            Invalid = 0,
            Read = 1,
            [Admin]
            Create = Read | 2,
            [Admin]
            Update = Read | 4,
            [Admin]
            Delete = Read | 8,
            [Admin]
            Move = Read | 16
        }

        [Flags]
        public enum Groups : ulong
        {
            Invalid = 0,
            Read = 1,
            Create = Read | 2,
            Update = Read | 4,
            Delete = Read | 8,
            Move = Read | 16
        }

        [Flags]
        [Admin]
        public enum CameraPresets : ulong
        {
            Invalid = 0,
            Read = 1,
            Create = Read | 2,
            Update = Read | 4,
            Delete = Read | 8
        }

        [Flags]
        [Admin]
        public enum CameraQuotas : ulong
        {
            Invalid = 0,
            Read = 1,
            Create = Read | 2,
            Update = Read | 4,
            Delete = Read | 8
        }

        [Flags]
        [Admin]
        public enum CameraPublicAccess : ulong
        {
            Invalid = 0,
            Read = 1,
            Create = Read | 2,
            Update = Read | 4,
            Delete = Read | 8
        }

        [Flags]
        public enum CameraViews : ulong
        {
            Invalid = 0,
            Read = 1,
            [Admin]
            Create = Read | 2,
            [Admin]
            Update = Read | 4,
            [Admin]
            Delete = Read | 8
        }
    }
}

[AttributeUsage(AttributeTargets.Enum | AttributeTargets.Field, AllowMultiple = false)]
public class AdminAttribute : Attribute;