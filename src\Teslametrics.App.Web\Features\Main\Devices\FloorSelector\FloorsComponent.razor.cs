using System.Reactive;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Devices.FloorSelector;

public partial class FloorsComponent
{
    private bool _subscribing;
    private SubscribeFloorListUseCase.Response? _subscribeResponse;
    private GetFloorListUseCase.Response? _response = null;

    public record FloorModel(Guid Id, int? Number);

    [Parameter]
    [EditorRequired]
    public Guid CityId { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid BuildingId { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid? Floor { get; set; }

    [Parameter]
    [EditorRequired]
    public EventCallback<Guid?> FloorChanged { get; set; }

    public async Task OnFloorSelectAsync(GetFloorListUseCase.Response.Item floor)
    {
        Floor = floor.Id;

        if (FloorChanged.HasDelegate)
            await FloorChanged.InvokeAsync(Floor);
    }

    protected override async Task OnParametersSetAsync()
    {
        await FetchDataAsync();
        await base.OnParametersSetAsync();
    }

    private async Task FetchDataAsync()
    {
        try
        {
            _response = await ScopeFactory.MediatorSend(new GetFloorListUseCase.Query(CityId, BuildingId));
        }
        catch (Exception ex)
        {
            _response = null;
            Logger.LogError(ex, ex.Message);
            Snackbar.Add($"Не удалось получить список этажей из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
        }

        if (_response is null) return;
        switch (_response.Result)
        {
            case GetFloorListUseCase.Result.Success:
                if (Floor is null && _response.Items.Count > 0)
                {
                    await OnFloorSelectAsync(_response.Items.First());
                }
                if (Floor is not null && _response.Items.Count > 0)
                {
                    var foundFloor = _response.Items.FirstOrDefault(floor => floor.Id == Floor);
                    if (foundFloor is null)
                        await OnFloorSelectAsync(_response.Items.First());
                }
                await SubscribeAsync();
                break;
            case GetFloorListUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении списка этажей", MudBlazor.Severity.Error);
                break;
            case GetFloorListUseCase.Result.PlanNotFound:
                Snackbar.Add("Не удалось получить список этажей из-за ошибки: " + _response.Result, MudBlazor.Severity.Error);
                break;
            case GetFloorListUseCase.Result.Unknown:
            default:
                Snackbar.Add("Не удалось получить список этажей из-за непредвиденной ошибки:" + _response.Result, MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task SubscribeAsync()
    {
        if (_response is null || !_response.IsSuccess || _subscribing) return;
        try
        {
            Unsubscribe();
            await SetSubscribingAsync(true);
            _subscribeResponse = await ScopeFactory.MediatorSend(new SubscribeFloorListUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), CityId, BuildingId));
        }
        catch (Exception ex)
        {
            _subscribeResponse = null;
            Snackbar.Add($"Не удалось подписаться на события списка этажей из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
        finally
        {
            await SetSubscribingAsync(false);
        }

        if (_subscribeResponse is null) return;

        switch (_subscribeResponse.Result)
        {
            case SubscribeFloorListUseCase.Result.Success:
                CompositeDisposable.Add(_subscribeResponse.Subscription!);
                break;
            case SubscribeFloorListUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события списка этажей", MudBlazor.Severity.Error);
                break;
            case SubscribeFloorListUseCase.Result.RoomNotFound:
                Snackbar.Add("Ошибка подписки на события списка этажей", MudBlazor.Severity.Error);
                break;
            case SubscribeFloorListUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FloorsComponent), nameof(SubscribeFloorListUseCase));
                Snackbar.Add($"Не удалось подписаться на события списка этажей из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(FloorsComponent), nameof(SubscribeFloorListUseCase), _subscribeResponse.Result);
                Snackbar.Add($"Не удалось подписаться на события списка этажей из-за ошибки: {_subscribeResponse.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
    private void Unsubscribe()
    {
        if (_subscribeResponse?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscribeResponse.Subscription);
            _subscribeResponse.Subscription.Dispose();
        }
    }

    protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [Event Handlers]
    private async void OnAppEventHandler(object appEvent)
    {
        await FetchDataAsync();
        await UpdateViewAsync();
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
        Logger.LogError(exc, exc.Message);
    }
    #endregion [Event Handlers]
}
