﻿@inherits InteractiveBaseComponent
<MudAutocomplete T="GetDeviceListUseCase.Response.Item"
                 SearchFunc="@SearchAsync"
                 ToStringFunc="@(e => e == null ? null : e.Name)"
                 Value="@_selected"
                 ValueChanged="@ValueChanged"
                 Label="Оборудование"
                 Clearable="true"
                 Margin="Margin.Dense"
                 ResetValueOnEmptyText="true"
                 Variant="Variant.Outlined"
                 Disabled="@_disabled" />
