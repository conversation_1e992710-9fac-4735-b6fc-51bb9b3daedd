::deep .mud-tooltip-root.mud-tooltip-inline {
    display: inline-flex;
}

::deep .table {
    overflow: hidden !important;
}

::deep .table .mud-table-container {
    width: 100% !important;
}

::deep .table .mud-drop-container {
    width: max-content;
}

.field-with-error-icon {
    display: flex;
    align-items: center;
    gap: 8px;
}

.field-with-error-icon .mud-input {
    flex: 1;
}

.error-icon-container {
    display: flex;
    align-items: center;
}

.error-icon-container::before {
    content: " ";
    width: 1px;
    height: 100%;
    min-height: 24px;
    margin: 0 8px;
}
.error-icon-container::before:has(>.validation-error-tooltip)
{
    background: var(--mud-palette-divider-light);
}

/* Validation error icon and tooltip styles */
.validation-error-icon {
    margin-left: 4px;
    cursor: pointer;
}

.validation-error-tooltip {
    z-index: 1600 !important;
}

.validation-error-content {
    max-width: 300px;
    padding: 8px;
}

.validation-error-message {
    margin-bottom: 4px;
    line-height: 1.4;
}

.validation-error-message:last-child {
    margin-bottom: 0;
}

::deep .mud-tabs-tabbar-wrapper {
    gap: 8px;
}

::deep .mud-tabs-tabbar-wrapper,
::deep .mud-tooltip-root {
    width: 100% !important;
}

::deep .tab_panel {
    border-radius: 8px;
}

::deep .tab_panel.active_tab {
    background: var(--color-secondary-900);
    color: var(--mud-palette-primary-text);
}
