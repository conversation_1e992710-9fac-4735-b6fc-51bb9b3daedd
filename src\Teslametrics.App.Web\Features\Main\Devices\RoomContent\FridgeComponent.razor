﻿@using Teslametrics.App.Web.Features.Main.Devices.RoomContent.Fridge
@using Teslametrics.App.Web.Features.Main.Devices.RoomContent.IncidentItems
@attribute [StreamRendering(true)]
@inherits InteractiveBaseComponent
<div class="d_contents">
	<MudExpansionPanel Class="list_item br_8"
					   Dense="false"
					   Gutters="false"
					   HeaderClass="px-4 py-2 header br_8">
		<TitleContent>
			@if (IsLoading && (_response is null || !_response.IsSuccess))
			{
				<MudSkeleton Width="60%" />
			}
			@if (_response is not null && _response.IsSuccess)
			{
				<div class="d-flex gap-2 align-center">
					<MudIcon Icon="@(_response.Fridge.HasIncidents? TeslaIcons.State.Warning : TeslaIcons.State.Success)"
							 Color="@(_response.Fridge.HasIncidents? Color.Error: Color.Default)" />
					<MudText>@_response.Fridge.Name</MudText>
				</div>
			}
		</TitleContent>
		<ChildContent>
			@if (IsLoading && (_response is null || !_response.IsSuccess))
			{
				<MudSkeleton Width="60%" />
				<MudSkeleton Width="60%" />
				<MudSkeleton Width="60%" />
				<MudSkeleton Width="60%" />
			}
			@if (_response is not null && _response.IsSuccess)
			{
				<div class="tile_grid py-3">
					@foreach (var item in _temperature ?? [])
					{
						<MudPaper Elevation="0"
								  Outlined="true"
								  Class="pa-4 tile"
								  @key="item">
							<SensorDataComponent TopicName="@item.Name"
												 Name="@item.DisplayName"
												 Error="@(item.Incident is not null && _wirenboardIncident is null)"
												 ValueProcessor="TempValueProcessor"
												 Icon="@TeslaIcons.Sensors.Temperature" />
						</MudPaper>
					}
					@foreach (var item in _humidity ?? [])
					{
						<MudPaper Elevation="0"
								  Outlined="true"
								  Class="pa-4 tile"
								  @key="item">
							<SensorDataComponent TopicName="@item.Name"
												 Name="@item.DisplayName"
												 Error="@(item.Incident is not null && _wirenboardIncident is null)"
												 ValueProcessor="HumidityValueProcessor"
												 Icon="@TeslaIcons.Sensors.Humidity" />
						</MudPaper>
					}
					@foreach (var item in _door ?? [])
					{
						<MudPaper Elevation="0"
								  Outlined="true"
								  Class="pa-4 tile"
								  @key="item">
							<SensorDataComponent TopicName="@item.Name"
												 Name="@item.DisplayName"
												 Error="@(item.Incident is not null && _wirenboardIncident is null)"
												 ValueProcessor="DoorValueProcessor"
												 Icon="@TeslaIcons.Sensors.Door" />
						</MudPaper>
					}
					@foreach (var item in _power ?? [])
					{
						<MudPaper Elevation="0"
								  Outlined="true"
								  Class="pa-4 tile"
								  @key="item">
							<SensorDataComponent TopicName="@item.Name"
												 Name="@item.DisplayName"
												 Error="@(item.Incident is not null && _wirenboardIncident is null)"
												 ValueProcessor="PowerValueProcessor"
												 Icon="@TeslaIcons.Sensors.Power" />
						</MudPaper>
					}
					@foreach (var item in _leak ?? [])
					{
						<MudPaper Elevation="0"
								  Outlined="true"
								  Class="pa-4 tile"
								  @key="item">
							<SensorDataComponent TopicName="@item.Name"
												 Name="@item.DisplayName"
												 Error="@(item.Incident is not null && _wirenboardIncident is null)"
												 ValueProcessor="LeakValueProcessor"
												 Icon="@TeslaIcons.Sensors.Leak" />
						</MudPaper>
					}
				</div>

				<div class="incidents_list d-flex gap-2 flex-column">
					@if (_wirenboardIncident is not null)
					{
						<WirenboardIncidentComponent IncidentId="@_wirenboardIncident.Id" />
					}
					@foreach (var item in _sensorsWithIncidents)
					{
						switch (item)
						{
							case GetFridgeUseCase.Response.TemperatureModel temperature:
								<TempIncidentComponent Item="temperature"
													   @key="item.Id" />
								break;
							case GetFridgeUseCase.Response.HumidityModel humidity:
								<HumidityIncidentComponent Item="humidity"
														   @key="item.Id" />
								break;
							case GetFridgeUseCase.Response.DoorModel door:
								<DoorIncidentComponent Item="door"
													   @key="item.Id" />
								break;
							case GetFridgeUseCase.Response.LeakModel leak:
								<LeakIncidentComponent Item="leak"
													   @key="item.Id" />
								break;
							case GetFridgeUseCase.Response.PowerModel power:
								<PowerIncidentComponent Item="power"
														@key="item.Id" />
								break;
						}
					}
				</div>
			}
		</ChildContent>
	</MudExpansionPanel>
</div>