using System.Timers;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;
using Teslametrics.MediaServer.Orleans.Wirenboard;

namespace Teslametrics.App.Web.Features.Main.Devices.RoomContent.Fridge;

public partial class SensorDataComponent : IAsyncDisposable, ISensorObserver
{
    [Inject]
    private IClusterClient _clusterClient { get; set; } = null!;

    private string? _topicName;
    private ISensorObserver? _observer;
    private object? _value = null;
    private string? _itemText;

    [Parameter]
    [EditorRequired]
    public string TopicName { get; set; } = null!;

    [Parameter]
    [EditorRequired]
    public string Name { get; set; } = string.Empty;

    [Parameter]
    public string? Icon { get; set; }

    [Parameter]
    public bool? Error { get; set; }

    [Parameter]
    public Func<object?, bool>? ErrorFunc { get; set; }

    [Parameter]
    public Func<object?, string> ValueProcessor { get; set; } = v => v?.ToString() ?? "-";

    protected override async Task OnInitializedAsync()
    {
        var timer = new System.Timers.Timer(240_000); // 4 минуты
        timer.Elapsed += OnTimerElapsed;
        timer.AutoReset = true;
        timer.Start();

        CompositeDisposable.Add(timer);

        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (_topicName != TopicName)
        {
            _topicName = TopicName;
            await SubscribeAsync();
        }
        await base.OnParametersSetAsync();
    }

    private void ApplyValue(object? value)
    {
        _value = value;
        _itemText = ValueProcessor(_value);
        Error ??= ErrorFunc?.Invoke(_value);   // короче то же самое
        StateHasChanged();
    }

    public Task ReceiveData(ISensorData sensorData)
    {
        // извлекаем само значение
        object? value = sensorData switch
        {
            SensorDoubleData d => d.Value,
            SensorIntData i => i.Value,
            SensorBoolData b => b.Value,
            SensorStringData s => s.Value,
            SensorNullData => null,
            _ => null          // на всякий случай
        };

        return InvokeAsync(() => ApplyValue(value));
    }

    private async Task SubscribeAsync()
    {
        try
        {
            _observer ??= _clusterClient.CreateObjectReference<ISensorObserver>(this);
        }
        catch (Exception ex)
        {
            Snackbar.Add("Не удалось создать подписчика на события датчика.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
            return;
        }

        SubscribeFridgeSensorUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new SubscribeFridgeSensorUseCase.Request(_observer, TopicName));
        }
        catch (Exception ex)
        {
            response = null;
            Snackbar.Add($"Не удалось подписаться на события датчика из-за ошибки сообщения с сервером.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case SubscribeFridgeSensorUseCase.Result.Success:
                _itemText = ValueProcessor(_value);
                break;

            case SubscribeFridgeSensorUseCase.Result.ValidationError:
                Snackbar.Add($"Не удалось подписаться на события датчика из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.",
                MudBlazor.Severity.Error);
                break;
            case SubscribeFridgeSensorUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(SensorDataComponent), nameof(SubscribeFridgeSensorUseCase));
                Snackbar.Add($"Не удалось подписаться на события датчика из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.",
                MudBlazor.Severity.Error);
                break;

            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(SensorDataComponent), nameof(SubscribeFridgeSensorUseCase),
                response.Result);
                Snackbar.Add($"Не удалось подписаться на события датчика из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task UnsubscribeAsync()
    {
        if (_observer is null) return;

        UnsubscribeFridgeSensorUseCase.Response? response = null;
        var observerToDelete = _observer;

        try
        {
            response = await ScopeFactory.MediatorSend(new UnsubscribeFridgeSensorUseCase.Request(_observer));
        }
        catch (Exception ex)
        {
            response = null;
            Snackbar.Add($"Не удалось отменить подписку на события датчика из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }


        // Always try to delete the object reference, even if unsubscribe failed
        try
        {
            if (observerToDelete is not null)
            {
                _clusterClient.DeleteObjectReference<ISensorObserver>(observerToDelete);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to delete Orleans object reference for observer");
        }
        finally
        {
            _observer = null;
        }

        if (response is null) return;

        switch (response.Result)
        {
            case UnsubscribeFridgeSensorUseCase.Result.Success:
                break;

            case UnsubscribeFridgeSensorUseCase.Result.ValidationError:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(SensorDataComponent), nameof(UnsubscribeFridgeSensorUseCase));
                Snackbar.Add($"Не удалось отменить подписку на события датчика из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.",
                MudBlazor.Severity.Error);
                break;

            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(SensorDataComponent), nameof(UnsubscribeFridgeSensorUseCase),
                response.Result);
                Snackbar.Add($"Не удалось отменить подписку на события датчика из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    public async ValueTask DisposeAsync()
    {
        await UnsubscribeAsync();
    }

    private void OnTimerElapsed(object? sender, ElapsedEventArgs e)
    {
        InvokeAsync(async () =>
        {
            await SubscribeAsync();
            StateHasChanged();
        });
    }
}
