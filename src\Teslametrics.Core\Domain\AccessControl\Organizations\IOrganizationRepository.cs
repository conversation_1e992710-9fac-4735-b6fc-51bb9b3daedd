using Teslametrics.Core.Abstractions;

namespace Teslametrics.Core.Domain.AccessControl.Organizations;

public interface IOrganizationRepository : IRepository<OrganizationAggregate>
{
    public Task<List<OrganizationAggregate>> FindByOwnerId(Guid ownerId, CancellationToken cancellationToken = default);

    public Task<List<OrganizationAggregate>> GetOrganizationsByOwnerIdAsync(Guid ownerId, CancellationToken cancellationToken = default);

    public Task<bool> IsOrganizationExistsAsync(Guid organizationId, CancellationToken cancellationToken = default);

    public Task<bool> IsOrganizationNameExistsAsync(string name, CancellationToken cancellationToken = default);

    public Task<bool> IsPresetUsedInQuotasAsync(Guid id, CancellationToken cancellationToken = default);
}