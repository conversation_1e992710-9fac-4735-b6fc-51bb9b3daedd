using Blazor.Diagrams.Core;
using Blazor.Diagrams.Core.Events;
using Blazor.Diagrams.Core.Geometry;
using Blazor.Diagrams.Core.Models.Base;
using Teslametrics.App.Web.Components.Diagrams.Models;

namespace Teslametrics.App.Web.Components.Diagrams.Behaivor;

public class VertexDragBehavior : Behavior
{
    private Point? _initialPosition;
    private MovableVertexModel? _movingVertexModel;

    private double? _lastClientX;
    private double? _lastClientY;
    private bool _moved;

    public VertexDragBehavior(Diagram diagram) : base(diagram)
    {
        Diagram.PointerDown += OnPointerDown;
        //Diagram.PointerMove += OnPointerMove;
        //Diagram.PointerUp += OnPointerUp;
    }

    private void OnPointerDown(Model? model, PointerEventArgs e)
    {
        if (model is not MovableVertexModel movableVertex || movableVertex.Locked)
            return;

        _initialPosition = movableVertex.Position;
        _movingVertexModel = movableVertex;

        _lastClientX = e.ClientX;
        _lastClientY = e.ClientY;
        _moved = false;

        Diagram.PointerMove += OnPointerMove;
        Diagram.PointerUp += OnPointerUp;
    }

    private void OnPointerMove(Model? model, PointerEventArgs e)
    {
        if (_initialPosition is null || _movingVertexModel is null || _lastClientX == null || _lastClientY == null)
            return;

        _moved = true;

        double deltaX = (e.ClientX - _lastClientX.Value) / Diagram.Zoom;
        double deltaY = (e.ClientY - _lastClientY.Value) / Diagram.Zoom;

        var ndx = ApplyGridSize(deltaX + _initialPosition.X);
        var ndy = ApplyGridSize(deltaY + _initialPosition.Y);
        //if (e.ShiftKey) <- Попытка сделать с шифтом перетаскивание по прямой оси X/Y
        //{
        //	double cos = Math.Round((_initialPosition.X * deltaX + _initialPosition.Y * deltaY) / (Math.Sqrt(_initialPosition.X * _initialPosition.X + _initialPosition.Y * _initialPosition.Y) * Math.Sqrt(deltaX * deltaX + deltaY * deltaY)), 9);
        //	if(Math.Acos(cos) % 1 > 0.5)
        //	{
        //		ndx = _initialPosition.X;
        //	}
        //	else
        //	{
        //		ndy = _initialPosition.Y;
        //	}
        //}
        _movingVertexModel.SetPosition(ndx, ndy);
    }

    private void OnPointerUp(Model? model, PointerEventArgs e)
    {
        Diagram.PointerMove -= OnPointerMove;
        Diagram.PointerUp -= OnPointerUp;

        if (_movingVertexModel is null)
            return;

        if (_moved)
        {
            _movingVertexModel.TriggerMoved();
            if (_movingVertexModel.Selected)
            {
                Diagram.UnselectModel(_movingVertexModel);
            }
        }

        _initialPosition = null;
        _movingVertexModel = null;
        _lastClientX = null;
        _lastClientY = null;
    }

    private double ApplyGridSize(double n)
    {
        if (Diagram.Options.GridSize == null)
            return n;

        var gridSize = Diagram.Options.GridSize.Value;
        return gridSize * Math.Floor((n + gridSize / 2.0) / gridSize);
    }

    public override void Dispose()
    {
        _lastClientX = null;
        _lastClientY = null;
        _initialPosition = null;
        _movingVertexModel = null;

        Diagram.PointerDown -= OnPointerDown;
        Diagram.PointerMove -= OnPointerMove;
        Diagram.PointerUp -= OnPointerUp;
    }
}
