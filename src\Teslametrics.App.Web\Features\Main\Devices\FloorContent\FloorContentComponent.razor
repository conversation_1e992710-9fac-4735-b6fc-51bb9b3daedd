@inherits InteractiveBaseComponent
<div class="d_contents">
	<NoItemsFoundComponent HasItems="!(_response is null && !IsLoading)" />
	@if (_response is not null)
	{
		<MudGrid Spacing="2">
			<MudItem xs="12">
				<MudPaper Elevation="0"
						  Outlined="true"
						  Class="@(_response.Rooms.Count == 0 || _response.Rooms.All(x => x.ActiveIncidentCount == 0) ? "incidents pa-4 br_12" : "incidents pa-4 error br_12")">
					<InfoCardComponent Icon="@(_response.Rooms.Count == 0 || _response.Rooms.All(x => x.ActiveIncidentCount == 0) ? TeslaIcons.State.Success : TeslaIcons.State.Warning)"
									   Title="@_response.Rooms.Sum(x => x.ActiveIncidentCount).ToString()"
									   Subtitle="Происшествия"
									   Error="_response.Rooms.Count != 0 && _response.Rooms.Any(x => x.ActiveIncidentCount != 0)" />
				</MudPaper>
			</MudItem>
			<MudItem xs="12"
					 md="6">
				<MudPaper Elevation="0"
						  Outlined="true"
						  Class="pa-4 br_12">
					<InfoCardComponent Icon="@TeslaIcons.Devices.Fridge"
									   Title="@_response.Rooms.Sum(x => x.FridgeCount).ToString()"
									   Subtitle="Холодильники" />
				</MudPaper>
			</MudItem>
			<MudItem xs="12"
					 md="6">
				<MudPaper Elevation="0"
						  Outlined="true"
						  Class="pa-4 br_12">
					<InfoCardComponent Icon="@TeslaIcons.Devices.Camera"
									   Title="@_response.Rooms.Sum(x => x.CameraCount).ToString()"
									   Subtitle="Камеры" />
				</MudPaper>
			</MudItem>
		</MudGrid>
		<MudList T="GetRoomListUseCase.Response.Room"
				 Class="d-flex gap-2 flex-column list">
			@foreach (var room in _response.Rooms)
			{
				<MudListItem Class="list_item px-4 py-3"
							 Icon="@(room.ActiveIncidentCount == 0 ? TeslaIcons.State.Success : TeslaIcons.State.Warning)"
							 IconColor="@(room.ActiveIncidentCount == 0 ? Color.Default : Color.Error)"
							 OnClick="() => OnRoomSelectAsync(room)"
							 @key="room">
					<ChildContent>
						<div class="content">
							<MudText Inline="true"
									 Class="room_name">
								<MudTooltip Text="@room.Name"
											Arrow="true"
											Placement="Placement.Start">@room.Name
								</MudTooltip>
							</MudText>

							<MudTooltip Text="Холодильники"
										Arrow="true"
										Placement="Placement.Start">
								<div class="data_item">
									@room.FridgeCount
									<MudIcon Icon="@TeslaIcons.Devices.Fridge"
											 Size="Size.Small" />
								</div>
							</MudTooltip>

							<MudTooltip Text="Камеры"
										Arrow="true"
										Placement="Placement.Start">
								<div class="data_item">
									@room.CameraCount
									<MudIcon Icon="@TeslaIcons.Devices.Camera"
											 Size="Size.Small" />
								</div>
							</MudTooltip>

							<MudTooltip Text="Датчики"
										Arrow="true"
										Placement="Placement.Start">
								<div class="data_item">
									@room.SensorCount
									<MudIcon Icon="@TeslaIcons.Devices.Sensors"
											 Size="Size.Small" />
								</div>
							</MudTooltip>
							<div class="to_room">
								<MudIcon Icon="@Icons.Material.Filled.ArrowForwardIos"
										 Size="Size.Small" />
							</div>
						</div>
					</ChildContent>
				</MudListItem>
			}
		</MudList>
	}
</div>