﻿@inherits InteractiveBaseComponent
<MudAutocomplete T="GetBuildingListUseCase.Response.Item"
                 SearchFunc="@SearchCityAsync"
                 Margin="Margin.Dense"
                 ToStringFunc="@(e => e == null ? null : e.Name)"
                 Value="@_selected"
                 ValueChanged="@CityValueChanged"
                 Label="Здание"
                 Clearable="true"
                 ResetValueOnEmptyText="true"
                 Variant="Variant.Outlined"
                 Disabled="City is null" />