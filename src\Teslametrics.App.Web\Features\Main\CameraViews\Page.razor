﻿@using Teslametrics.App.Web.Features.Main.CameraViews.TreeView
@using Teslametrics.App.Web.Features.Main.CameraViews.ViewFrame
@using Teslametrics.App.Web.Features.Main.CameraViews.DeleteDialog
@using Teslametrics.App.Web.Features.Main.CameraViews.Drawer
@using Teslametrics.Shared
@attribute [Route(RouteConstants.CameraViews)]
@attribute [AppAuthorize(AppPermissions.Main.CameraViews.Read)]
@inherits InteractiveBaseComponent
<PageTitle>Multimonitor | Виды</PageTitle>
<div class="d_contents">
    <MudSwipeArea OnSwipeEnd="@OnSwipeEnd"
                  Class="mud-width-full mud-height-full overflow-hidden swipe_container">
        @if (UserDeviceService.IsMobile)
        {
            <MudDrawer @bind-Open="@_drawerOpen"
                       Breakpoint="Breakpoint.Always"
                       Fixed="true"
                       Width="100%"
                       Elevation="1"
                       Variant="@DrawerVariant.Temporary">
                <TreeViewComponent OrganizationId="@OrganizationId"
                                   ViewId="@ViewId"
                                   OnParametersChanged="OnTreeParametersChanged" />
            </MudDrawer>
        }
        else
        {
            <MudPaper Class="camera_tree overflow-hidden px-2"
                      Elevation="0">
                <TreeViewComponent OrganizationId="@OrganizationId"
                                   ViewId="@ViewId"
                                   OnParametersChanged="OnTreeParametersChanged" />
            </MudPaper>
        }
        @if (OrganizationId is not null && ViewId is not null)
        {
            <ViewFrameComponent OrganizationId="@OrganizationId.Value"
                                ViewId="@ViewId.Value" />
        }
        <NoTreeElementSelectedComponent Show="!OrganizationId.HasValue || !ViewId.HasValue"
                                        LeftIcon="@Icons.Material.Filled.Folder"
                                        RightIcon="@Icons.Material.Filled.ViewModule"
                                        Title="Выберите вид"
                                        Subtitle="Для просмотра камер выберите вид в дереве слева" />
    </MudSwipeArea>
</div>
<CameraViewDrawerComponent />
<DeleteDialogComponent />