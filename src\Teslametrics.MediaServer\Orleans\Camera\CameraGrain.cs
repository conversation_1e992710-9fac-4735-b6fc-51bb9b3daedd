using Orleans.Streams;
using Teslametrics.MediaServer.Orleans.Camera.Events;
using Teslametrics.Shared;
using static Teslametrics.MediaServer.Orleans.Camera.ICameraGrain;

namespace Teslametrics.MediaServer.Orleans.Camera;

public class CameraGrain : Grain, ICameraGrain
{
    private readonly ILogger<CameraGrain> _logger;
    private RtspStateMachine _rtspStateMachine;
    private readonly OnvifStateMachine _onvifStateMachine;
    private readonly CancellationTokenSource _cts;
    private CameraStatus _cameraStatus = CameraStatus.Stopped;
    private IAsyncStream<CameraStatusChangedEvent>? _eventLiveStream;
    //private Task? _updateStatusTask;
    private IGrainTimer? _rtspStateMachineTimer;
    private IGrainTimer? _onvifStateMachineTimer;
    private IGrainTimer? _updateStatusTimer;
    //private static readonly TimeSpan _updateInterval = TimeSpan.FromSeconds(1);

    public CameraGrain(ILoggerFactory loggerFactory,
                       IServiceScopeFactory serviceScopeFactory)
    {
        _logger = loggerFactory.CreateLogger<CameraGrain>();

        _rtspStateMachine = new RtspStateMachine(loggerFactory.CreateLogger<RtspStateMachine>(), this.GetPrimaryKey(), serviceScopeFactory);

        _onvifStateMachine = new OnvifStateMachine(loggerFactory.CreateLogger<OnvifStateMachine>(), this.GetPrimaryKey(), serviceScopeFactory);

        _cts = new CancellationTokenSource();
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        var provider = this.GetStreamProvider(StreamNames.CameraEventLiveStream);
        _eventLiveStream = provider.GetStream<CameraStatusChangedEvent>(StreamId.Create(StreamNamespaces.CameraEventStreams, Guid.Empty));

        //_updateStatusTask = Task.Run(() => UpdateStatusAsync(_cts.Token));
        await base.OnActivateAsync(cancellationToken);
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        if (await _rtspStateMachine.GetStatusAsync() is not RtspStateMachine.Status.Stopped)
        {
            _rtspStateMachine.Disconnect();
        }

        if (_onvifStateMachine.GetStatus() is not (OnvifStateMachine.Status.Disabled or OnvifStateMachine.Status.Stopped))
        {
            await _onvifStateMachine.DisconnectAsync();
        }

        if (_cameraStatus is not CameraStatus.Stopped)
        {
            _updateStatusTimer!.Dispose();
            _updateStatusTimer = null;
        }

        //_cts.Cancel();
        //await _updateStatusTask!;

        // Освобождаем CancellationTokenSource
        _cts.Dispose();

        await base.OnDeactivateAsync(reason, cancellationToken);
    }

    public Task<CameraStatus> GetStatusAsync() =>
        Task.FromResult(_cameraStatus);

    public Task ConnectRtspAsync(ConnectRtspRequest request)
    {
        _rtspStateMachine.Disconnected -= RtspStateMachine_Disconnected;
        _onvifStateMachine.Disconnected -= OnvifStateMachine_Disconnected;

        _updateStatusTimer ??= this.RegisterGrainTimer(UpdateStatusAsync, new GrainTimerCreationOptions(TimeSpan.Zero, TimeSpan.FromMilliseconds(1000)) { KeepAlive = true });
        _rtspStateMachineTimer ??= this.RegisterGrainTimer(_rtspStateMachine.ProcessAsync, new GrainTimerCreationOptions(TimeSpan.Zero, TimeSpan.FromMilliseconds(100)));
        _rtspStateMachine.Connect(request.ArchiveUri, request.ViewUri, request.PublicUri);

        return Task.CompletedTask;
    }

    public async Task ConnectOnvifAsync(ConnectOnvifRequest request)
    {
        _onvifStateMachineTimer ??= this.RegisterGrainTimer(_onvifStateMachine.ProcessAsync, new GrainTimerCreationOptions(TimeSpan.Zero, TimeSpan.FromMilliseconds(100)));
        await _onvifStateMachine.ConnectAsync(request.Host, request.Port, request.Username, request.Password);
    }

    public async Task DisconnectAsync()
    {
        _rtspStateMachine.Disconnected += RtspStateMachine_Disconnected;
        _onvifStateMachine.Disconnected += OnvifStateMachine_Disconnected;
            
        _rtspStateMachine.Disconnect();

        await _onvifStateMachine.DisconnectAsync();
    }

    private void RtspStateMachine_Disconnected()
    {
        _rtspStateMachineTimer!.Dispose();
        _rtspStateMachineTimer = null;
    }

    private void OnvifStateMachine_Disconnected()
    {
        _onvifStateMachineTimer!.Dispose();
        _onvifStateMachineTimer = null;
    }

    public Task<GetCameraStreamIdResponse> GetCameraStreamIdAsync(GetCameraStreamIdRequest request) =>
         Task.FromResult(new GetCameraStreamIdResponse(_rtspStateMachine.GetCameraStreamGrain(request.StreamType)?.GetPrimaryKey()));

    private async Task UpdateStatusAsync(CancellationToken cancellationToken = default)
    {
        //var currentTime = DateTime.UtcNow;

        //while (!cancellationToken.IsCancellationRequested)
        //{
        var state = (await _rtspStateMachine.GetStatusAsync(), _onvifStateMachine.GetStatus());

        var status = state switch
        {
            (RtspStateMachine.Status.Problem, _) => CameraStatus.Problem,
            (_, OnvifStateMachine.Status.Problem) => CameraStatus.Problem,
            (RtspStateMachine.Status.Stopped, OnvifStateMachine.Status.Disabled) => CameraStatus.Stopped,
            (RtspStateMachine.Status.Connecting, OnvifStateMachine.Status.Disabled) => CameraStatus.Connecting,
            (RtspStateMachine.Status.Running, OnvifStateMachine.Status.Disabled) => CameraStatus.Running,
            (RtspStateMachine.Status.Running, OnvifStateMachine.Status.Stopped) => CameraStatus.Running,
            (RtspStateMachine.Status.Stopped, OnvifStateMachine.Status.Stopped) => CameraStatus.Stopped,
            (RtspStateMachine.Status.Connecting, _) => CameraStatus.Connecting,
            (_, OnvifStateMachine.Status.Starting) => CameraStatus.Connecting,
            (RtspStateMachine.Status.Running, OnvifStateMachine.Status.Running) => CameraStatus.Running,
            _ => CameraStatus.Problem
        };

        if (_cameraStatus != status)
        {
            _cameraStatus = status;

            await _eventLiveStream!.OnNextAsync(new CameraStatusChangedEvent(this.GetPrimaryKey(), _cameraStatus));

            if (_cameraStatus == CameraStatus.Stopped)
            {
                _updateStatusTimer!.Dispose();
                _updateStatusTimer = null;
            }
        }

            //var deltaTime = DateTime.UtcNow - currentTime;

            //var remainingTime = _updateInterval - deltaTime;

            //if (remainingTime > TimeSpan.Zero)
            //{
            //    await Task.Delay(remainingTime);
            //}

            //currentTime = DateTime.UtcNow;
        //}
    }
}