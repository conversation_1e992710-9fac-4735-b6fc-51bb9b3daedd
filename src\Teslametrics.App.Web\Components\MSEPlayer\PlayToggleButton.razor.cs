using Microsoft.AspNetCore.Components;

namespace Teslametrics.App.Web.Components.MSEPlayer;

public partial class PlayToggleButton : IDisposable
{
    private bool _disposed = false;

    [Parameter]
    [EditorRequired]
    public IMsePlayer Player { get; set; } = null!;

    protected override void OnInitialized()
    {
        Player.StreamStatusChanged += PlayerStreamStatusChanged;

        base.OnInitialized();
    }

    private async Task Toggle()
    {
        switch (Player.Status)
        {
            case IMsePlayer.StreamStatus.Playing:
                await Player.PauseStream();
                break;
            case IMsePlayer.StreamStatus.Paused:
                await Player.ResumeStream();
                break;
            case IMsePlayer.StreamStatus.Stopped:
                await Player.ResumeStream();
                break;
        }
    }

    private void PlayerStreamStatusChanged(object? sender, IMsePlayer.StreamStatus streamStatus)
    {
        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        // Dispose of unmanaged resources.
        Dispose(true);
        // Suppress finalization.
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (_disposed)
        {
            return;
        }

        if (disposing)
        {
            if (Player != null)
            {
                Player.StreamStatusChanged -= PlayerStreamStatusChanged;
            }
        }

        _disposed = true;
    }
}
