using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.JSInterop;
using MudBlazor;

namespace Teslametrics.App.Web.Features.Main.Devices;

public partial class DevicesPage
{
	[Inject]
	private NavigationManager NavigationManager { get; set; } = null!;

	[SupplyParameterFromQuery(Name = "cityId")]
	public string? CityIdQuery { get; set; }

	[SupplyParameterFromQuery(Name = "buildingId")]
	public string? BuildingIdQuery { get; set; }

	[SupplyParameterFromQuery(Name = "floorId")]
	public string? FloorIdQuery { get; set; }

	[SupplyParameterFromQuery(Name = "roomId")]
	public string? RoomIdQuery { get; set; }

	private Guid? _cityId;
	private Guid? _buildingId;
	private Guid? _floorId;
	private Guid? _roomId;

	protected override void OnParametersSet()
	{
		// Parse and validate cityId
		if (!string.IsNullOrWhiteSpace(CityIdQuery) && Guid.TryParse(CityIdQuery, out var cityGuid))
		{
			_cityId = cityGuid;
		}

		// Parse and validate buildingId
		if (!string.IsNullOrWhiteSpace(BuildingIdQuery) && Guid.TryParse(BuildingIdQuery, out var buildingGuid))
		{
			_buildingId = buildingGuid;
		}

		// Parse and validate floorId
		if (!string.IsNullOrWhiteSpace(FloorIdQuery) && Guid.TryParse(FloorIdQuery, out var floorGuid))
		{
			// We need to set _selectedFloor, but we only have the ID
			// The floor number will be updated when the floor list is loaded
			if (_floorId == null || _floorId != floorGuid)
			{
				_floorId = floorGuid;
			}
		}
		else
		{
			_floorId = null;
		}

		// Parse and validate roomId
		if (!string.IsNullOrWhiteSpace(RoomIdQuery) && Guid.TryParse(RoomIdQuery, out var roomGuid))
		{
			// We'll need to load the room details to get the name
			if (_roomId == null || _roomId != roomGuid)
			{
				// Create a temporary room object with just the ID
				// The rest of the details will be filled in when the room list is loaded
				_roomId = roomGuid;
			}
		}
		else
		{
			_roomId = null;
		}
	}

	private void OnRoomSelect(Guid? room)
	{
		_roomId = room;
		UpdateQueryParameters();
	}

	private void OnFloorSelect(Guid? floor)
	{
		_floorId = floor;
		_roomId = null; // Clear room selection when floor changes
		UpdateQueryParameters();
	}

	private void OnCityChanged(Guid? CityId)
	{
		_cityId = CityId;
		_buildingId = null;
		_floorId = null;
		_roomId = null;
		UpdateQueryParameters();
	}

	private void OnBuildingChanged(Guid? BuildingId)
	{
		_buildingId = BuildingId;
		_floorId = null;
		_roomId = null;
		UpdateQueryParameters();
	}

	private void UpdateQueryParameters()
	{
		try
		{
			var queryParameters = new Dictionary<string, string?>();

			if (_cityId.HasValue)
				queryParameters.Add("cityId", _cityId.Value.ToString());

			if (_buildingId.HasValue)
				queryParameters.Add("buildingId", _buildingId.Value.ToString());

			if (_floorId != null)
				queryParameters.Add("floorId", _floorId.ToString());

			if (_roomId != null)
				queryParameters.Add("roomId", _roomId.ToString());

			var uriWithoutQuery = NavigationManager.Uri.Split('?')[0];
			var newUri = QueryHelpers.AddQueryString(uriWithoutQuery, queryParameters);

			if (NavigationManager.Uri != newUri)
				NavigationManager.NavigateTo(newUri, forceLoad: false, replace: true);
		}
		catch (Exception) // Данное исключение прокинется, если пользователь покинет страницу, пока ему не поствится новый url с помощью NavigationManager
		{
		}
	}
}
