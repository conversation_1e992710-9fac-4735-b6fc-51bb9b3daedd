﻿<div class="d_contents">
    <MudPaper Class="pa-1 pr-5 d-flex align-center gap-2 quick-range"
              Elevation="0">

        <!--  Быстрые пресеты  -->
        <MudToggleGroup T="FilterDate"
                        Value="@_quick"
                        Color="Color.Default"
                        Outlined="false"
                        Delimiters="false"
                        Class="toggle_group"
                        ValueChanged="OnQuickChanged">
            <MudToggleItem T="FilterDate"
                           Value="@FilterDate.Today">Сегодня</MudToggleItem>
            <MudToggleItem T="FilterDate"
                           Value="@FilterDate.Yesterday">Вчера</MudToggleItem>
            <MudToggleItem T="FilterDate"
                           Value="@FilterDate.Week">Неделя</MudToggleItem>
            <MudToggleItem T="FilterDate"
                           Value="@FilterDate.Month">Месяц</MudToggleItem>
        </MudToggleGroup>

        <!--  Произвольный диапазон  -->
        <MudButton Variant="Variant.Text"
                   OnClick="OnDateRangeButtonClicked"
                   Class="@(_quick == FilterDate.Custom ? "range-btn active" : "range-btn")">
            @_rangeText
            <MudIcon Icon="@Icons.Material.Filled.KeyboardArrowDown"
                     Size="Size.Small"
                     Class="ml-1" />
            <MudPopover OverflowBehavior="OverflowBehavior.FlipNever"
                        Open="@_dateRangeOpened"
                        Fixed="true"
                        Elevation="3"
                        Class="br_20 overflow-hidden">
                <MudDateRangePicker DateRange="_range"
                                    DisplayMonths="2"
                                    ShowToolbar="false"
                                    DateRangeChanged="OnDateRangeChanged"
                                    PickerVariant="PickerVariant.Static" />
            </MudPopover>
        </MudButton>
        <MudOverlay @bind-Visible="_dateRangeOpened"
                    AutoClose="true" />

        <MudIcon Icon="@Icons.Material.Outlined.CalendarToday"
                 Color="Color.Primary" />
    </MudPaper>
</div>