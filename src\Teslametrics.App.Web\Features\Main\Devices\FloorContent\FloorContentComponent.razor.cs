using System.Reactive;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Devices.FloorContent;

public partial class FloorContentComponent
{
	private SubscribeRoomListUseCase.Response? _subscribeResponse;
	private bool _subscribing;
	private GetRoomListUseCase.Response? _response = null;

	public record Room(Guid Id, string Name, int FreezerCount, int CameraCount, int SensorCount, int AlarmCount);

	[Parameter]
	public EventCallback<Guid?> OnRoomSelected { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid FloorId { get; set; }

	protected override async Task OnParametersSetAsync()
	{
		await FetchAsync();
		await base.OnParametersSetAsync();
	}

	private async Task FetchAsync()
	{
		if (IsLoading) return;
		try
		{
			await SetLoadingAsync(true);
			_response = await ScopeFactory.MediatorSend(new GetRoomListUseCase.Query(FloorId));
		}
		catch (Exception ex)
		{
			_response = null;
			Logger.LogError(ex, ex.Message);
			Snackbar.Add($"Не удалось получить данные этажа из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
		}

		await SetLoadingAsync(false);
		if (_response is null) return;

		switch (_response.Result)
		{
			case GetRoomListUseCase.Result.Success:
				await SubscribeAsync();
				break;
			case GetRoomListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FloorContentComponent), nameof(GetRoomListUseCase));
				Snackbar.Add($"Не удалось получить данные этажа из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(FloorContentComponent), nameof(GetRoomListUseCase), _response.Result);
				Snackbar.Add($"Не удалось получить данные этажа из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
				break;
		}

	}

	private async Task OnRoomSelectAsync(GetRoomListUseCase.Response.Room room)
	{
		if (OnRoomSelected.HasDelegate)
			await OnRoomSelected.InvokeAsync(room.Id);
	}

	private async Task SubscribeAsync()
	{
		Unsubscribe();

		if (_response is null || !_response.IsSuccess || _subscribing) return;
		try
		{
			await SetSubscribingAsync(true);
			_subscribeResponse = await ScopeFactory.MediatorSend(new SubscribeRoomListUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError)));
		}
		catch (Exception ex)
		{
			_subscribeResponse = null;
			Snackbar.Add($"Не удалось подписаться на события комнаты из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetSubscribingAsync(false);
		if (_subscribeResponse is null) return;
		switch (_subscribeResponse.Result)
		{
			case SubscribeRoomListUseCase.Result.Success:
				break;
			case SubscribeRoomListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события комнаты", MudBlazor.Severity.Error);
				break;
			case SubscribeRoomListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FloorContentComponent), nameof(SubscribeRoomListUseCase));
				Snackbar.Add($"Не удалось подписаться на события комнаты из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(FloorContentComponent), nameof(SubscribeRoomListUseCase), _subscribeResponse.Result);
				Snackbar.Add($"Не удалось подписаться на события комнаты из-за ошибки: {_subscribeResponse.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private void Unsubscribe()
	{
		if (_subscribeResponse?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscribeResponse.Subscription);
			_subscribeResponse.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		await FetchAsync();
		await UpdateViewAsync();
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]
}
