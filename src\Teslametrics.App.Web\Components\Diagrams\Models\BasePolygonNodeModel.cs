using Blazor.Diagrams.Core.Models.Base;
using Blazor.Diagrams.Core.Models;
using System.Collections.ObjectModel;
using Blazor.Diagrams.Core.Geometry;

namespace Teslametrics.App.Web.Components.Diagrams.Models;

public class BasePolygonNodeModel : NodeModel, IDisposable
{
	private readonly List<MovableVertexModel> _vertexPoints;

	public ReadOnlyCollection<MovableVertexModel> VertexPoints => _vertexPoints.AsReadOnly();

	public BasePolygonNodeModel(string id, IEnumerable<Point> points, Point position) : base(id, position)
	{
		ControlledSize = true;
		_vertexPoints = [.. points.Select(item => new MovableVertexModel(this, item))];
		_vertexPoints.ForEach(point =>
		{
			point.Moved += OnVertexMoved;
		});

		UpdateSize();
	}

	public BasePolygonNodeModel(Point position) : base(position)
	{
		ControlledSize = true;
		_vertexPoints = new();
		UpdateSize();
	}

	public Point AddShapePoint(Point point, int? position = null)
	{
		if (_vertexPoints.Any(vertex => vertex.Position == point)) return point;

		var movable = new MovableVertexModel(this, point);
		if (position is not null)
		{
			_vertexPoints.Insert(position.Value, movable);
		}
		else
		{
			_vertexPoints.Add(movable);
		}

		if (point.X < 0 || point.Y < 0)
		{
			var offsetX = point.X > 0 ? 0 : -point.X;
			var offsetY = point.Y > 0 ? 0 : -point.Y;

			SetOffsets(offsetX, offsetY);
			UpdateSize();
			SetPosition(Position.X - offsetX, Position.Y - offsetY);
		}

		UpdateSize();

		movable.Moved += OnVertexMoved;
		return point;
	}

	public bool RemoveVertex(MovableVertexModel vertex)
	{
		var removed = _vertexPoints.Remove(vertex);
		if (removed)
			vertex.Moved -= OnVertexMoved;

		return removed;
	}

	private void SetOffsets(double offsetX, double offsetY)
	{
		foreach (var vertext in VertexPoints)
		{
			vertext.SetPosition(vertext.Position.X + offsetX, vertext.Position.Y + offsetY);
		}
	}

	private void UpdateSize()
	{
		if (_vertexPoints.Count > 0)
		{
			var width = _vertexPoints.Max(point => point.Position.X) - _vertexPoints.Min(point => point.Position.X);
			var height = _vertexPoints.Max(point => point.Position.Y) - _vertexPoints.Min(point => point.Position.Y);

			if (Size is null || Size.Width != width || Size.Height != height)
				Size = new Size(width, height);
		}
		else
		{
			Size = Size.Zero;
		}
	}

	// Написано в пьяном угаре в первом часу ночи. Вероятно можно оптимизировать
	private void OnVertexMoved(MovableModel vertexModel)
	{
		double offsetX = 0;
		double offsetY = 0;
		if (vertexModel.Position.X < 0 || vertexModel.Position.Y < 0)
		{
			offsetX = vertexModel.Position.X > 0 ? 0 : -vertexModel.Position.X;
			offsetY = vertexModel.Position.Y > 0 ? 0 : -vertexModel.Position.Y;
		}

		if (!_vertexPoints.Any(x => x.Position.X == 0) && offsetX == 0)
		{
			var minimalX = _vertexPoints.Min(x => x.Position.X);
			offsetX = -minimalX;
		}

		if (!_vertexPoints.Any(x => x.Position.Y == 0) && offsetY == 0)
		{
			var minimalY = _vertexPoints.Min(x => x.Position.Y);
			offsetY = -minimalY;
		}

		SetOffsets(offsetX, offsetY);
		SetPosition(Position.X - offsetX, Position.Y - offsetY);
		UpdateSize();
		Refresh(); // <- Нужен, чтобы обновились ссылки и прочее
	}

	public void Dispose()
	{
		foreach (var item in VertexPoints)
		{
			item.Moved -= OnVertexMoved;
		}
	}
}
