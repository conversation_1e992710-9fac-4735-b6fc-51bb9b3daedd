using System.Reactive;
using FluentValidation;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Extensions;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.CameraViews.Drawer.Edit;

public partial class DrawerEditComponent
{
	private class Cell(GridItem.GridItemComponent.CameraViewCell? camera, short index)
	{
		public GridItem.GridItemComponent.CameraViewCell? Camera { get; set; } = camera;
		public short CellIndex { get; set; } = index;
		public Guid CellId = Guid.NewGuid();
	}
	private class Model
	{
		private short _rows = 2;
		private short _columns = 2;
		private int _cellCount = 4;

		private GridType _gridType = GridType.GridCustom;
		public string Name { get; set; } = string.Empty;
		public GridType Grid
		{
			get => _gridType; set
			{
				if (_gridType == value)
				{
					return;
				}

				_gridType = value;
				switch (value)
				{
					case GridType.Grid1Plus5:
						_rows = 3;
						_columns = 3;
						_cellCount = 6;
						break;

					case GridType.Grid1Plus12:
						_rows = 4;
						_columns = 4;
						_cellCount = 13;
						break;

					case GridType.Grid1Plus7:
						_rows = 4;
						_columns = 4;
						_cellCount = 8;
						break;

					case GridType.Grid2Plus8:
						_rows = 4;
						_columns = 4;
						_cellCount = 10;
						break;
					case GridType.Grid3Plus4:
						_rows = 4;
						_columns = 4;
						_cellCount = 7;
						break;

					default:
						_rows = 2;
						_columns = 2;
						_cellCount = 4;
						break;
				}
				UpdateCellsLayout();
			}
		}

		public short Rows
		{
			get => _rows;
			set
			{
				if (_rows != value)
				{
					_rows = value;
					_cellCount = _columns * _rows;
					UpdateCellsLayout();
				}
			}
		}

		public short Columns
		{
			get => _columns;
			set
			{
				if (_columns != value)
				{
					_columns = value;
					_cellCount = _columns * _rows;
					UpdateCellsLayout();
				}
			}
		}

		public List<Cell> Cells { get; set; } = Enumerable.Range(0, 4).Select(i => new Cell(null, (short)i)).ToList();

		private void UpdateCellsLayout()
		{
			var existingCells = Cells.OrderBy(c => c.CellIndex).ToList();
			var newCells = new List<Cell>();

			var cells = existingCells.Where(c => c.Camera != null).Take((int)_cellCount);
			if (Cells.Count < _cellCount)
			{
				Cells.AddRange(Enumerable.Range(Cells.Count, (int)(_cellCount - Cells.Count)).Select(i => new Cell(null, (short)i)));
			}
			else if (Cells.Count > _cellCount)
			{
				Cells.RemoveRange((int)_cellCount, Cells.Count - (int)_cellCount);
			}
		}
	}

	private class Validator : BaseFluentValidator<Model>
	{
		public Validator()
		{
			RuleFor(model => model.Name)
				.Length(3, 60)
				.WithMessage("наименование должно быть длиной от 3 до 60 символов");
		}
	}

	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	private bool _isValid => _validator.Validate(_model).IsValid;
	private Model _model = new();
	private Validator _validator = new();


	private Guid _organizationId;
	private Guid _viewId;

	private GetViewUseCase.Response? _viewResponse;
	private DateTime _lastRefreshTime = DateTime.Now;
	private bool _subscribing = false;
	private SubscribeViewUseCase.Response _subscriptionResult = null!;

	#region [Parameters]
	[Parameter]
	[EditorRequired]
	public Guid ViewId { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }
	#endregion

	protected override async Task OnParametersSetAsync()
	{
		await base.OnParametersSetAsync();

		if (OrganizationId != _organizationId || ViewId != _viewId)
		{
			_organizationId = OrganizationId;
			_viewId = ViewId;

			await SubscribeAsync();
			await FetchAsync();
		}
	}

	private async Task FetchAsync()
	{
		await SetLoadingAsync(true);
		_lastRefreshTime = DateTime.Now;
		try
		{
			_viewResponse = await ScopeFactory.MediatorSend(new GetViewUseCase.Query(ViewId));
		}
		catch (Exception exc)
		{
			_viewResponse = null;
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось получить список камер из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
		}

		await SetLoadingAsync(false);
		if (_viewResponse is null) return;
		switch (_viewResponse.Result)
		{
			case GetViewUseCase.Result.Success:
				_model.Name = _viewResponse.Name;
				_model.Columns = _viewResponse.ColumnCount;
				_model.Rows = _viewResponse.RowCount;
				_model.Grid = _viewResponse.GridType;
				FillCells();
				break;
			case GetViewUseCase.Result.ViewNotFound:
				Snackbar.Add("Не удалось получить вид. Вид не существует.", MudBlazor.Severity.Error);
				break;
			case GetViewUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(DrawerEditComponent), nameof(GetViewUseCase));
				Snackbar.Add($"Не удалось получить вид из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(DrawerEditComponent), nameof(GetViewUseCase), _viewResponse.Result);
				Snackbar.Add($"Не удалось получить вид из-за непредвиденной ошибки: {_viewResponse.Result}. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
		}
	}

	private void FillCells()
	{
		if (_model == null || _viewResponse is null) return;

		int cellCount = _viewResponse.GridType switch
		{
			GridType.Grid1Plus5 => 6,
			GridType.Grid1Plus7 => 8,
			GridType.Grid1Plus12 => 13,
			GridType.Grid2Plus8 => 10,
			GridType.Grid3Plus4 => 7,
			_ => _viewResponse.RowCount * _viewResponse.ColumnCount
		};
		_model.Cells.Clear();
		_model.Cells.Capacity = cellCount;

		// Initialize all cells as empty
		for (short i = 0; i < cellCount; i++)
		{
			var cell = new Cell(null, i);
			_model.Cells.Add(cell);
		}

		// Fill in the actual cells from _viewResponse
		foreach (var cell in _viewResponse.Cells)
		{
			if (cell.CellIndex < cellCount)
			{
				_model.Cells[(int)cell.CellIndex] = new Cell(new GridItem.GridItemComponent.CameraViewCell(cell.CameraId, cell.Name), cell.CellIndex);
			}
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			Unsubscribe();

			await SetSubscribingAsync(true);
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeViewUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), ViewId));
		}
		catch (Exception ex)
		{
			await SetSubscribingAsync(false);
			Snackbar.Add("Не удалось получить подписку на события камеры из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetSubscribingAsync(false);
		if (_subscriptionResult is null) return;

		switch (_subscriptionResult.Result)
		{
			case SubscribeViewUseCase.Result.Success:
				CompositeDisposable.Add(_subscriptionResult.Subscription!);
				break;
			case SubscribeViewUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
				break;
			case SubscribeViewUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(DrawerEditComponent), nameof(SubscribeViewUseCase));
				Snackbar.Add($"Не удалось получить подписку на события вида из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(DrawerEditComponent), nameof(SubscribeViewUseCase), _subscriptionResult.Result);
				Snackbar.Add($"Не удалось получить подписку на события вида из-за непредвиденной ошибки: {_subscriptionResult.Result}. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task CancelAsync() => Drawer.HideAsync();
	private async Task SubmitAsync()
	{
		UpdateViewUseCase.Response? response = null;
		try
		{
			var updateCellsLayout = _model.Cells.Where(c => c.Camera is not null && c.Camera.CameraId != Guid.Empty).Select(c => (c.Camera!.CameraId, c.CellIndex)).ToList();
			response = await ScopeFactory.MediatorSend(new UpdateViewUseCase.Command(ViewId, _model.Name, _model.Columns, _model.Rows, _model.Grid, updateCellsLayout));
		}
		catch (Exception exc)
		{
			response = null;
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось обновить вид из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
		}

		if (response is null) return;
		switch (response.Result)
		{
			case UpdateViewUseCase.Result.Success:
				Snackbar.Add("Вид успешно обновлён.", MudBlazor.Severity.Success);
				await Drawer.HideAsync();
				break;
			case UpdateViewUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации, проверьте правильность заполнения полей", MudBlazor.Severity.Error);
				break;
			case UpdateViewUseCase.Result.CameraViewNameAlreadyExists:
				Snackbar.Add("Вид с данным названием уже существует", MudBlazor.Severity.Error);
				break;
			case UpdateViewUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(DrawerEditComponent), nameof(UpdateViewUseCase));
				Snackbar.Add($"Не удалось обновить вид из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(DrawerEditComponent), nameof(UpdateViewUseCase), _subscriptionResult.Result);
				Snackbar.Add($"Не удалось обновить вид из-за непредвиденной ошибки: {_subscriptionResult.Result}. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
		}

	}
	private Task RefreshAsync() => FetchAsync();
	#endregion

	#region [Event Handlers]
	private void SetGridType(GridType type)
	{
		_model.Grid = type;
	}
	private void SortList((int oldIndex, int newIndex) indices)
	{
		// deconstruct the tuple
		var (oldIndex, newIndex) = indices;

		var itemToMove = _model.Cells[oldIndex];
		_model.Cells.RemoveAt(oldIndex);

		if (newIndex < _model.Cells.Count)
		{
			_model.Cells.Insert(newIndex, itemToMove);
		}
		else
		{
			_model.Cells.Add(itemToMove);
		}

		// Update indices for all cells based on their new positions
		for (short i = 0; i < _model.Cells.Count; i++)
		{
			_model.Cells[i].CellIndex = i;
		}
	}

	private async void OnAppEventHandler(object appEvent)
	{
		await FetchAsync();
		await UpdateViewAsync();
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]
}
