name: teslametrics
services:
  media-server:
    container_name: media-server
    build:
      context: ..
      dockerfile: eng/dockerfile-media-server
    restart: always
    user: root
    ports:
      - "127.0.0.1:30000:30000"
      - "5022:22"
    environment:
      ASPNETCORE_ENVIRONMENT: Tesla

  app-web:
    container_name: app-web
    build:
      context: ..
      dockerfile: eng/dockerfile-app-web
    restart: always
    user: root
    ports:
      - "80:80"
      - "443:443"
    environment:
      ASPNETCORE_ENVIRONMENT: Tesla
      #DOTNET_DiagnosticPorts: /diag/dp.socket,nosuspend
      DOTNET_EnableDiagnostics: 1
    #volumes:
      #- dotnet_diagnostics:/diag

#volumes:
  #dotnet_diagnostics: