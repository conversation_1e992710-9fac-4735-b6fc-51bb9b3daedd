.time-range-container {
    display: flex;
    align-items: center;
    border-radius: 8px;
    height: 100%;
    min-height: 40px;
    border: 1px solid var(--color-stroke-stroke);
}

::deep .time_icon {
    color: var(--mud-palette-text-secondary);
    font-size: 16px;
    width: 48px;
    height: 40px;
}

.time-section {
    display: flex;
    align-items: center;
    height: -webkit-fill-available;
}
.time-section:not(:first-child) {
    border-left: 1px solid var(--color-stroke-stroke);
}

::deep .mud-input-adornment-text,
.time-label {
    font-size: 0.75rem;
    color: var(--color-text-placeholder);
    font-weight: 400;
    white-space: nowrap;
    line-height: 1;
}

.time-display {
    display: flex;
    align-items: center;
    gap: 2px;
}

::deep .time-input {
    width: 70px;
}

::deep .time-input input {
    font-size: 0.75rem;
    padding-right: 20px !important;
    padding-inline-end: 20px !important;
}

::deep .mud-input-numeric-spin {
    background: var(--color-bg-3);
    border-radius: 4px;
    width: 16px;
}

::deep .mud-input.mud-input-text::after,
::deep .mud-input.mud-input-text::before {
    display: none !important;
}