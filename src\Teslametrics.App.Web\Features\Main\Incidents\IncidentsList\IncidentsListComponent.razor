@attribute [StreamRendering(true)]
@inherits InteractiveBaseComponent
<div class="d_contents">
    @if (_response is not null)
    {
        <MudDataGrid T="GetIncidentListUseCase.Response.Incident"
                     Items="@_response.Incidents"
                     Elevation="0"
                     Filterable="false"
                     SortMode="@SortMode.None"
                     Groupable="false"
                     RowClick="OnSelectedIncidentChanged"
                     MultiSelection="false"
                     Hover="true"
                     Loading="IsLoading"
                     Striped="true"
                     Virtualize="true"
                     FixedHeader="true"
                     Height="100%"
                     class="overflow-auto mud-height-full br_12"
                     RowClassFunc="@GetRowClass">
            <Columns>
                <TemplateColumn Title="Статус">
                    <CellTemplate>
                        <div class="d-flex justify-start">
                            <MudIcon Icon="@(context.Item.IsResolved? TeslaIcons.State.Success : TeslaIcons.State.Warning)"
                                     Color="@(context.Item.IsResolved? Color.Default: Color.Error)" />
                        </div>
                    </CellTemplate>
                </TemplateColumn>
                <PropertyColumn Property="x => x.City"
                                Title="Город" />
                <PropertyColumn Property="x => x.Address"
                                Title="Здание" />
                <PropertyColumn Property="x => x.IncidentType == Teslametrics.Shared.IncidentType.WirenboardDisconnected ? string.Empty : x.Floor.ToString()"
                                Title="Этаж" />
                <PropertyColumn Property="x => x.Room"
                                Title="Помещение" />
                <PropertyColumn Property="x => x.Device"
                                Title="Оборудование" />
                <PropertyColumn Property="x => x.Date.Date.ToShortDateString()"
                                Title="Дата" />
                <PropertyColumn Property="x => x.Time"
                                Title="Время" />
                <TemplateColumn CellClassFunc="@((context) => context.IsViewed ? string.Empty : "unread")"
                                Title="Тип происшествия">
                    <CellTemplate>
                        <MudStack Row="true"
                                  AlignItems="AlignItems.Center"
                                  Justify="Justify.SpaceBetween"
                                  Class="actions">
                            <MudIcon Icon="@GetIncidentIcon(context.Item.IncidentType)"
                                     Color="@(context.Item.IsResolved? Color.Default: Color.Error)" />
                            @context.Item.IncidentType.GetName()
                        </MudStack>
                    </CellTemplate>
                </TemplateColumn>
            </Columns>
            <NoRecordsContent>
                @if (IsLoading)
                {
                    <MudPaper Class="d-flex flex-column align-center justify-center pa-6"
                              Style="min-height: 200px; width: 100%; text-align: center;">
                        <MudSkeleton Width="50%"
                                     Height="24px"
                                     Class="mb-2" />
                        <MudSkeleton Width="60%"
                                     Height="20px"
                                     Class="mb-1" />
                        <MudSkeleton Width="40%"
                                     Height="20px" />
                    </MudPaper>
                }
                else
                {
                    <MudText Typo="Typo.body1"
                             Align="Align.Center"
                             Color="Color.Secondary">
                        Нет данных для отображения
                    </MudText>
                }
            </NoRecordsContent>
        </MudDataGrid>
    }
</div>