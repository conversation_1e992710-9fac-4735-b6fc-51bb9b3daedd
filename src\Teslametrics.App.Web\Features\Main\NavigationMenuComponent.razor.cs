using Microsoft.AspNetCore.Components;
using MudBlazor;
using MudBlazor.Services;

namespace Teslametrics.App.Web.Features.Main;

public partial class NavigationMenuComponent : IAsyncDisposable, IBrowserViewportObserver
{
    private bool _mobile;

    [Inject]
    private IBrowserViewportService BrowserViewportService { get; set; } = null!;

    #region Drawer
    public readonly IEnumerable<Breakpoint> Markers = [Breakpoint.Xs, Breakpoint.Sm, Breakpoint.Md, Breakpoint.SmAndDown, Breakpoint.MdAndDown];
    public Guid Id { get; } = Guid.NewGuid();
    #endregion

    [Parameter]
    public bool Open { get; set; }

    [Parameter]
    public EventCallback<bool> OpenChanged { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await BrowserViewportService.SubscribeAsync(this, fireImmediately: true);
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    public async ValueTask DisposeAsync() => await BrowserViewportService.UnsubscribeAsync(this);

    Guid IBrowserViewportObserver.Id { get; } = Guid.NewGuid();

    ResizeOptions IBrowserViewportObserver.ResizeOptions { get; } = new()
    {
        ReportRate = 250,
        NotifyOnBreakpointOnly = true
    };

    Task IBrowserViewportObserver.NotifyBrowserViewportChangeAsync(BrowserViewportEventArgs browserViewportEventArgs)
    {
        _mobile = Markers.Contains(browserViewportEventArgs.Breakpoint);

        return Task.CompletedTask;
    }
}
