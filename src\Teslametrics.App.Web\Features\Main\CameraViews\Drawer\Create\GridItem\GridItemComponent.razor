﻿@using Teslametrics.App.Web.Features.Main.CameraViews.Drawer.Create.GridItem.Preview
@inherits InteractiveBaseComponent
<div>
	<MudPaper Elevation="0"
			  Outlined="false"
			  Class="camera_card pa-1">
		<MudAutocomplete T="CameraViewCell"
						 SearchFunc="@SearchAsync"
						 Label="Камера"
						 ToStringFunc="@(e => e == null ? null : e.Name)"
						 Value="Camera"
						 Clearable="true"
						 ValueChanged="OnSelectedValudeChanged"
						 OpenChanged="OnOpenChangedAsync"
						 ResetValueOnEmptyText="true"
						 Variant="Variant.Outlined"
						 Margin="Margin.Dense"
						 Dense="true"
						 @ref="_ref">
			<NoItemsTemplate>
				<MudText Align="Align.Center"
						 Class="px-4 py-1">
					Не найдено камер с данным названием
				</MudText>
			</NoItemsTemplate>
		</MudAutocomplete>
		<div class="preview_container">
			<Preview CameraId="@Camera?.CameraId" />
		</div>
	</MudPaper>
</div>
