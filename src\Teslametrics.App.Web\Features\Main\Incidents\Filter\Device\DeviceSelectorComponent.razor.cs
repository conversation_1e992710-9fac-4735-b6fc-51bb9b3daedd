using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Incidents.Filter.Device;

public partial class DeviceSelectorComponent
{
    private bool _disabled => BuildingId is null || CityId is null || FloorId is null || RoomId is null;
    private GetDeviceListUseCase.Response.Item? _selected;

    [Parameter]
    public Guid? CityId { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }

    [Parameter]
    public Guid? FloorId { get; set; }

    [Parameter]
    public Guid? RoomId { get; set; }

    [Parameter]
    public Guid? DeviceId { get; set; }

    [Parameter]
    public EventCallback<Guid?> DeviceIdChanged { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        if (_selected?.Id != RoomId)
        {
            _selected = null;
            await GetDataAsync();
        }
        await base.OnParametersSetAsync();
    }

    private async Task GetDataAsync()
    {
        if (!CityId.HasValue || !BuildingId.HasValue || !FloorId.HasValue || !RoomId.HasValue || !DeviceId.HasValue) return;

        GetDeviceUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetDeviceUseCase.Query(CityId.Value, BuildingId.Value, FloorId.Value, RoomId.Value, DeviceId.Value));
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching cities");
            Snackbar.Add("Не удалось получить устройство из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case GetDeviceUseCase.Result.Success:
                _selected = new(CityId.Value, response.Name);
                break;

            case GetDeviceUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении устройства", MudBlazor.Severity.Error);
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(DeviceSelectorComponent), nameof(GetDeviceUseCase));
                break;
            case GetDeviceUseCase.Result.CityNotFound:
                Snackbar.Add("Город не найден", MudBlazor.Severity.Error);

                DeviceId = null;
                if (DeviceIdChanged.HasDelegate)
                    await DeviceIdChanged.InvokeAsync(BuildingId);

                break;

            case GetDeviceUseCase.Result.BuildingNotFound:
                Snackbar.Add("Здание не найдено", MudBlazor.Severity.Error);

                DeviceId = null;
                if (DeviceIdChanged.HasDelegate)
                    await DeviceIdChanged.InvokeAsync(BuildingId);
                break;

            case GetDeviceUseCase.Result.FloorNotFound:
                Snackbar.Add("Этаж не найден", MudBlazor.Severity.Error);

                DeviceId = null;
                if (DeviceIdChanged.HasDelegate)
                    await DeviceIdChanged.InvokeAsync(BuildingId);
                break;

            case GetDeviceUseCase.Result.RoomNotFound:
                Snackbar.Add("Комната не найдена", MudBlazor.Severity.Error);

                DeviceId = null;
                if (DeviceIdChanged.HasDelegate)
                    await DeviceIdChanged.InvokeAsync(BuildingId);
                break;

            case GetDeviceUseCase.Result.DeviceNotFound:
                Snackbar.Add("Устройство не найдено", MudBlazor.Severity.Error);

                DeviceId = null;
                if (DeviceIdChanged.HasDelegate)
                    await DeviceIdChanged.InvokeAsync(BuildingId);
                break;

            case GetDeviceUseCase.Result.Unknown:
                Snackbar.Add("Не удалось получить устройство из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(DeviceSelectorComponent), nameof(GetDeviceUseCase));
                break;

            default:
                Snackbar.Add("Неизвестная ошибка при получении устройства", MudBlazor.Severity.Error);
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(DeviceSelectorComponent), nameof(GetDeviceUseCase), response.Result);
                break;
        }
    }

    private async Task ValueChanged(GetDeviceListUseCase.Response.Item? item)
    {
        _selected = item;
        await DeviceIdChanged.InvokeAsync(item?.Id);
    }

    private async Task<IEnumerable<GetDeviceListUseCase.Response.Item>> SearchAsync(string value, CancellationToken token)
    {
        if (!CityId.HasValue || !BuildingId.HasValue || !FloorId.HasValue || !RoomId.HasValue) return [];

        GetDeviceListUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetDeviceListUseCase.Query(CityId!.Value, BuildingId!.Value, FloorId!.Value, RoomId!.Value, value), token);
        }
        catch (TaskCanceledException)
        {
            // Search was canceled, just return empty result
            return Enumerable.Empty<GetDeviceListUseCase.Response.Item>();
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching cities");
            Snackbar.Add("Не удалось получить список этажей из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
            return Enumerable.Empty<GetDeviceListUseCase.Response.Item>();
        }

        if (response.Result == GetDeviceListUseCase.Result.Success)
            return response.Items;

        if (response.Result == GetDeviceListUseCase.Result.ValidationError)
            Snackbar.Add("Ошибка валидации при получении списка городов", MudBlazor.Severity.Error);
        else
            Snackbar.Add("Не удалось получить список городов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);

        return Enumerable.Empty<GetDeviceListUseCase.Response.Item>();
    }
}
