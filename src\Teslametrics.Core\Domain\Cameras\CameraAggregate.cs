using Teslametrics.Core.Abstractions;
using Teslametrics.Core.Domain.Cameras.Events;

namespace Teslametrics.Core.Domain.Cameras;

public record OnvifSettingsValueObject(string Host, int Port, string Username, string Password);

public class CameraAggregate : IEntity
{
    public Guid Id { get; private set; }

    public Guid OrganizationId { get; private set; }

    public Guid FolderId { get; private set; }

    public Guid? QuotaId {get; private set;}

    public string Name { get; private set; }

    public TimeSpan TimeZone { get; private set; }

    public double? Latitude { get; private set; }

    public double? Longitude { get; private set; }

    public string ArchiveUri { get; private set; }

    public string ViewUri { get; private set; }

    public string PublicUri { get; private set; }

    public bool AutoStart { get; private set; }

    public bool IsBlocked { get; private set; }

    public bool OnvifEnabled { get; private set; }

    public OnvifSettingsValueObject? OnvifSettings { get; private set; }

    public static (CameraAggregate camera, List<object> events) Create(Guid id, Guid organizationId, Guid folderId, Guid quotaId, string name, TimeSpan timeZone, double? latitude, double? longitude, string archiveUri, string viewUri, string publicUri, bool onvifEnabled, OnvifSettingsValueObject? onvifSettings, bool autoStart)
    {
        return (new CameraAggregate(id, organizationId, folderId, quotaId, name, timeZone, latitude, longitude, archiveUri, viewUri, publicUri, onvifEnabled, onvifSettings, autoStart), [new CameraCreatedEvent(id, organizationId)]);
    }

    private CameraAggregate()
    {
        Name = string.Empty;
        ArchiveUri = string.Empty;
        ViewUri = string.Empty;
        PublicUri = string.Empty;
    }

    private CameraAggregate(Guid id, Guid organizationId, Guid folderId, Guid quotaId, string name, TimeSpan timeZone, double? latitude, double? longitude, string archiveUri, string viewUri, string publicUri, bool onvifEnabled, OnvifSettingsValueObject? onvifSettings, bool autoStart)
    {
        Id = id;
        OrganizationId = organizationId;
        FolderId = folderId;
        QuotaId = quotaId;
        Name = name;
        TimeZone = timeZone;
        Latitude = latitude;
        Longitude = longitude;
        ArchiveUri = archiveUri;
        ViewUri = viewUri;
        PublicUri = publicUri;
        OnvifEnabled = onvifEnabled;
        OnvifSettings = onvifSettings;
        AutoStart = autoStart;
    }

    public List<object> Update(Guid quotaId, string name, TimeSpan timeZone, double? latitude, double? longitude, string archiveUri, string viewUri, string publicUri, bool onvifEnabled, OnvifSettingsValueObject? onvifSettings, bool autoStart)
    {
        bool isUpdated = false;

        if (QuotaId != quotaId)
        {
            QuotaId = quotaId;
            IsBlocked = false;
            isUpdated = true;
        }

        if (Name != name)
        {
            Name = name;
            isUpdated = true;
        }

        if (TimeZone != timeZone)
        {
            TimeZone = timeZone;
            isUpdated = true;
        }

        if (Latitude != latitude)
        {
            Latitude = latitude;
            isUpdated = true;
        }

        if (Longitude != longitude)
        {
            Longitude = longitude;
            isUpdated = true;
        }

        if (ArchiveUri != archiveUri)
        {
            ArchiveUri = archiveUri;
            isUpdated = true;
        }

        if (ViewUri != viewUri)
        {
            ViewUri = viewUri;
            isUpdated = true;
        }

        if (PublicUri != publicUri)
        {
            PublicUri = publicUri;
            isUpdated = true;
        }

        if (OnvifEnabled != onvifEnabled)
        {
            OnvifEnabled = onvifEnabled;
            isUpdated = true;
        }

        if (OnvifSettings != onvifSettings)
        {
            OnvifSettings = onvifSettings;
            isUpdated = true;
        }

        if (AutoStart != autoStart)
        {
            AutoStart = autoStart;
            isUpdated = true;
        }

        return isUpdated ? [new CameraUpdatedEvent(Id, OrganizationId)] : [];
    }

    public List<object> ChangeFolder(Guid folderId)
    {
        if (FolderId != folderId)
        {
            FolderId = folderId;
            return [new CameraFolderChangedEvent(Id, OrganizationId)];
        }

        return [];
    }

    public List<object> Block()
    {
        if (!IsBlocked)
        {
            QuotaId = null;
            IsBlocked = true;

            return [new CameraBlockedEvent(Id, OrganizationId)];
        }

        return [];
    }
}