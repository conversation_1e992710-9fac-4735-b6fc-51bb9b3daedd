@using Teslametrics.Shared
@inherits MudFormComponent<Coordinates?, string?>

<div class="field-with-error-icon">
    <div class="d-flex align-center gap-2 flex-grow-1">
        <MudTextField @bind-Value="@_coordinatesText"
                      Placeholder="Нажмите на карту для выбора"
                      ReadOnly="true"
                      Variant="Variant.Outlined"
                      Class="flex-grow-1" />

        <MudIconButton Icon="@Icons.Material.Filled.Map"
                       Color="Color.Primary"
                       Variant="Variant.Outlined"
                       OnClick="OpenMapDialog"
                       title="Выбрать на карте" />

        @if (Value.HasValue)
        {
            <MudIconButton Icon="@Icons.Material.Filled.Clear"
                           Color="Color.Error"
                           Variant="Variant.Outlined"
                           OnClick="ClearCoordinates"
                           title="Очистить" />
        }
    </div>
    <div class="error-icon-container">
        @if (HasValidationError)
        {
            <MudTooltip Arrow="true"
                        Placement="Placement.Start"
                        Class="validation-error-tooltip">
                <ChildContent>
                    <MudIcon Icon="@Icons.Material.Filled.ErrorOutline"
                             Color="Color.Error"
                             Size="Size.Small"
                             Class="validation-error-icon" />
                </ChildContent>
                <TooltipContent>
                    @if (!string.IsNullOrEmpty(ValidationErrorText))
                    {
                        <div class="validation-error-content">
                            <div class="validation-error-message">@ValidationErrorText</div>
                        </div>
                    }
                </TooltipContent>
            </MudTooltip>
        }
    </div>
</div>

<MudDialog @bind-Visible="_isMapDialogVisible"
           Options="_dialogOptions">
    <DialogContent>
        <MudText Typo="Typo.h6"
                 Class="mb-4">Выберите координаты на карте</MudText>
        <YandexMaps @bind-Coordinates="@_selectedCoordinates"
                    Width="100%"
                    Height="400px"
                    ReadOnly="false"
                    SingleMarkerMode="true" />
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CancelMapSelection"
                   Variant="Variant.Text">Отменить</MudButton>
        <MudButton OnClick="ConfirmMapSelection"
                   Variant="Variant.Filled"
                   Color="Color.Primary">Выбрать</MudButton>
    </DialogActions>
</MudDialog>
