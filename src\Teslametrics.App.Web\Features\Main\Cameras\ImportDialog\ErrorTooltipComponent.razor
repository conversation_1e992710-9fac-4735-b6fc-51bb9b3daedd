﻿<div class="error-icon-container">
    @if (HasError)
    {
        <MudTooltip Arrow="true"
                    Placement="Placement.Start"
                    Class="validation-error-tooltip"
                    RootClass="error-tooltip">
            <ChildContent>
                <MudIcon Icon="@Icons.Material.Filled.ErrorOutline"
                         Color="Color.Error"
                         Size="Size.Small"
                         Class="validation-error-icon" />
            </ChildContent>
            <TooltipContent>
                @if (ErrorMessages?.Any() ?? false)
                {
                    <div class="validation-error-content">
                        @foreach (var message in ErrorMessages)
                        {
                            <div class="validation-error-message">@message</div>
                        }
                    </div>
                }
            </TooltipContent>
        </MudTooltip>
    }
</div>

@code {
    [Parameter]
    public bool HasError { get; set; }

    [Parameter]
    public IEnumerable<string> ErrorMessages { get; set; } = [];
}
