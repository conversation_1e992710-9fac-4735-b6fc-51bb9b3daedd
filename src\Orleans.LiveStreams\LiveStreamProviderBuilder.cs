using Microsoft.Extensions.Configuration;
using Orleans;
using Orleans.Hosting;
using Orleans.Providers;

[assembly: Register<PERSON><PERSON>ider("Live", "Streaming", "Client", typeof(LiveStreamProviderBuilder))]
[assembly: RegisterProvider("Live", "Streaming", "Silo", typeof(LiveStreamProviderBuilder))]
namespace Orleans.Providers;

internal sealed class LiveStreamProviderBuilder : IProviderBuilder<ISiloBuilder>, IProviderBuilder<IClientBuilder>
{
    public void Configure(ISiloBuilder builder, string name, IConfigurationSection configurationSection)
    {
        builder.AddLiveStreams(name);
    }

    public void Configure(IClientBuilder builder, string name, IConfigurationSection configurationSection)
    {
        builder.AddLiveStreams(name);
    }
}
