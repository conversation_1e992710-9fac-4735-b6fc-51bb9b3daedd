using System.ComponentModel.DataAnnotations;

namespace Teslametrics.Shared;

public enum IncidentType
{
    [Display(Name = "Температура")]
    Temperature,

    [Display(Name = "Дверь")]
    Door,

    [Display(Name = "Влажность")]
    <PERSON><PERSON><PERSON><PERSON>,

    [<PERSON><PERSON><PERSON>(Name = "Протечка")]
    <PERSON><PERSON>,

    [<PERSON><PERSON><PERSON>(Name = "Питание")]
    Power,

    [Display(Name = "Wirenboard отключен")]
    WirenboardDisconnected
}