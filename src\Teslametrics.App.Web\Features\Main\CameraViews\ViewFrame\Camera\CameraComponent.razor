﻿@using Teslametrics.App.Web.Features.Main.CameraViews.ViewFrame.Camera.Preview
@using Teslametrics.MediaServer.Orleans.Camera
@inherits InteractiveBaseComponent
<div class="d_contents">
	<MudCard Class="mud-height-full mud-width-full camera_card"
			 Elevation="0">
		<MudCardHeader Class="pa-1">
			@if (CameraId is null || CameraId == Guid.Empty)
			{
				<MudText Typo="Typo.h5"
						 class="camera_name mud-width-full">
					Пустая ячейка
				</MudText>
			}
			@if (_viewResponse is null && IsLoading)
			{
				<MudSkeleton />
				<MudSpacer />
				<MudSkeleton Width="30px"
							 Height="30px" />
			}
			@if (_viewResponse is not null)
			{
				<MudText Typo="Typo.h5"
						 class="camera_name mud-width-full">
					@_viewResponse.Name
				</MudText>
				@if (_viewResponse.IsBlocked)
				{
					<MudChip T="string"
							 Variant="Variant.Outlined"
							 Icon="@TeslaIcons.Devices.Camera"
							 Size="Size.Small"
							 Class="pl-3"
							 Color="Color.Error">Lck</MudChip>
				}
				else
				{
					@if (_cameraStatus == CameraStatus.Connecting)
					{
						<MudChip T="string"
								 Variant="Variant.Outlined"
								 Icon="@TeslaIcons.Devices.Camera"
								 Size="Size.Small"
								 Class="pl-3"
								 Color="Color.Info">Off</MudChip>
					}
					@if (_cameraStatus == CameraStatus.Problem)
					{
						<MudChip T="string"
								 Variant="Variant.Outlined"
								 Icon="@TeslaIcons.Devices.Camera"
								 Size="Size.Small"
								 Class="pl-3"
								 Color="Color.Error">Err</MudChip>
					}
					@if (_cameraStatus == CameraStatus.Running)
					{
						<MudChip T="string"
								 Variant="Variant.Outlined"
								 Icon="@TeslaIcons.Devices.Camera"
								 Size="Size.Small"
								 Class="pl-3"
								 Color="Color.Success">On</MudChip>
					}
					@if (_cameraStatus == CameraStatus.Stopped)
					{
						<MudChip T="string"
								 Variant="Variant.Outlined"
								 Icon="@TeslaIcons.Devices.Camera"
								 Size="Size.Small"
								 Class="pl-3"
								 Color="Color.Info">Off</MudChip>
					}
				}
			}
		</MudCardHeader>
		<div class="image_container pa-1">
			<div class="d-flex mud-width-full flex-column justify-center align-center mud-height-full bg3">
				@if (CameraId is null || CameraId == Guid.Empty)
				{
					<div class="empty_cell"></div>
				}
				@if (_viewResponse is null && IsLoading)
				{
					<MudSkeleton SkeletonType="SkeletonType.Rectangle"
								 Width="100%"
								 Height="100%" />
				}
				@if (_viewResponse is not null)
				{
					@switch (_cameraStatus)
					{
						case CameraStatus.Problem:
						case CameraStatus.Running:
							<CameraPreview CameraId="@_viewResponse.Id"
										   CameraStreamId="@_viewResponse.CameraStreamId" />
							break;
						case CameraStatus.Stopped:
							<MudIcon Icon="@Icons.Material.Filled.Block"
									 Color="Color.Warning" />
							<div>Камера отключена</div>
							break;
						case CameraStatus.Connecting:
							<MudProgressCircular Color="Color.Info"
												 Style="height:70px;width:70px;"
												 Indeterminate="true" />
							<div>Камера подключается</div>
							break;
						default:
							break;
					}
				}
			</div>
		</div>
	</MudCard>
</div>