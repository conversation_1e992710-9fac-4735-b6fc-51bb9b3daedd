using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using MudBlazor;

namespace Teslametrics.App.Web.Components.MSEPlayer;

public partial class VolumeComponent
{
    /// <summary>
    ///     Shows the volume control even if the player is muted.
    ///     This is useful for players that are not autoplaying and therefore
    ///     don't have a default volume.
    /// </summary>
    [Parameter]
    public bool ShowForced { get; set; } = false;

    [Parameter]
    [EditorRequired]
    public IMsePlayer Player { get; set; } = null!;

    private const float _lowVolumeThreshold = 0.50f;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Установка начальной громкости
            await UpdatePlayerVolumeAsync();
        }
        await base.OnAfterRenderAsync(firstRender);
    }

    /// <summary>
    /// Обработчик изменения громкости через слайдер
    /// </summary>
    private Task OnVolumeChangedAsync(float value)
    {
        try
        {
            return Player.SetVolumeAsync(value);
        }
        catch (JSDisconnectedException)
        {
            // Игнорируем ошибку отключения JS
        }
        catch (Exception ex)
        {
            // Логирование ошибки
            Console.Error.WriteLine($"Ошибка при изменении громкости: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Обработчик переключения режима mute
    /// </summary>
    private Task OnMuteToggle()
    {
        try
        {
            return Player.SetVolumeAsync(0);
        }
        catch (JSDisconnectedException)
        {
            // Игнорируем ошибку отключения JS
        }
        catch (Exception ex)
        {
            // Логирование ошибки
            Console.Error.WriteLine($"Ошибка при изменении громкости: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Возвращает иконку в зависимости от текущей громкости
    /// </summary>
    private string GetVolumeIcon()
    {
        if (Player.Muted) return Icons.Material.Filled.VolumeOff;

        if (Player.Volume == 0)
            return Icons.Material.Filled.VolumeMute;
        else if (Player.Volume < _lowVolumeThreshold)
            return Icons.Material.Filled.VolumeDown;
        else
            return Icons.Material.Filled.VolumeUp;
    }

    /// <summary>
    /// Асинхронно обновляет громкость плеера
    /// </summary>
    private async Task UpdatePlayerVolumeAsync()
    {
        try
        {
            // Если плеер не инициализирован, выходим
            if (Player == null) return;
            // Устанавливаем громкость в плеере
            await Player.SetVolumeAsync(Player.Volume == 0 ? 0 : 0.50f);
        }
        catch (Exception ex)
        {
            // Логирование ошибки
            Console.Error.WriteLine($"Ошибка при установке громкости: {ex.Message}");
        }
    }
}
