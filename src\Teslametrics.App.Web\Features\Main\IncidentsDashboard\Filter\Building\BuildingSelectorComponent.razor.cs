using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.Filter.Building;

public partial class BuildingSelectorComponent
{
    private GetBuildingListUseCase.Response.Item? _selected;

    [Parameter]
    public Guid? City { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }

    [Parameter]
    public EventCallback<Guid?> BuildingIdChanged { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        if (_selected?.Id != BuildingId)
        {
            _selected = null;
            await GetDataAsync();
        }
        await base.OnParametersSetAsync();
    }

    private async Task GetDataAsync()
    {
        if (!City.HasValue || !BuildingId.HasValue) return;

        GetBuildingUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetBuildingUseCase.Query(City.Value, BuildingId.Value));
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching cities");
            Snackbar.Add("Не удалось получить здание из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case GetBuildingUseCase.Result.Success:
                _selected = new(City.Value, response.Name);
                break;
            case GetBuildingUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении здания", MudBlazor.Severity.Error);
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(BuildingSelectorComponent), nameof(GetBuildingUseCase));
                break;
            case GetBuildingUseCase.Result.CityNotFound:
                Snackbar.Add("Город не найден", MudBlazor.Severity.Error);

                BuildingId = null;
                if (BuildingIdChanged.HasDelegate)
                    await BuildingIdChanged.InvokeAsync(BuildingId);

                break;

            case GetBuildingUseCase.Result.BuildingNotFound:
                Snackbar.Add("Здание не найдено", MudBlazor.Severity.Error);

                BuildingId = null;
                if (BuildingIdChanged.HasDelegate)
                    await BuildingIdChanged.InvokeAsync(BuildingId);

                break;

            case GetBuildingUseCase.Result.Unknown:
                Snackbar.Add("Не удалось получить здание из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(BuildingSelectorComponent), nameof(GetBuildingUseCase));
                break;
        }
    }

    private async Task CityValueChanged(GetBuildingListUseCase.Response.Item? item)
    {
        _selected = item;
        await BuildingIdChanged.InvokeAsync(item?.Id);
    }

    private async Task<IEnumerable<GetBuildingListUseCase.Response.Item>> SearchCityAsync(string value, CancellationToken token)
    {
        if (City is null) return [];

        GetBuildingListUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetBuildingListUseCase.Query(City.Value, value), token);
        }
        catch (TaskCanceledException)
        {
            // Search was canceled, just return empty result
            return [];
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching buildings");
            Snackbar.Add("Не удалось получить список адресов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
            return [];
        }

        if (response.Result == GetBuildingListUseCase.Result.Success)
            return response.Items;

        if (response.Result == GetBuildingListUseCase.Result.ValidationError)
            Snackbar.Add("Ошибка валидации при получении списка адресов", MudBlazor.Severity.Error);
        else
            Snackbar.Add("Не удалось получить список адресов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);

        return [];
    }
}
