﻿using Teslametrics.Core.Abstractions;

namespace Teslametrics.Core.Domain.Folders;

public interface IFolderRepository : IRepository<FolderAggregate>
{
    public Task<FolderAggregate?> FindByNameAsync(string folderName, Guid organizationId, CancellationToken cancellationToken = default);

    public Task<bool> IsFolderExistsAsync(Guid folderId, CancellationToken cancellationToken = default);

    public Task<bool> IsFolderNameExistsAsync(string name, Guid organizationId, CancellationToken cancellationToken = default);
}