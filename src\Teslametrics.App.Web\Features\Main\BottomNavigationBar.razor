﻿@inherits InteractiveBaseComponent
<div class="d_contents">
    <MudStack Row="true"
              Class="bottom_navbar"
              Justify="Justify.SpaceEvenly"
              AlignItems="AlignItems.Center">
        <MudIconButton Icon="@Icons.Material.TwoTone.MoreVert"
                       Variant="Variant.Text"
                       Color="Color.Inherit"
                       OnClick="@(() => NavBarOpenedChanged.InvokeAsync(!NavBarOpened))" />
        <MudIconButton Icon="@Icons.Material.Filled.Home"
                       Href="/"
                       Variant="Variant.Text"
                       Color="Color.Inherit" />
        <MudIconButton Href="/incidents"
                       Icon="@TeslaIcons.State.Warning"
                       Variant="Variant.Text" />
        <MudIconButton Href="/incidents-dashboard"
                       Icon="@TeslaIcons.PageIcons.Analytics"
                       Variant="Variant.Text" />
        <MudIconButton Icon="@Icons.Material.Filled.Notifications"
                       Href="/notifications"
                       Variant="Variant.Text"
                       Color="Color.Inherit" />
    </MudStack>
</div>

@code {
    [Parameter]
    public bool NavBarOpened { get; set; }

    [Parameter]
    public EventCallback<bool> NavBarOpenedChanged { get; set; }
}